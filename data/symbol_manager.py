#!/usr/bin/env python3
"""
Enhanced Symbol Manager for RapidTrader

This module provides comprehensive symbol management including:
- Daily symbol downloads from multiple sources
- Symbol caching and validation
- Master contract management
- Integration with multiple brokers (Fyers, DhanHQ, etc.)
- OpenAlgo-style symbol management
"""

import os
import sys
import json
import logging
import requests
import pandas as pd
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("symbol_manager")

class SymbolManager:
    """Enhanced Symbol Manager for RapidTrader"""

    def __init__(self, data_dir: str = "data/symbols"):
        """Initialize the Symbol Manager"""
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # Symbol files
        self.nse_symbols_file = self.data_dir / "nse_symbols.json"
        self.bse_symbols_file = self.data_dir / "bse_symbols.json"
        self.fyers_symbols_file = self.data_dir / "fyers_symbols.json"
        self.master_contract_file = self.data_dir / "master_contract.json"
        self.symbol_mapping_file = self.data_dir / "symbol_mapping.json"

        # Cache
        self._symbol_cache = {}
        self._last_update = {}

        # Configuration
        self.update_interval_hours = 24  # Update daily
        self.max_retries = 3
        self.timeout = 30

        logger.info("Symbol Manager initialized")

    def download_nse_symbols(self) -> List[Dict[str, Any]]:
        """Download NSE symbols from official NSE website"""
        try:
            # Try multiple NSE URLs
            urls = [
                "https://www1.nseindia.com/content/equities/EQUITY_L.csv",
                "https://archives.nseindia.com/content/equities/EQUITY_L.csv"
            ]

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }

            symbols = []

            for url in urls:
                try:
                    logger.info(f"Downloading NSE symbols from {url}...")
                    response = requests.get(url, headers=headers, timeout=self.timeout)
                    response.raise_for_status()

                    # Parse CSV with error handling
                    from io import StringIO

                    # Try to read with different parameters
                    try:
                        df = pd.read_csv(StringIO(response.text), on_bad_lines='skip')
                    except:
                        # If that fails, try with different encoding
                        df = pd.read_csv(StringIO(response.text), encoding='latin-1', on_bad_lines='skip')

                    # Check if we have the expected columns
                    required_columns = ["SYMBOL", "SERIES"]
                    if not all(col in df.columns for col in required_columns):
                        logger.warning(f"Missing required columns in {url}")
                        continue

                    # Extract symbols
                    for _, row in df.iterrows():
                        try:
                            symbol_data = {
                                "symbol": str(row.get("SYMBOL", "")).strip(),
                                "name": str(row.get("NAME OF COMPANY", row.get("NAME", ""))).strip(),
                                "series": str(row.get("SERIES", "")).strip(),
                                "exchange": "NSE",
                                "segment": "EQ" if str(row.get("SERIES", "")) == "EQ" else str(row.get("SERIES", "")),
                                "source": "NSE_OFFICIAL"
                            }

                            # Add optional fields if available
                            optional_fields = {
                                "date_of_listing": "DATE OF LISTING",
                                "paid_up_value": "PAID UP VALUE",
                                "market_lot": "MARKET LOT",
                                "isin_number": "ISIN NUMBER",
                                "face_value": "FACE VALUE"
                            }

                            for field, column in optional_fields.items():
                                if column in df.columns:
                                    symbol_data[field] = str(row.get(column, "")).strip()

                            # Only add if symbol is valid
                            if symbol_data["symbol"] and len(symbol_data["symbol"]) > 0:
                                symbols.append(symbol_data)

                        except Exception as e:
                            logger.debug(f"Error processing row: {e}")
                            continue

                    if symbols:
                        logger.info(f"Downloaded {len(symbols)} NSE symbols from {url}")
                        return symbols

                except Exception as e:
                    logger.warning(f"Failed to download from {url}: {e}")
                    continue

            # If official sources fail, use fallback symbol list
            if not symbols:
                logger.warning("Official NSE download failed, using fallback symbol list")
                symbols = self._get_fallback_nse_symbols()

            return symbols

        except Exception as e:
            logger.error(f"Error downloading NSE symbols: {e}")
            return self._get_fallback_nse_symbols()

    def _get_fallback_nse_symbols(self) -> List[Dict[str, Any]]:
        """Get fallback NSE symbols list"""
        # Popular NSE symbols as fallback
        fallback_symbols = [
            "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "INFY", "HINDUNILVR", "ITC",
            "SBIN", "BHARTIARTL", "KOTAKBANK", "LT", "ASIANPAINT", "AXISBANK", "MARUTI",
            "NESTLEIND", "HCLTECH", "WIPRO", "ULTRACEMCO", "BAJFINANCE", "TITAN",
            "SUNPHARMA", "ONGC", "NTPC", "POWERGRID", "COALINDIA", "TATAMOTORS",
            "BAJAJFINSV", "HDFCLIFE", "TECHM", "GRASIM", "ADANIPORTS", "JSWSTEEL",
            "INDUSINDBK", "TATASTEEL", "CIPLA", "DRREDDY", "EICHERMOT", "BRITANNIA",
            "DIVISLAB", "APOLLOHOSP", "BPCL", "HEROMOTOCO", "SHREECEM", "PIDILITIND",
            "BAJAJ-AUTO", "SBILIFE", "ADANIENT", "TATACONSUM", "IOC", "GODREJCP"
        ]

        symbols = []
        for symbol in fallback_symbols:
            symbol_data = {
                "symbol": symbol,
                "name": f"{symbol} Limited",
                "series": "EQ",
                "exchange": "NSE",
                "segment": "EQ",
                "source": "FALLBACK"
            }
            symbols.append(symbol_data)

        logger.info(f"Using fallback list with {len(symbols)} NSE symbols")
        return symbols

    def download_bse_symbols(self) -> List[Dict[str, Any]]:
        """Download BSE symbols"""
        try:
            # BSE equity list URL (alternative approach)
            # Note: BSE doesn't provide a direct CSV like NSE
            # This is a placeholder for BSE symbol download

            logger.info("BSE symbol download not implemented yet")
            return []

        except Exception as e:
            logger.error(f"Error downloading BSE symbols: {e}")
            return []

    def download_fyers_symbols(self) -> List[Dict[str, Any]]:
        """Download Fyers symbol master"""
        try:
            # Fyers provides symbol master files
            # This would require Fyers API integration

            logger.info("Fyers symbol download requires API integration")
            return []

        except Exception as e:
            logger.error(f"Error downloading Fyers symbols: {e}")
            return []

    def save_symbols(self, symbols: List[Dict[str, Any]], exchange: str) -> bool:
        """Save symbols to file"""
        try:
            if exchange.upper() == "NSE":
                file_path = self.nse_symbols_file
            elif exchange.upper() == "BSE":
                file_path = self.bse_symbols_file
            elif exchange.upper() == "FYERS":
                file_path = self.fyers_symbols_file
            else:
                logger.error(f"Unknown exchange: {exchange}")
                return False

            # Create backup if file exists
            if file_path.exists():
                backup_path = file_path.with_suffix(f".bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                file_path.rename(backup_path)
                logger.info(f"Created backup: {backup_path}")

            # Save symbols
            data = {
                "exchange": exchange.upper(),
                "symbols": symbols,
                "count": len(symbols),
                "updated_at": datetime.now().isoformat(),
                "version": "1.0"
            }

            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Saved {len(symbols)} {exchange} symbols to {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving {exchange} symbols: {e}")
            return False

    def load_symbols(self, exchange: str) -> List[Dict[str, Any]]:
        """Load symbols from file"""
        try:
            if exchange.upper() == "NSE":
                file_path = self.nse_symbols_file
            elif exchange.upper() == "BSE":
                file_path = self.bse_symbols_file
            elif exchange.upper() == "FYERS":
                file_path = self.fyers_symbols_file
            else:
                logger.error(f"Unknown exchange: {exchange}")
                return []

            if not file_path.exists():
                logger.warning(f"Symbol file not found: {file_path}")
                return []

            with open(file_path, 'r') as f:
                data = json.load(f)

            symbols = data.get("symbols", [])
            logger.info(f"Loaded {len(symbols)} {exchange} symbols")
            return symbols

        except Exception as e:
            logger.error(f"Error loading {exchange} symbols: {e}")
            return []

    def update_symbols(self, exchange: str = None) -> Dict[str, int]:
        """Update symbols for specified exchange or all exchanges"""
        results = {}

        exchanges = [exchange] if exchange else ["NSE", "BSE", "FYERS"]

        for exch in exchanges:
            try:
                if exch.upper() == "NSE":
                    symbols = self.download_nse_symbols()
                elif exch.upper() == "BSE":
                    symbols = self.download_bse_symbols()
                elif exch.upper() == "FYERS":
                    symbols = self.download_fyers_symbols()
                else:
                    continue

                if symbols:
                    if self.save_symbols(symbols, exch):
                        results[exch] = len(symbols)
                        self._last_update[exch] = datetime.now()
                    else:
                        results[exch] = 0
                else:
                    results[exch] = 0

            except Exception as e:
                logger.error(f"Error updating {exch} symbols: {e}")
                results[exch] = 0

        return results

    def create_master_contract(self) -> bool:
        """Create master contract file combining all exchanges"""
        try:
            master_contract = {
                "created_at": datetime.now().isoformat(),
                "version": "1.0",
                "exchanges": {},
                "total_symbols": 0
            }

            total_symbols = 0

            # Load symbols from all exchanges
            for exchange in ["NSE", "BSE", "FYERS"]:
                symbols = self.load_symbols(exchange)
                if symbols:
                    master_contract["exchanges"][exchange] = {
                        "count": len(symbols),
                        "symbols": symbols,
                        "last_updated": self._last_update.get(exchange, datetime.now()).isoformat()
                    }
                    total_symbols += len(symbols)

            master_contract["total_symbols"] = total_symbols

            # Save master contract
            with open(self.master_contract_file, 'w') as f:
                json.dump(master_contract, f, indent=2)

            logger.info(f"Created master contract with {total_symbols} symbols")
            return True

        except Exception as e:
            logger.error(f"Error creating master contract: {e}")
            return False

    def get_symbol_info(self, symbol: str, exchange: str = None) -> Optional[Dict[str, Any]]:
        """Get information for a specific symbol"""
        try:
            exchanges = [exchange] if exchange else ["NSE", "BSE", "FYERS"]

            for exch in exchanges:
                symbols = self.load_symbols(exch)
                for symbol_data in symbols:
                    if symbol_data.get("symbol", "").upper() == symbol.upper():
                        return symbol_data

            return None

        except Exception as e:
            logger.error(f"Error getting symbol info for {symbol}: {e}")
            return None

    def search_symbols(self, query: str, exchange: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Search symbols by name or symbol"""
        try:
            results = []
            query = query.upper()

            exchanges = [exchange] if exchange else ["NSE", "BSE", "FYERS"]

            for exch in exchanges:
                symbols = self.load_symbols(exch)
                for symbol_data in symbols:
                    # Handle different symbol data formats
                    if isinstance(symbol_data, dict):
                        symbol = symbol_data.get("symbol", "").upper()
                        name = symbol_data.get("name", "").upper()
                    elif isinstance(symbol_data, str):
                        # Handle simple string symbols (like BSE format)
                        symbol = symbol_data.upper()
                        name = ""
                        # Convert to dict format
                        symbol_data = {
                            "symbol": symbol_data,
                            "name": f"BSE {symbol_data}",
                            "exchange": exch,
                            "segment": "EQ"
                        }
                    else:
                        continue

                    if query in symbol or query in name:
                        results.append(symbol_data)

                        if len(results) >= limit:
                            break

                if len(results) >= limit:
                    break

            return results

        except Exception as e:
            logger.error(f"Error searching symbols: {e}")
            return []

    def is_update_needed(self, exchange: str) -> bool:
        """Check if symbol update is needed for an exchange"""
        try:
            last_update = self._last_update.get(exchange)
            if not last_update:
                return True

            time_diff = datetime.now() - last_update
            return time_diff.total_seconds() > (self.update_interval_hours * 3600)

        except Exception as e:
            logger.error(f"Error checking update status: {e}")
            return True

    def schedule_daily_updates(self):
        """Schedule daily symbol updates"""
        try:
            # Schedule updates at 6 AM daily
            schedule.every().day.at("06:00").do(self.daily_update_job)

            logger.info("Scheduled daily symbol updates at 6:00 AM")

            # Run scheduler in background
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute

        except KeyboardInterrupt:
            logger.info("Symbol update scheduler stopped")
        except Exception as e:
            logger.error(f"Error in scheduler: {e}")

    def daily_update_job(self):
        """Daily update job"""
        try:
            logger.info("Starting daily symbol update...")

            results = self.update_symbols()

            # Create master contract
            self.create_master_contract()

            # Log results
            total_updated = sum(results.values())
            logger.info(f"Daily update completed: {total_updated} symbols updated")

            for exchange, count in results.items():
                logger.info(f"{exchange}: {count} symbols")

        except Exception as e:
            logger.error(f"Error in daily update job: {e}")


def main():
    """Main function for CLI usage"""
    import argparse

    parser = argparse.ArgumentParser(description="RapidTrader Symbol Manager")
    parser.add_argument("--update", choices=["NSE", "BSE", "FYERS", "ALL"],
                       help="Update symbols for specific exchange")
    parser.add_argument("--search", help="Search symbols")
    parser.add_argument("--info", help="Get symbol information")
    parser.add_argument("--exchange", choices=["NSE", "BSE", "FYERS"],
                       help="Specify exchange")
    parser.add_argument("--schedule", action="store_true",
                       help="Start daily update scheduler")
    parser.add_argument("--master-contract", action="store_true",
                       help="Create master contract file")

    args = parser.parse_args()

    manager = SymbolManager()

    if args.update:
        exchange = None if args.update == "ALL" else args.update
        results = manager.update_symbols(exchange)
        print(f"Update results: {results}")

    elif args.search:
        results = manager.search_symbols(args.search, args.exchange)
        print(f"Found {len(results)} symbols:")
        for symbol in results[:10]:  # Show first 10
            print(f"  {symbol.get('symbol')} - {symbol.get('name')} ({symbol.get('exchange')})")

    elif args.info:
        info = manager.get_symbol_info(args.info, args.exchange)
        if info:
            print(json.dumps(info, indent=2))
        else:
            print(f"Symbol {args.info} not found")

    elif args.master_contract:
        if manager.create_master_contract():
            print("Master contract created successfully")
        else:
            print("Failed to create master contract")

    elif args.schedule:
        print("Starting daily update scheduler...")
        manager.schedule_daily_updates()

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
