"""
Data Fetcher for RapidTrader

This module provides a unified interface for fetching data from various sources.
"""

import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
import pandas as pd

from data.base_fetcher import BaseDataFetcher
from data.yfinance_fetcher import YFinanceFetcher
from data.data_manager import DataManager

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("fetcher")

class Fetcher:
    """
    Unified interface for fetching data from various sources.

    This class provides a simple interface for fetching data, hiding the complexity
    of the underlying data sources and caching mechanisms.
    """

    def __init__(self,
                data_dir: str = "userdata/historical_data",
                default_source: str = "yfinance"):
        """
        Initialize the fetcher.

        Args:
            data_dir: Directory to store data
            default_source: Default data source
        """
        self.data_dir = data_dir
        self.default_source = default_source

        # Create data directory if it doesn't exist
        os.makedirs(data_dir, exist_ok=True)

        # Initialize data sources
        self.sources = {}
        self.managers = {}

        # Initialize YFinance data source
        self.sources["yfinance"] = YFinanceFetcher(cache_dir=os.path.join(data_dir, "yfinance"))
        logger.info("Using YFinance fetcher")

        # Initialize the data manager
        self.managers["yfinance"] = DataManager(
            data_fetcher=self.sources["yfinance"],
            db_path=os.path.join(data_dir, "yfinance", "rapidtrader.db"),
            csv_dir=os.path.join(data_dir, "yfinance")
        )

    def get_ohlcv(self,
                 symbol: str,
                 timeframe: str = "1d",
                 start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None,
                 exchange: Optional[str] = None,
                 source: Optional[str] = None,
                 force_refresh: bool = False) -> pd.DataFrame:
        """
        Get OHLCV data for a symbol.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from
            source: Data source to use
            force_refresh: Whether to force a refresh from the data source

        Returns:
            DataFrame with OHLCV data
        """
        # Use default source if not specified
        source = source or self.default_source

        # Check if the source exists
        if source not in self.managers:
            logger.error(f"Data source {source} not found")
            return pd.DataFrame()

        # Get data from the manager
        return self.managers[source].get_ohlcv(
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            exchange=exchange,
            force_refresh=force_refresh
        )

    def get_multiple_ohlcv(self,
                          symbols: List[str],
                          timeframe: str = "1d",
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          exchange: Optional[str] = None,
                          source: Optional[str] = None,
                          force_refresh: bool = False) -> Dict[str, pd.DataFrame]:
        """
        Get OHLCV data for multiple symbols.

        Args:
            symbols: List of trading symbols
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from
            source: Data source to use
            force_refresh: Whether to force a refresh from the data source

        Returns:
            Dictionary mapping symbols to DataFrames with OHLCV data
        """
        # Use default source if not specified
        source = source or self.default_source

        # Check if the source exists
        if source not in self.managers:
            logger.error(f"Data source {source} not found")
            return {symbol: pd.DataFrame() for symbol in symbols}

        # Get data from the manager
        return self.managers[source].get_multiple_ohlcv(
            symbols=symbols,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            exchange=exchange,
            force_refresh=force_refresh
        )

    def get_available_symbols(self,
                             exchange: Optional[str] = None,
                             source: Optional[str] = None) -> List[str]:
        """
        Get the list of available symbols.

        Args:
            exchange: Exchange name
            source: Data source to use

        Returns:
            List of available symbols
        """
        # Use default source if not specified
        source = source or self.default_source

        # Check if the source exists
        if source not in self.managers:
            logger.error(f"Data source {source} not found")
            return []

        # Get symbols from the manager
        return self.managers[source].get_available_symbols(exchange)

    def get_all_symbols(self,
                       exchange: str,
                       source: Optional[str] = None) -> List[str]:
        """
        Get all available symbols for an exchange.

        Args:
            exchange: Exchange name
            source: Data source to use

        Returns:
            List of available symbols
        """
        # Try to use the symbol_manager first
        try:
            # Import the symbol_manager
            import importlib.util

            # Get the path to the symbol_manager.py file
            current_dir = os.path.dirname(os.path.abspath(__file__))
            symbol_manager_path = os.path.join(current_dir, "symbol_manager.py")

            # Check if the file exists
            if os.path.exists(symbol_manager_path):
                # Import the module
                spec = importlib.util.spec_from_file_location("symbol_manager", symbol_manager_path)
                if spec is not None:
                    symbol_manager = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(symbol_manager)

                    # Get symbols from the symbol_manager
                    symbols = symbol_manager.get_symbols(exchange)
                    if symbols:
                        logger.info(f"Using symbols from symbol_manager: {len(symbols)} symbols found for {exchange}")
                        return symbols
        except Exception as e:
            logger.warning(f"Error using symbol_manager: {e}")

        # If we get here, fall back to the data source
        # Use default source if not specified
        source = source or self.default_source

        # Check if the source exists
        if source not in self.sources:
            logger.error(f"Data source {source} not found")
            return []

        # Get symbols from the source
        return self.sources[source].get_all_symbols(exchange)

    def get_available_timeframes(self, source: Optional[str] = None) -> List[str]:
        """
        Get the list of available timeframes.

        Args:
            source: Data source to use

        Returns:
            List of available timeframes
        """
        # Use default source if not specified
        source = source or self.default_source

        # Check if the source exists
        if source not in self.managers:
            logger.error(f"Data source {source} not found")
            return []

        # Get timeframes from the manager
        return self.managers[source].get_available_timeframes()

    def get_exchange_info(self,
                         exchange: str,
                         source: Optional[str] = None) -> Dict[str, Any]:
        """
        Get information about an exchange.

        Args:
            exchange: Exchange name
            source: Data source to use

        Returns:
            Dictionary with exchange information
        """
        # Use default source if not specified
        source = source or self.default_source

        # Check if the source exists
        if source not in self.managers:
            logger.error(f"Data source {source} not found")
            return {}

        # Get exchange info from the manager
        return self.managers[source].get_exchange_info(exchange)

    def get_available_sources(self) -> List[str]:
        """
        Get the list of available data sources.

        Returns:
            List of available data sources
        """
        return list(self.sources.keys())

    def add_source(self,
                  name: str,
                  fetcher: BaseDataFetcher,
                  db_path: Optional[str] = None,
                  csv_dir: Optional[str] = None) -> None:
        """
        Add a new data source.

        Args:
            name: Name of the data source
            fetcher: Data fetcher instance
            db_path: Path to the SQLite database
            csv_dir: Directory for CSV files
        """
        self.sources[name] = fetcher

        # Use default paths if not specified
        if db_path is None:
            db_path = os.path.join(self.data_dir, name, "rapidtrader.db")

        if csv_dir is None:
            csv_dir = os.path.join(self.data_dir, name)

        # Create the data manager
        self.managers[name] = DataManager(
            data_fetcher=fetcher,
            db_path=db_path,
            csv_dir=csv_dir
        )

        logger.info(f"Added data source: {name}")

    def remove_source(self, name: str) -> None:
        """
        Remove a data source.

        Args:
            name: Name of the data source
        """
        if name in self.sources:
            del self.sources[name]

        if name in self.managers:
            del self.managers[name]

        logger.info(f"Removed data source: {name}")

    def set_default_source(self, name: str) -> None:
        """
        Set the default data source.

        Args:
            name: Name of the data source
        """
        if name in self.sources:
            self.default_source = name
            logger.info(f"Set default data source to: {name}")
        else:
            logger.error(f"Data source {name} not found")