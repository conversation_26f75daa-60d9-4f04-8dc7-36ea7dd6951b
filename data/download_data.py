#!/usr/bin/env python3
"""
Download Data from YFinance

This script downloads data from YFinance for NSE and BSE symbols.
It reads symbols from the JSON files in the data/symbols directory.
"""

import os
import json
import logging
import argparse
from datetime import datetime, timedelta
import pandas as pd
import yfinance as yf
import concurrent.futures
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("download_data")

# Constants
SYMBOLS_DIR = os.path.join("data", "symbols")
NSE_SYMBOLS_FILE = os.path.join(SYMBOLS_DIR, "nse_symbols.json")
BSE_SYMBOLS_FILE = os.path.join(SYMBOLS_DIR, "bse_symbols.json")
DATA_DIR = os.path.join("userdata", "historical_data", "yfinance")

def load_symbols(exchange):
    """
    Load symbols from the JSON file.

    Args:
        exchange: Exchange code (nse, bse)

    Returns:
        List of symbols
    """
    if exchange.lower() == "nse":
        file_path = NSE_SYMBOLS_FILE
    elif exchange.lower() == "bse":
        file_path = BSE_SYMBOLS_FILE
    else:
        logger.error(f"Unsupported exchange: {exchange}")
        return []

    try:
        if os.path.exists(file_path):
            with open(file_path, "r") as f:
                data = json.load(f)

            if "symbols" in data:
                logger.info(f"Loaded {len(data['symbols'])} {exchange.upper()} symbols from {file_path}")
                return data["symbols"]

        logger.warning(f"Symbol file not found: {file_path}")
        return []
    except Exception as e:
        logger.error(f"Loading failed: {e}")
        return []

def get_yfinance_symbol(symbol, exchange):
    """
    Convert a symbol to a yfinance symbol.

    Args:
        symbol: Symbol
        exchange: Exchange code (nse, bse)

    Returns:
        yfinance symbol
    """
    if exchange.lower() == "nse":
        return f"{symbol}.NS"
    elif exchange.lower() == "bse":
        return f"{symbol}.BO"
    else:
        return symbol

def validate_timeframe(timeframe):
    """
    Validate and normalize the timeframe.

    Args:
        timeframe: Timeframe string

    Returns:
        Normalized timeframe string or None if invalid
    """
    # Valid yfinance timeframes
    valid_timeframes = {
        # Minutes
        "1m": "1m", "2m": "2m", "5m": "5m", "15m": "15m", "30m": "30m", "60m": "60m", "90m": "90m",
        # Hours
        "1h": "1h", "1hr": "1h", "60min": "1h",
        # Days
        "1d": "1d", "day": "1d", "daily": "1d",
        # Weeks
        "1wk": "1wk", "1w": "1wk", "week": "1wk", "weekly": "1wk",
        # Months
        "1mo": "1mo", "1m": "1mo", "month": "1mo", "monthly": "1mo",
        # Quarters
        "3mo": "3mo", "quarter": "3mo", "quarterly": "3mo"
    }

    # Normalize timeframe
    tf = timeframe.lower()
    if tf in valid_timeframes:
        return valid_timeframes[tf]

    # Handle special cases
    if tf == "1min":
        return "1m"
    elif tf == "5min":
        return "5m"
    elif tf == "15min":
        return "15m"
    elif tf == "30min":
        return "30m"

    logger.warning(f"Invalid timeframe: {timeframe}")
    logger.info("Valid timeframes: 1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 1wk, 1mo, 3mo")
    return None

def parse_date(date_str):
    """
    Parse a date string in various formats.

    Args:
        date_str: Date string

    Returns:
        Datetime object or None if parsing fails
    """
    if not date_str:
        return None

    formats = [
        "%Y-%m-%d",  # 2025-05-19
        "%Y/%m/%d",  # 2025/05/19
        "%d-%m-%Y",  # 19-05-2025
        "%d/%m/%Y",  # 19/05/2025
        "%Y%m%d",    # 20250519
        "%d%m%Y"     # 19052025
    ]

    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue

    logger.warning(f"Could not parse date: {date_str}")
    logger.info("Supported date formats: YYYY-MM-DD, YYYY/MM/DD, DD-MM-YYYY, DD/MM/YYYY, YYYYMMDD, DDMMYYYY")
    return None

def download_data(symbol, exchange, timeframe="1d", days=365, start_date=None, end_date=None, output_dir=DATA_DIR):
    """
    Download data for a symbol.

    Args:
        symbol: Symbol to download
        exchange: Exchange code (nse, bse)
        timeframe: Timeframe for the data
        days: Number of days of history to download (used if start_date is not provided)
        start_date: Start date (datetime object or string)
        end_date: End date (datetime object or string)
        output_dir: Directory to save the data

    Returns:
        Tuple of (symbol, success, rows)
    """
    try:
        # Validate timeframe
        normalized_timeframe = validate_timeframe(timeframe)
        if not normalized_timeframe:
            return symbol, False, 0

        # Parse dates if they are strings
        if isinstance(start_date, str):
            start_date = parse_date(start_date)
            if not start_date:
                return symbol, False, 0

        if isinstance(end_date, str):
            end_date = parse_date(end_date)
            if not end_date:
                return symbol, False, 0

        # Calculate date range if not provided
        if not end_date:
            end_date = datetime.now()

        if not start_date:
            start_date = end_date - timedelta(days=days)

        # Ensure start_date is before end_date
        if start_date >= end_date:
            logger.error(f"Start date ({start_date}) must be before end date ({end_date})")
            return symbol, False, 0

        # Convert symbol to yfinance format
        yf_symbol = get_yfinance_symbol(symbol, exchange)

        # Download data
        logger.info(f"Downloading {normalized_timeframe} data for {yf_symbol} from {start_date.date()} to {end_date.date()}")
        df = yf.download(
            tickers=yf_symbol,
            start=start_date,
            end=end_date,
            interval=normalized_timeframe,
            auto_adjust=True,
            progress=False
        )

        if df.empty:
            logger.warning(f"No data found for {yf_symbol}")
            return symbol, False, 0

        # Standardize column names
        if isinstance(df.columns, pd.MultiIndex):
            # Handle multi-level columns (when downloading multiple symbols)
            new_columns = []
            for col in df.columns:
                if isinstance(col, tuple):
                    new_columns.append(col[0].lower())
                else:
                    new_columns.append(col.lower())
            df.columns = new_columns
        else:
            # Handle single-level columns
            df.columns = [col.lower() for col in df.columns]

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Save data to CSV
        output_file = os.path.join(output_dir, f"{exchange.upper()}_{symbol}_{timeframe}.csv")
        df.to_csv(output_file)
        logger.info(f"Saved {len(df)} rows to {output_file}")

        return symbol, True, len(df)
    except Exception as e:
        logger.error(f"Error downloading data for {symbol}: {e}")
        return symbol, False, 0

def download_all(exchange, timeframe="1d", days=365, start_date=None, end_date=None, max_workers=5, max_symbols=None, output_dir=DATA_DIR):
    """
    Download data for all symbols.

    Args:
        exchange: Exchange code (nse, bse)
        timeframe: Timeframe for the data
        days: Number of days of history to download (used if start_date is not provided)
        start_date: Start date (datetime object or string)
        end_date: End date (datetime object or string)
        max_workers: Maximum number of concurrent downloads
        max_symbols: Maximum number of symbols to download (None for all)
        output_dir: Directory to save the data

    Returns:
        Dictionary with download statistics
    """
    # Validate timeframe
    normalized_timeframe = validate_timeframe(timeframe)
    if not normalized_timeframe:
        logger.error(f"Invalid timeframe: {timeframe}")
        return {"total_symbols": 0, "success_count": 0, "success_rate": 0, "total_rows": 0, "elapsed_time": 0}

    # Parse dates if they are strings
    if isinstance(start_date, str):
        start_date = parse_date(start_date)
        if not start_date:
            logger.error(f"Invalid start date: {start_date}")
            return {"total_symbols": 0, "success_count": 0, "success_rate": 0, "total_rows": 0, "elapsed_time": 0}

    if isinstance(end_date, str):
        end_date = parse_date(end_date)
        if not end_date:
            logger.error(f"Invalid end date: {end_date}")
            return {"total_symbols": 0, "success_count": 0, "success_rate": 0, "total_rows": 0, "elapsed_time": 0}

    # Calculate date range if not provided
    if not end_date:
        end_date = datetime.now()

    if not start_date:
        start_date = end_date - timedelta(days=days)

    # Ensure start_date is before end_date
    if start_date >= end_date:
        logger.error(f"Start date ({start_date}) must be before end date ({end_date})")
        return {"total_symbols": 0, "success_count": 0, "success_rate": 0, "total_rows": 0, "elapsed_time": 0}

    # Load symbols
    symbols = load_symbols(exchange)

    if not symbols:
        logger.error(f"No symbols found for {exchange}")
        return {"total_symbols": 0, "success_count": 0, "success_rate": 0, "total_rows": 0, "elapsed_time": 0}

    # Limit the number of symbols if specified
    if max_symbols and max_symbols < len(symbols):
        symbols = symbols[:max_symbols]
        logger.info(f"Limited to {len(symbols)} symbols")

    # Log download parameters
    logger.info(f"Downloading {normalized_timeframe} data for {len(symbols)} symbols from {exchange.upper()}")
    logger.info(f"Date range: {start_date.date()} to {end_date.date()}")
    logger.info(f"Using {max_workers} concurrent workers")

    # Download data for each symbol in parallel
    results = {}
    start_time = time.time()

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit download tasks
        future_to_symbol = {
            executor.submit(download_data, symbol, exchange, normalized_timeframe, days, start_date, end_date, output_dir): symbol
            for symbol in symbols
        }

        # Process results as they complete
        for i, future in enumerate(concurrent.futures.as_completed(future_to_symbol)):
            symbol = future_to_symbol[future]
            try:
                symbol, success, rows = future.result()
                results[symbol] = {"success": success, "rows": rows}

                # Log progress
                completed = i + 1
                total = len(symbols)
                elapsed = time.time() - start_time
                avg_time = elapsed / completed
                remaining = avg_time * (total - completed)

                logger.info(f"Progress: {completed}/{total} ({completed/total*100:.1f}%) - "
                           f"Elapsed: {elapsed:.1f}s - Remaining: {remaining:.1f}s")
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                results[symbol] = {"success": False, "rows": 0}

    # Calculate statistics
    success_count = sum(1 for result in results.values() if result["success"])
    total_rows = sum(result["rows"] for result in results.values())
    elapsed_time = time.time() - start_time

    stats = {
        "total_symbols": len(symbols),
        "success_count": success_count,
        "success_rate": success_count / len(symbols) * 100 if symbols else 0,
        "total_rows": total_rows,
        "elapsed_time": elapsed_time,
        "avg_time_per_symbol": elapsed_time / len(symbols) if symbols else 0
    }

    return stats

def list_timeframes():
    """
    List all available timeframes.
    """
    logger.info("Available timeframes:")
    logger.info("Minutes: 1m, 2m, 5m, 15m, 30m, 60m, 90m")
    logger.info("Hours: 1h")
    logger.info("Days: 1d")
    logger.info("Weeks: 1wk")
    logger.info("Months: 1mo")
    logger.info("Quarters: 3mo")
    logger.info("")
    logger.info("Note: Minute and hour data is only available for the last 7 days")
    logger.info("      Daily data is available for the last 50+ years")
    logger.info("      Weekly and monthly data is available for the last 50+ years")

def main():
    """
    Main function.
    """
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Download data from YFinance")
    parser.add_argument("--exchange", choices=["nse", "bse"], required=True, help="Exchange to download data for")
    parser.add_argument("--timeframe", default="1d", help="Timeframe for the data (e.g., 1d, 1wk, 1mo)")
    parser.add_argument("--days", type=int, default=365, help="Number of days of history to download (used if start-date is not provided)")
    parser.add_argument("--start-date", help="Start date (format: YYYY-MM-DD, YYYY/MM/DD, DD-MM-YYYY, DD/MM/YYYY, YYYYMMDD, DDMMYYYY)")
    parser.add_argument("--end-date", help="End date (format: YYYY-MM-DD, YYYY/MM/DD, DD-MM-YYYY, DD/MM/YYYY, YYYYMMDD, DDMMYYYY)")
    parser.add_argument("--max-workers", type=int, default=5, help="Maximum number of concurrent downloads")
    parser.add_argument("--max-symbols", type=int, default=None, help="Maximum number of symbols to download")
    parser.add_argument("--output-dir", default=DATA_DIR, help="Directory to save the data")
    parser.add_argument("--symbol", help="Download data for a specific symbol only")
    parser.add_argument("--list-timeframes", action="store_true", help="List available timeframes and exit")
    args = parser.parse_args()

    # List timeframes and exit if requested
    if args.list_timeframes:
        list_timeframes()
        return

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Download data for a specific symbol or all symbols
    if args.symbol:
        logger.info(f"Downloading data for {args.symbol} from {args.exchange}")
        symbol, success, rows = download_data(
            symbol=args.symbol,
            exchange=args.exchange,
            timeframe=args.timeframe,
            days=args.days,
            start_date=args.start_date,
            end_date=args.end_date,
            output_dir=args.output_dir
        )
        if success:
            logger.info(f"Successfully downloaded {rows} rows for {symbol}")
        else:
            logger.error(f"Failed to download data for {symbol}")
    else:
        logger.info(f"Downloading data for all symbols from {args.exchange}")
        stats = download_all(
            exchange=args.exchange,
            timeframe=args.timeframe,
            days=args.days,
            start_date=args.start_date,
            end_date=args.end_date,
            max_workers=args.max_workers,
            max_symbols=args.max_symbols,
            output_dir=args.output_dir
        )

        # Print statistics
        logger.info("Download completed")
        logger.info(f"Total symbols: {stats['total_symbols']}")
        logger.info(f"Success count: {stats['success_count']}")
        logger.info(f"Success rate: {stats['success_rate']:.2f}%")
        logger.info(f"Total rows: {stats['total_rows']}")
        logger.info(f"Elapsed time: {stats['elapsed_time']:.2f}s")
        logger.info(f"Average time per symbol: {stats['avg_time_per_symbol']:.2f}s")

if __name__ == "__main__":
    main()
