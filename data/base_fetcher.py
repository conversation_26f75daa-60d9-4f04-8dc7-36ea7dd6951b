"""
Base Data Fetcher for RapidTrader

This module defines the base class for all data fetchers in RapidTrader.
"""

import abc
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Union, Any
import pandas as pd

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("data_fetcher")

class BaseDataFetcher(abc.ABC):
    """
    Abstract base class for all data fetchers.
    
    This class defines the interface that all data fetchers must implement.
    """
    
    def __init__(self, cache_dir: str = "userdata/historical_data"):
        """
        Initialize the data fetcher.
        
        Args:
            cache_dir: Directory to cache data
        """
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        
    @abc.abstractmethod
    def fetch_ohlcv(self, 
                   symbol: str, 
                   timeframe: str,
                   start_date: Optional[datetime] = None,
                   end_date: Optional[datetime] = None,
                   exchange: Optional[str] = None) -> pd.DataFrame:
        """
        Fetch OHLCV (Open, High, Low, Close, Volume) data for a symbol.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data (e.g., '1m', '5m', '1h', '1d')
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from
            
        Returns:
            DataFrame with OHLCV data
        """
        pass
    
    @abc.abstractmethod
    def fetch_multiple_ohlcv(self, 
                            symbols: List[str], 
                            timeframe: str,
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None,
                            exchange: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        Fetch OHLCV data for multiple symbols.
        
        Args:
            symbols: List of trading symbols
            timeframe: Timeframe for the data (e.g., '1m', '5m', '1h', '1d')
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from
            
        Returns:
            Dictionary mapping symbols to DataFrames with OHLCV data
        """
        pass
    
    @abc.abstractmethod
    def get_available_timeframes(self) -> List[str]:
        """
        Get the list of available timeframes.
        
        Returns:
            List of available timeframes
        """
        pass
    
    @abc.abstractmethod
    def get_exchange_info(self, exchange: str) -> Dict[str, Any]:
        """
        Get information about an exchange.
        
        Args:
            exchange: Exchange name
            
        Returns:
            Dictionary with exchange information
        """
        pass
    
    def get_cache_filename(self, symbol: str, timeframe: str, exchange: Optional[str] = None) -> str:
        """
        Get the filename for caching data.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            exchange: Exchange name
            
        Returns:
            Cache filename
        """
        if exchange:
            return os.path.join(self.cache_dir, f"{exchange}_{symbol}_{timeframe}.csv")
        else:
            return os.path.join(self.cache_dir, f"{symbol}_{timeframe}.csv")
    
    def save_to_cache(self, df: pd.DataFrame, symbol: str, timeframe: str, exchange: Optional[str] = None) -> None:
        """
        Save data to cache.
        
        Args:
            df: DataFrame with OHLCV data
            symbol: Trading symbol
            timeframe: Timeframe for the data
            exchange: Exchange name
        """
        if df.empty:
            logger.warning(f"Empty DataFrame for {symbol}, not saving to cache")
            return
            
        filename = self.get_cache_filename(symbol, timeframe, exchange)
        df.to_csv(filename)
        logger.info(f"Saved {len(df)} rows to {filename}")
    
    def load_from_cache(self, symbol: str, timeframe: str, exchange: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        Load data from cache.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            exchange: Exchange name
            
        Returns:
            DataFrame with OHLCV data, or None if not in cache
        """
        filename = self.get_cache_filename(symbol, timeframe, exchange)
        if os.path.exists(filename):
            try:
                df = pd.read_csv(filename, index_col=0, parse_dates=True)
                logger.info(f"Loaded {len(df)} rows from {filename}")
                return df
            except Exception as e:
                logger.error(f"Error loading from cache: {e}")
                return None
        else:
            logger.info(f"Cache file {filename} not found")
            return None
    
    def clear_cache(self, symbol: Optional[str] = None, timeframe: Optional[str] = None, exchange: Optional[str] = None) -> None:
        """
        Clear cache files.
        
        Args:
            symbol: Trading symbol (if None, clear all symbols)
            timeframe: Timeframe for the data (if None, clear all timeframes)
            exchange: Exchange name (if None, clear all exchanges)
        """
        if symbol and timeframe:
            filename = self.get_cache_filename(symbol, timeframe, exchange)
            if os.path.exists(filename):
                os.remove(filename)
                logger.info(f"Removed cache file {filename}")
        else:
            pattern = ""
            if exchange:
                pattern += f"{exchange}_"
            if symbol:
                pattern += f"{symbol}_"
            if timeframe:
                pattern += f"{timeframe}"
                
            for file in os.listdir(self.cache_dir):
                if file.startswith(pattern) and file.endswith(".csv"):
                    os.remove(os.path.join(self.cache_dir, file))
                    logger.info(f"Removed cache file {file}")
