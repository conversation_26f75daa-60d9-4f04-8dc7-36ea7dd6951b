"""
Data Cleanup Module for RapidTrader

This module provides functionality for cleaning up and managing historical data.
"""

import os
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd

# Configure logging
logger = logging.getLogger("data_cleanup")

def get_data_files(data_dir: str, exchange: Optional[str] = None, symbol: Optional[str] = None, 
                  timeframe: Optional[str] = None) -> List[str]:
    """
    Get a list of data files matching the criteria.
    
    Args:
        data_dir: Directory containing data files
        exchange: Exchange filter (optional)
        symbol: Symbol filter (optional)
        timeframe: Timeframe filter (optional)
        
    Returns:
        List of file paths
    """
    files = []
    
    if not os.path.exists(data_dir):
        logger.warning(f"Data directory {data_dir} does not exist")
        return files
        
    for file in os.listdir(data_dir):
        if not file.endswith(".csv"):
            continue
            
        # Parse filename
        parts = file.split("_")
        
        if len(parts) < 2:
            continue
            
        # Handle different filename formats
        if len(parts) == 3:  # exchange_symbol_timeframe.csv
            file_exchange, file_symbol, file_timeframe = parts[0], parts[1], parts[2].replace(".csv", "")
        elif len(parts) == 2:  # symbol_timeframe.csv
            file_exchange, file_symbol, file_timeframe = None, parts[0], parts[1].replace(".csv", "")
        else:
            continue
            
        # Apply filters
        if exchange and file_exchange and file_exchange.lower() != exchange.lower():
            continue
            
        if symbol and file_symbol.lower() != symbol.lower():
            continue
            
        if timeframe and file_timeframe != timeframe:
            continue
            
        files.append(os.path.join(data_dir, file))
        
    return files

def get_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a data file.
    
    Args:
        file_path: Path to the data file
        
    Returns:
        Dictionary with file information
    """
    info = {
        "path": file_path,
        "filename": os.path.basename(file_path),
        "size": os.path.getsize(file_path),
        "last_modified": datetime.fromtimestamp(os.path.getmtime(file_path)),
        "rows": 0,
        "start_date": None,
        "end_date": None,
        "has_gaps": False,
        "gap_count": 0
    }
    
    try:
        # Load the data
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        
        # Get basic info
        info["rows"] = len(df)
        
        if not df.empty:
            info["start_date"] = df.index.min()
            info["end_date"] = df.index.max()
            
            # Check for gaps
            if len(df) > 1:
                # Get the expected frequency
                timeframe = info["filename"].split("_")[-1].replace(".csv", "")
                freq = None
                
                if timeframe == "1d":
                    freq = "B"  # Business day
                elif timeframe == "1wk":
                    freq = "W"
                elif timeframe == "1mo":
                    freq = "M"
                elif timeframe.endswith("m"):
                    freq = f"{timeframe[:-1]}min"
                elif timeframe.endswith("h"):
                    freq = f"{timeframe[:-1]}H"
                
                if freq:
                    # Create an ideal index
                    ideal_index = pd.date_range(start=info["start_date"], end=info["end_date"], freq=freq)
                    
                    # Check for missing dates
                    missing_dates = ideal_index.difference(df.index)
                    
                    if len(missing_dates) > 0:
                        info["has_gaps"] = True
                        info["gap_count"] = len(missing_dates)
    except Exception as e:
        logger.error(f"Error analyzing file {file_path}: {e}")
        
    return info

def cleanup_old_data(data_dir: str, days: int = 365, dry_run: bool = True) -> Dict[str, Any]:
    """
    Clean up data files older than the specified number of days.
    
    Args:
        data_dir: Directory containing data files
        days: Number of days to keep
        dry_run: If True, only simulate the cleanup
        
    Returns:
        Dictionary with cleanup statistics
    """
    stats = {
        "total_files": 0,
        "deleted_files": 0,
        "freed_space": 0
    }
    
    # Get all data files
    files = get_data_files(data_dir)
    stats["total_files"] = len(files)
    
    # Calculate cutoff date
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # Process each file
    for file_path in files:
        info = get_file_info(file_path)
        
        # Check if the file is older than the cutoff date
        if info["end_date"] and info["end_date"] < cutoff_date:
            stats["deleted_files"] += 1
            stats["freed_space"] += info["size"]
            
            if not dry_run:
                try:
                    os.remove(file_path)
                    logger.info(f"Deleted {file_path}")
                except Exception as e:
                    logger.error(f"Error deleting {file_path}: {e}")
    
    return stats

def verify_data_integrity(data_dir: str, fix: bool = False) -> Dict[str, Any]:
    """
    Verify the integrity of data files.
    
    Args:
        data_dir: Directory containing data files
        fix: If True, attempt to fix issues
        
    Returns:
        Dictionary with verification statistics
    """
    stats = {
        "total_files": 0,
        "corrupt_files": 0,
        "files_with_gaps": 0,
        "total_gaps": 0,
        "fixed_files": 0
    }
    
    # Get all data files
    files = get_data_files(data_dir)
    stats["total_files"] = len(files)
    
    # Process each file
    for file_path in files:
        try:
            # Load the data
            df = pd.read_csv(file_path, index_col=0, parse_dates=True)
            
            # Check for gaps
            info = get_file_info(file_path)
            
            if info["has_gaps"]:
                stats["files_with_gaps"] += 1
                stats["total_gaps"] += info["gap_count"]
                
                # TODO: Implement gap filling if fix=True
        except Exception as e:
            stats["corrupt_files"] += 1
            logger.error(f"Corrupt file {file_path}: {e}")
            
            if fix:
                try:
                    # Attempt to fix by removing the file
                    os.remove(file_path)
                    stats["fixed_files"] += 1
                    logger.info(f"Removed corrupt file {file_path}")
                except Exception as e2:
                    logger.error(f"Error removing corrupt file {file_path}: {e2}")
    
    return stats

def merge_data_files(data_dir: str, output_dir: str, exchange: str, symbol: str, 
                    timeframes: List[str]) -> Dict[str, Any]:
    """
    Merge data files for a symbol across different timeframes.
    
    Args:
        data_dir: Directory containing data files
        output_dir: Directory to save merged files
        exchange: Exchange code
        symbol: Symbol to merge
        timeframes: List of timeframes to merge
        
    Returns:
        Dictionary with merge statistics
    """
    stats = {
        "merged_files": 0,
        "total_rows": 0
    }
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each timeframe
    for timeframe in timeframes:
        # Get files for this symbol and timeframe
        files = get_data_files(data_dir, exchange, symbol, timeframe)
        
        if not files:
            logger.warning(f"No files found for {exchange}_{symbol}_{timeframe}")
            continue
            
        # Merge the data
        merged_df = pd.DataFrame()
        
        for file_path in files:
            try:
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
                
                if merged_df.empty:
                    merged_df = df
                else:
                    # Combine and remove duplicates
                    merged_df = pd.concat([merged_df, df])
                    merged_df = merged_df[~merged_df.index.duplicated(keep='last')]
                    merged_df = merged_df.sort_index()
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")
        
        if not merged_df.empty:
            # Save the merged data
            output_file = os.path.join(output_dir, f"{exchange}_{symbol}_{timeframe}.csv")
            merged_df.to_csv(output_file)
            
            stats["merged_files"] += 1
            stats["total_rows"] += len(merged_df)
            
            logger.info(f"Merged {len(files)} files into {output_file} with {len(merged_df)} rows")
    
    return stats
