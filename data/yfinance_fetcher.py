"""
YFinance Data Fetcher for RapidTrader

This module implements a data fetcher using the yfinance library.
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any
import pandas as pd
import yfinance as yf

from data.base_fetcher import BaseDataFetcher

# Configure logging
logger = logging.getLogger("yfinance_fetcher")

class YFinanceFetcher(BaseDataFetcher):
    """
    YFinance data fetcher implementation.

    This class implements the BaseDataFetcher interface using the yfinance library.
    """

    # Mapping from RapidTrader timeframes to yfinance intervals
    TIMEFRAME_MAP = {
        "1m": "1m",
        "2m": "2m",
        "5m": "5m",
        "15m": "15m",
        "30m": "30m",
        "1h": "60m",
        "1d": "1d",
        "5d": "5d",
        "1wk": "1wk",
        "1mo": "1mo",
        "3mo": "3mo"
    }

    # Mapping from exchanges to yfinance suffixes
    EXCHANGE_MAP = {
        "NSE": ".NS",
        "BSE": ".BO",
        "NYSE": "",
        "NASDAQ": "",
        "AMEX": "",
        "MCX": ".MCX",
        "NYMEX": "",
        "FOREX": "=X"
    }

    def __init__(self, cache_dir: str = "userdata/historical_data", use_cache: bool = True):
        """
        Initialize the YFinance data fetcher.

        Args:
            cache_dir: Directory to cache data
            use_cache: Whether to use cache
        """
        super().__init__(cache_dir)
        self.use_cache = use_cache

    def _get_yfinance_symbol(self, symbol: str, exchange: Optional[str] = None) -> str:
        """
        Convert a RapidTrader symbol to a yfinance symbol.

        Args:
            symbol: RapidTrader symbol
            exchange: Exchange name

        Returns:
            yfinance symbol
        """
        # Handle special cases
        if "-EQ" in symbol:
            # Convert SBIN-EQ to SBIN.NS
            base_symbol = symbol.split("-")[0]
            if exchange == "NSE" or exchange is None:
                return f"{base_symbol}.NS"
            elif exchange == "BSE":
                return f"{base_symbol}.BO"
            else:
                return base_symbol

        # Add exchange suffix if needed
        if exchange and exchange in self.EXCHANGE_MAP:
            suffix = self.EXCHANGE_MAP[exchange]
            if suffix and not symbol.endswith(suffix):
                return f"{symbol}{suffix}"

        return symbol

    def _get_yfinance_interval(self, timeframe: str) -> str:
        """
        Convert a RapidTrader timeframe to a yfinance interval.

        Args:
            timeframe: RapidTrader timeframe

        Returns:
            yfinance interval
        """
        return self.TIMEFRAME_MAP.get(timeframe, "1d")

    def _standardize_dataframe(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        Standardize a yfinance DataFrame to RapidTrader format.

        Args:
            df: yfinance DataFrame
            symbol: Trading symbol

        Returns:
            Standardized DataFrame
        """
        if df.empty:
            return pd.DataFrame()

        # Handle multi-level columns (when downloading multiple symbols)
        if isinstance(df.columns, pd.MultiIndex):
            # Extract data for the specific symbol
            symbol_data = {}
            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                if (col, symbol) in df.columns:
                    symbol_data[col.lower()] = df[(col, symbol)]

            if not symbol_data:
                return pd.DataFrame()

            result_df = pd.DataFrame(symbol_data, index=df.index)
        else:
            # Rename columns to lowercase
            result_df = df.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })

        # Ensure the index is a DatetimeIndex named 'date'
        if not isinstance(result_df.index, pd.DatetimeIndex):
            result_df.index = pd.to_datetime(result_df.index)

        result_df.index.name = 'date'

        # Ensure all required columns exist
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col not in result_df.columns:
                result_df[col] = 0

        return result_df

    def fetch_ohlcv(self,
                   symbol: str,
                   timeframe: str,
                   start_date: Optional[datetime] = None,
                   end_date: Optional[datetime] = None,
                   exchange: Optional[str] = None) -> pd.DataFrame:
        """
        Fetch OHLCV data for a symbol using yfinance.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from

        Returns:
            DataFrame with OHLCV data
        """
        # Check cache first if enabled
        if self.use_cache:
            cached_data = self.load_from_cache(symbol, timeframe, exchange)
            if cached_data is not None:
                # If we have cached data and no date range is specified, return it
                if start_date is None and end_date is None:
                    return cached_data

                # If we have cached data and a date range is specified, filter it
                if start_date is not None:
                    cached_data = cached_data[cached_data.index >= pd.Timestamp(start_date)]
                if end_date is not None:
                    cached_data = cached_data[cached_data.index <= pd.Timestamp(end_date)]

                # If the filtered data covers the requested range, return it
                if not cached_data.empty:
                    if start_date is None or cached_data.index.min() <= pd.Timestamp(start_date):
                        if end_date is None or cached_data.index.max() >= pd.Timestamp(end_date):
                            return cached_data

        # Convert symbol and timeframe to yfinance format
        yf_symbol = self._get_yfinance_symbol(symbol, exchange)
        yf_interval = self._get_yfinance_interval(timeframe)

        # Determine period or date range
        if start_date is None and end_date is None:
            # Use period='max' to get all available data
            period = 'max'
            kwargs = {'period': period}
        else:
            # Use start and end dates
            kwargs = {}
            if start_date:
                kwargs['start'] = start_date
            if end_date:
                kwargs['end'] = end_date

        try:
            # Download data from yfinance
            logger.info(f"Downloading {yf_symbol} data with interval {yf_interval}")
            df = yf.download(
                tickers=yf_symbol,
                interval=yf_interval,
                auto_adjust=True,
                progress=False,
                **kwargs
            )

            # Standardize the DataFrame
            result_df = self._standardize_dataframe(df, yf_symbol)

            # Cache the result if enabled
            if self.use_cache and not result_df.empty:
                self.save_to_cache(result_df, symbol, timeframe, exchange)

            return result_df
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    def fetch_multiple_ohlcv(self,
                            symbols: List[str],
                            timeframe: str,
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None,
                            exchange: Optional[str] = None) -> Dict[str, pd.DataFrame]:
        """
        Fetch OHLCV data for multiple symbols using yfinance.

        Args:
            symbols: List of trading symbols
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from

        Returns:
            Dictionary mapping symbols to DataFrames with OHLCV data
        """
        result = {}

        # Fetch each symbol individually for more reliable results
        for symbol in symbols:
            try:
                df = self.fetch_ohlcv(
                    symbol=symbol,
                    timeframe=timeframe,
                    start_date=start_date,
                    end_date=end_date,
                    exchange=exchange
                )
                result[symbol] = df
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {e}")
                result[symbol] = pd.DataFrame()

        return result

    def get_available_timeframes(self) -> List[str]:
        """
        Get the list of available timeframes.

        Returns:
            List of available timeframes
        """
        return list(self.TIMEFRAME_MAP.keys())

    def get_exchange_info(self, exchange: str) -> Dict[str, Any]:
        """
        Get information about an exchange.

        Args:
            exchange: Exchange name

        Returns:
            Dictionary with exchange information
        """
        exchange_info = {
            "name": exchange,
            "supported": exchange in self.EXCHANGE_MAP,
            "suffix": self.EXCHANGE_MAP.get(exchange, ""),
            "timeframes": self.get_available_timeframes()
        }

        return exchange_info

    def get_all_symbols(self, exchange: str) -> List[str]:
        """
        Get all available symbols for an exchange.

        Args:
            exchange: Exchange name

        Returns:
            List of available symbols
        """
        import os
        import sys

        # Try to import the symbol_manager module
        try:
            # Add the parent directory to the path if needed
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)

            from symbol_manager import get_symbols

            # Load symbols from the symbol_manager
            symbols = get_symbols(exchange)
            if symbols:
                logger.info(f"Loaded {len(symbols)} symbols for {exchange} from symbol_manager")
                return symbols
        except Exception as e:
            logger.warning(f"Error loading symbols from symbol_manager: {e}")

        # If we get here, we need to use the fallback method
        symbols = []

        if exchange == "NSE":
            # Fetch NSE symbols
            try:
                # First, try to load from a comprehensive NSE symbols file
                # This file contains a much larger list of NSE symbols
                nse_symbols_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "nse_symbols.csv")

                # If the file doesn't exist, create it with a comprehensive list of NSE symbols
                if not os.path.exists(nse_symbols_file):
                    # Create a comprehensive list of NSE symbols
                    # This is a combination of various indices and sectors
                    comprehensive_symbols = [
                        # Nifty 50
                        "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "HINDUNILVR", "INFY", "HDFC", "ITC", "SBIN", "BHARTIARTL",
                        "KOTAKBANK", "LT", "BAJFINANCE", "AXISBANK", "ASIANPAINT", "MARUTI", "HCLTECH", "SUNPHARMA", "TITAN", "ULTRACEMCO",
                        "TATAMOTORS", "ADANIPORTS", "WIPRO", "POWERGRID", "NTPC", "BAJAJ-AUTO", "ONGC", "JSWSTEEL", "GRASIM", "TATASTEEL",
                        "INDUSINDBK", "DRREDDY", "NESTLEIND", "COALINDIA", "TECHM", "BPCL", "HINDALCO", "DIVISLAB", "SHREECEM", "HEROMOTOCO",
                        "BRITANNIA", "CIPLA", "EICHERMOT", "M&M", "UPL", "IOC", "TATACONSUM", "HDFCLIFE", "SBILIFE", "BAJAJFINSV",

                        # Banking & Financial Services
                        "YESBANK", "PNB", "BANKBARODA", "CANBK", "UNIONBANK", "FEDERALBNK", "BANDHANBNK", "RBLBANK", "IDBI", "CENTRALBK",
                        "MAHABANK", "UCOBANK", "J&KBANK", "KARURVYSYA", "DCBBANK", "SOUTHBANK", "CITYUNIONB", "UJJIVANSFB", "AUBANK", "IDFCFIRSTB",
                        "MUTHOOTFIN", "MANAPPURAM", "CHOLAFIN", "BAJAJFINSV", "BAJAJHLDNG", "HDFCAMC", "ICICIGI", "ICICIPRULI", "HDFCLIFE", "SBILIFE",
                        "PNBHOUSING", "LICHSGFIN", "IBULHSGFIN", "CANFINHOME", "CREDITACC", "IIFL", "ISEC", "ANGELONE", "MOTILALOFS", "RATEGAIN",

                        # IT & Technology
                        "INFY", "TCS", "WIPRO", "HCLTECH", "TECHM", "LTIM", "PERSISTENT", "COFORGE", "MINDTREE", "MPHASIS",
                        "NAUKRI", "ZOMATO", "POLICYBZR", "PAYTM", "NYKAA", "TANLA", "ROUTE", "INDIAMART", "JUSTDIAL", "IRCTC",
                        "HAPPSTMNDS", "SONATSOFTW", "CYIENT", "KPITTECH", "OFSS", "INTELLECT", "RAMCOAMS", "MASTEK", "ECLERX", "DATAPATTNS",

                        # Pharma & Healthcare
                        "SUNPHARMA", "DRREDDY", "CIPLA", "DIVISLAB", "AUROPHARMA", "LUPIN", "BIOCON", "GLAND", "ALKEM", "TORNTPHARM",
                        "APOLLOHOSP", "FORTIS", "MAXHEALTH", "MEDPLUS", "METROPOLIS", "LALPATHLAB", "STAR", "NATCOPHARM", "GRANULES", "IPCALAB",
                        "AJANTPHARM", "ASTRAZEN", "ABBOTINDIA", "PFIZER", "GLAXO", "SANOFI", "JBCHEPHARM", "LAURUSLABS", "CAPLIN", "THYROCARE",

                        # FMCG & Consumer
                        "HINDUNILVR", "ITC", "NESTLEIND", "BRITANNIA", "DABUR", "MARICO", "GODREJCP", "COLPAL", "EMAMILTD", "PGHH",
                        "TATACONSUM", "MCDOWELL-N", "RADICO", "UBL", "ABFRL", "BATAINDIA", "PAGEIND", "TRENT", "VMART", "JUBLFOOD",
                        "VARUNBEV", "GSKCONS", "HATSUN", "VBL", "TASTYBITE", "JYOTHYLAB", "GILLETTE", "BAJAJCON", "RELAXO", "CAMPUS",

                        # Manufacturing & Industrial
                        "LT", "SIEMENS", "ABB", "HAVELLS", "CROMPTON", "POLYCAB", "KEI", "VOLTAS", "BLUESTAR", "AMBER",
                        "TATASTEEL", "JSWSTEEL", "SAIL", "HINDALCO", "NATIONALUM", "JINDALSTEL", "APLAPOLLO", "RATNAMANI", "WELCORP", "KALYANKJIL",
                        "GRINDWELL", "CARBORUNIV", "TIMKEN", "SCHAEFFLER", "SKFINDIA", "ELGIEQUIP", "ASTRAL", "SUPREMEIND", "FINOLEXIND", "CERA",

                        # Oil & Gas
                        "RELIANCE", "ONGC", "GAIL", "IGL", "MGL", "GUJGASLTD", "PETRONET", "BPCL", "HPCL", "IOC",
                        "CASTROLIND", "GULFOILLUB", "HINDPETRO", "OIL", "GSPL", "ATGL", "MAHSEAMLES", "DEEPAKFERT", "CHAMBLFERT", "FACT",

                        # Cement & Construction
                        "ULTRACEMCO", "SHREECEM", "AMBUJACEM", "ACC", "RAMCOCEM", "JKCEMENT", "DALBHARAT", "HEIDELBERG", "ORIENTCEM", "STARCEMENT",
                        "DLF", "GODREJPROP", "PRESTIGE", "OBEROIRLTY", "SOBHA", "BRIGADE", "MAHLIFE", "SUNTECK", "KOLTEPATIL", "PURAVANKARA",

                        # Automobile & Auto Ancillaries
                        "TATAMOTORS", "M&M", "MARUTI", "BAJAJ-AUTO", "HEROMOTOCO", "EICHERMOT", "TVSMOTOR", "ASHOKLEY", "BHARATFORG", "MRF",
                        "APOLLOTYRE", "CEATLTD", "BALKRISIND", "EXIDEIND", "AMARARAJA", "BOSCHLTD", "MOTHERSON", "ENDURANCE", "VARROC", "SOLARINDS",

                        # Power & Utilities
                        "NTPC", "POWERGRID", "TATAPOWER", "ADANIPOWER", "TORNTPOWER", "CESC", "JSWENERGY", "NHPC", "RECLTD", "PFC",
                        "SUZLON", "KALPATPOWR", "KEC", "ADANIGREEN", "ADANIENSOL", "JKPAPER", "WESTLIFE", "RVNL", "IRCON", "NBCC",

                        # Metals & Mining
                        "TATASTEEL", "JSWSTEEL", "SAIL", "HINDALCO", "JINDALSTEL", "NATIONALUM", "HINDZINC", "VEDL", "NMDC", "COALINDIA",
                        "MOIL", "GMDC", "APLAPOLLO", "RATNAMANI", "WELCORP", "KALYANKJIL", "SANDESH", "JSWHOLDINGS", "KIRLOSIND", "JKLAKSHMI",

                        # Telecom & Media
                        "BHARTIARTL", "IDEA", "TATACOMM", "INDIACEM", "NETWORK18", "TV18BRDCST", "ZEEL", "SUNTV", "PVR", "INOXLEISUR",
                        "NAZARA", "DBCORP", "JAGRAN", "HATHWAY", "DISHTV", "NXTDIGITAL", "SAREGAMA", "TIPS", "TVTODAY", "BALAJITELE",

                        # Chemicals & Fertilizers
                        "PIDILITIND", "ASIANPAINT", "BERGEPAINT", "AKZOINDIA", "BAYERCROP", "UPL", "PIIND", "SRF", "ATUL", "DEEPAKNITRITE",
                        "NAVINFLUOR", "FLUOROCHEM", "ALKYLAMINE", "AARTIIND", "SUMICHEM", "BASF", "RALLIS", "COROMANDEL", "CHAMBLFERT", "GNFC",

                        # Textiles & Apparel
                        "PAGEIND", "ABFRL", "VMART", "TRENT", "RELAXO", "BATA", "CAMPUS", "KPRMILL", "RAYMOND", "ARVIND",
                        "WELSPUNIND", "GRASIM", "VARDHACRLC", "INDORAMA", "JSWISPL", "HIMATSEIDE", "TRIDENT", "VIPIND", "LUXIND", "JCHAC",

                        # Real Estate
                        "DLF", "GODREJPROP", "PRESTIGE", "OBEROIRLTY", "SOBHA", "BRIGADE", "MAHLIFE", "SUNTECK", "KOLTEPATIL", "PURAVANKARA",
                        "PHOENIXLTD", "IBREALEST", "ANANTRAJ", "MAHINDCIE", "INDIABULLS", "UNITECH", "HDIL", "PARSVNATH", "OMAXE", "AJMERA",

                        # New Age Tech & E-commerce
                        "ZOMATO", "NYKAA", "PAYTM", "POLICYBZR", "CARTRADE", "EASEMYTRIP", "NAZARA", "JUSTDIAL", "INDIAMART", "IRCTC",
                        "INFIBEAM", "TANLA", "ROUTE", "AFFLE", "HAPPSTMNDS", "DATAPATTNS", "CAMS", "KFINTECH", "MAPMYINDIA", "DELHIVERY",

                        # Defense & Aerospace
                        "HAL", "BEL", "BDL", "COCHINSHIP", "GRSE", "MAZAGAON", "DATAPATTERN", "PARAS", "MIDHANI", "DYNAMATECH",

                        # Miscellaneous
                        "ADANIENT", "ADANIPORTS", "ADANIGREEN", "ADANIPOWER", "ADANIENSOL", "ADANITRANS", "ATGL", "AWL", "AMBUJACEM", "ACC"
                    ]

                    # Remove duplicates
                    comprehensive_symbols = list(dict.fromkeys(comprehensive_symbols))

                    # Save to file
                    with open(nse_symbols_file, 'w') as f:
                        f.write("Symbol\n")
                        for symbol in comprehensive_symbols:
                            f.write(f"{symbol}\n")

                    symbols = comprehensive_symbols
                else:
                    # Load from file
                    df = pd.read_csv(nse_symbols_file)
                    if "Symbol" in df.columns:
                        symbols = df["Symbol"].tolist()
                    else:
                        symbols = df.iloc[:, 0].tolist()

                # If we still don't have symbols, try to fetch from NSE website
                if not symbols:
                    # Method 1: Try to fetch from NSE website (Nifty 500 companies)
                    import requests
                    import io
                    import pandas as pd

                    url = "https://archives.nseindia.com/content/indices/ind_nifty500list.csv"
                    response = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})

                    if response.status_code == 200:
                        df = pd.read_csv(io.StringIO(response.text))
                        if "Symbol" in df.columns:
                            symbols = df["Symbol"].tolist()
                        elif "SYMBOL" in df.columns:
                            symbols = df["SYMBOL"].tolist()

                # If that fails, try another source
                if not symbols:
                    # Method 2: Try to fetch from another source (Nifty 50 companies)
                    import requests
                    import io
                    import pandas as pd

                    url = "https://archives.nseindia.com/content/indices/ind_nifty50list.csv"
                    response = requests.get(url, headers={"User-Agent": "Mozilla/5.0"})

                    if response.status_code == 200:
                        df = pd.read_csv(io.StringIO(response.text))
                        if "Symbol" in df.columns:
                            symbols = df["Symbol"].tolist()
                        elif "SYMBOL" in df.columns:
                            symbols = df["SYMBOL"].tolist()

                # If all methods fail, use a hardcoded list of major NSE stocks
                if not symbols:
                    logger.warning("Failed to fetch NSE symbols from all sources, using hardcoded list")
                    symbols = [
                        # Nifty 50 companies
                        "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "HINDUNILVR", "INFY", "HDFC", "ITC", "SBIN", "BHARTIARTL",
                        "KOTAKBANK", "LT", "BAJFINANCE", "AXISBANK", "ASIANPAINT", "MARUTI", "HCLTECH", "SUNPHARMA", "TITAN", "ULTRACEMCO",
                        "TATAMOTORS", "ADANIPORTS", "WIPRO", "POWERGRID", "NTPC", "BAJAJ-AUTO", "ONGC", "JSWSTEEL", "GRASIM", "TATASTEEL",
                        "INDUSINDBK", "DRREDDY", "NESTLEIND", "COALINDIA", "TECHM", "BPCL", "HINDALCO", "DIVISLAB", "SHREECEM", "HEROMOTOCO",
                        "BRITANNIA", "CIPLA", "EICHERMOT", "M&M", "UPL", "IOC", "TATACONSUM", "HDFCLIFE", "SBILIFE", "BAJAJFINSV"
                    ]
            except Exception as e:
                logger.error(f"Error fetching NSE symbols: {e}")
                # Fallback to hardcoded list
                symbols = [
                    "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "HINDUNILVR", "INFY", "HDFC", "ITC", "SBIN", "BHARTIARTL",
                    "KOTAKBANK", "LT", "BAJFINANCE", "AXISBANK", "ASIANPAINT", "MARUTI", "HCLTECH", "SUNPHARMA", "TITAN", "ULTRACEMCO",
                    "TATAMOTORS", "ADANIPORTS", "WIPRO", "POWERGRID", "NTPC", "BAJAJ-AUTO", "ONGC", "JSWSTEEL", "GRASIM", "TATASTEEL"
                ]

        elif exchange == "BSE":
            # Fetch BSE symbols
            try:
                # Try to fetch BSE 500 companies (or a larger list)
                # Since direct BSE API access is complex, we'll use a more comprehensive hardcoded list
                # This list includes BSE Sensex 30 + additional popular BSE stocks
                symbols = [
                    # BSE Sensex 30 companies
                    "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "HINDUNILVR", "INFY", "HDFC", "ITC", "SBIN", "BHARTIARTL",
                    "KOTAKBANK", "LT", "BAJFINANCE", "AXISBANK", "ASIANPAINT", "MARUTI", "HCLTECH", "SUNPHARMA", "TITAN", "ULTRACEMCO",
                    "TATAMOTORS", "ADANIPORTS", "WIPRO", "NTPC", "ONGC", "JSWSTEEL", "TATASTEEL", "INDUSINDBK", "DRREDDY", "NESTLEIND",

                    # Additional BSE 500 companies
                    "YESBANK", "PNB", "BANKBARODA", "CANBK", "UNIONBANK", "FEDERALBNK", "BANDHANBNK", "RBLBANK", "IDBI", "CENTRALBK",
                    "MAHABANK", "UCOBANK", "J&KBANK", "KARURVYSYA", "DCBBANK", "SOUTHBANK", "CITYUNIONB", "UJJIVANSFB", "AUBANK", "IDFCFIRSTB",

                    # Large Cap Companies
                    "ADANIENT", "ADANIPOWER", "ADANIGREEN", "ADANIPORTS", "ADANIENSOL", "ZOMATO", "PAYTM", "NYKAA", "POLICYBZR", "DMART",
                    "BAJAJFINSV", "HDFCLIFE", "SBILIFE", "ICICIPRULI", "ICICIGI", "HDFCAMC", "ABSLAMC", "MUTHOOTFIN", "CHOLAFIN", "LTIM",
                    "WIPRO", "HCLTECH", "TECHM", "INFY", "TCS", "PERSISTENT", "COFORGE", "LTTS", "MINDTREE", "MPHASIS",

                    # Mid Cap Companies
                    "IRCTC", "INDIGO", "BHARATFORG", "ASHOKLEY", "TVSMOTOR", "EICHERMOT", "M&M", "HEROMOTOCO", "BAJAJ-AUTO", "TATAPOWER",
                    "NTPC", "POWERGRID", "NHPC", "RECLTD", "PFC", "TORNTPOWER", "CESC", "ADANIPOWER", "JSWENERGY", "SUZLON",

                    # Pharma & Healthcare
                    "SUNPHARMA", "DRREDDY", "CIPLA", "DIVISLAB", "AUROPHARMA", "LUPIN", "BIOCON", "GLAND", "ALKEM", "TORNTPHARM",
                    "APOLLOHOSP", "FORTIS", "MAXHEALTH", "MEDPLUS", "METROPOLIS", "LALPATHLAB", "STAR", "NATCOPHARM", "GRANULES", "IPCALAB",

                    # FMCG & Consumer
                    "HINDUNILVR", "ITC", "NESTLEIND", "BRITANNIA", "DABUR", "MARICO", "GODREJCP", "COLPAL", "EMAMILTD", "PGHH",
                    "TATACONSUM", "MCDOWELL-N", "RADICO", "UBL", "ABFRL", "BATAINDIA", "PAGEIND", "TRENT", "VMART", "JUBLFOOD",

                    # IT & Technology
                    "INFY", "TCS", "WIPRO", "HCLTECH", "TECHM", "LTIM", "PERSISTENT", "COFORGE", "MINDTREE", "MPHASIS",
                    "NAUKRI", "ZOMATO", "POLICYBZR", "PAYTM", "NYKAA", "TANLA", "ROUTE", "INDIAMART", "JUSTDIAL", "IRCTC",

                    # Manufacturing & Industrial
                    "LT", "SIEMENS", "ABB", "HAVELLS", "CROMPTON", "POLYCAB", "KEI", "VOLTAS", "BLUESTAR", "AMBER",
                    "TATASTEEL", "JSWSTEEL", "SAIL", "HINDALCO", "NATIONALUM", "JINDALSTEL", "APLAPOLLO", "RATNAMANI", "WELCORP", "KALYANKJIL",

                    # Oil & Gas
                    "RELIANCE", "ONGC", "GAIL", "IGL", "MGL", "GUJGASLTD", "PETRONET", "BPCL", "HPCL", "IOC",
                    "CASTROLIND", "GULFOILLUB", "HINDPETRO", "OIL", "GSPL", "ATGL", "MAHSEAMLES", "DEEPAKFERT", "CHAMBLFERT", "FACT",

                    # Cement & Construction
                    "ULTRACEMCO", "SHREECEM", "AMBUJACEM", "ACC", "RAMCOCEM", "JKCEMENT", "DALBHARAT", "HEIDELBERG", "ORIENTCEM", "STARCEMENT",
                    "DLF", "GODREJPROP", "PRESTIGE", "OBEROIRLTY", "SOBHA", "BRIGADE", "MAHLIFE", "SUNTECK", "KOLTEPATIL", "PURAVANKARA",

                    # Miscellaneous
                    "TATAMOTORS", "M&M", "MARUTI", "BAJAJ-AUTO", "HEROMOTOCO", "EICHERMOT", "TVSMOTOR", "ASHOKLEY", "BHARATFORG", "MRF",
                    "APOLLOTYRE", "CEATLTD", "BALKRISIND", "EXIDEIND", "AMARARAJA", "BOSCHLTD", "MOTHERSON", "ENDURANCE", "VARROC", "SOLARINDS"
                ]

                # Remove duplicates
                symbols = list(dict.fromkeys(symbols))

            except Exception as e:
                logger.error(f"Error preparing BSE symbols: {e}")
                # Fallback to a smaller hardcoded list
                symbols = [
                    "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "HINDUNILVR", "INFY", "HDFC", "ITC", "SBIN", "BHARTIARTL",
                    "KOTAKBANK", "LT", "BAJFINANCE", "AXISBANK", "ASIANPAINT", "MARUTI", "HCLTECH", "SUNPHARMA", "TITAN", "ULTRACEMCO",
                    "TATAMOTORS", "ADANIPORTS", "WIPRO", "NTPC", "ONGC", "JSWSTEEL", "TATASTEEL", "INDUSINDBK", "DRREDDY", "NESTLEIND",
                    "YESBANK", "PNB", "BANKBARODA", "ADANIENT", "ADANIPOWER", "ZOMATO", "PAYTM", "NYKAA", "IRCTC", "DMART"
                ]

        elif exchange == "NYSE":
            # For NYSE, we'll use a hardcoded list of major stocks
            symbols = [
                "AAPL", "MSFT", "AMZN", "GOOGL", "META", "TSLA", "BRK.B", "JPM", "JNJ", "V",
                "PG", "UNH", "HD", "BAC", "MA", "XOM", "DIS", "NVDA", "PYPL", "ADBE",
                "CRM", "CMCSA", "NFLX", "PEP", "KO", "INTC", "VZ", "T", "CSCO", "ABT",
                "MRK", "PFE", "NKE", "TMO", "WMT", "CVX", "ABBV", "AVGO", "MCD", "ACN",
                "MDT", "NEE", "LIN", "TXN", "PM", "DHR", "UNP", "BMY", "LLY", "AMT"
            ]

        elif exchange == "NASDAQ":
            # For NASDAQ, we'll use a hardcoded list of major stocks
            symbols = [
                "AAPL", "MSFT", "AMZN", "GOOGL", "META", "TSLA", "NVDA", "PYPL", "ADBE", "CMCSA",
                "NFLX", "PEP", "INTC", "CSCO", "AVGO", "TXN", "QCOM", "GILD", "MDLZ", "BKNG",
                "SBUX", "CHTR", "AMGN", "ISRG", "AMD", "TMUS", "REGN", "ILMN", "ATVI", "BIIB",
                "ADP", "VRTX", "CSX", "FISV", "AMAT", "ADI", "LRCX", "MNST", "ADSK", "ALGN",
                "CDNS", "ORLY", "CTAS", "DXCM", "IDXX", "KLAC", "MCHP", "MXIM", "PAYX", "SNPS"
            ]

        else:
            # For other exchanges, return a small set of common symbols
            symbols = ["AAPL", "MSFT", "AMZN", "GOOGL", "META", "TSLA", "BRK.B", "JPM", "JNJ", "V"]

        # No need to cache the symbols here as they're already cached by the symbol_fetcher

        return symbols
