"""
Data Optimizer Module for RapidTrader

This module provides functionality for optimizing data storage.
"""

import os
import logging
import sqlite3
import gzip
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd

# Configure logging
logger = logging.getLogger("data_optimizer")

def compress_file(file_path: str, delete_original: bool = True) -> Optional[str]:
    """
    Compress a file using gzip.
    
    Args:
        file_path: Path to the file to compress
        delete_original: Whether to delete the original file
        
    Returns:
        Path to the compressed file or None if compression failed
    """
    if not os.path.exists(file_path):
        logger.warning(f"File not found: {file_path}")
        return None
        
    try:
        # Compressed file path
        compressed_path = f"{file_path}.gz"
        
        # Compress the file
        with open(file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
                
        # Check if compression was successful
        if os.path.exists(compressed_path) and os.path.getsize(compressed_path) > 0:
            # Delete the original file if requested
            if delete_original:
                os.remove(file_path)
                
            logger.info(f"Compressed {file_path} to {compressed_path}")
            return compressed_path
        else:
            logger.error(f"Compression failed for {file_path}")
            
            # Clean up the compressed file if it exists
            if os.path.exists(compressed_path):
                os.remove(compressed_path)
                
            return None
    except Exception as e:
        logger.error(f"Error compressing {file_path}: {e}")
        
        # Clean up the compressed file if it exists
        if os.path.exists(f"{file_path}.gz"):
            os.remove(f"{file_path}.gz")
            
        return None

def decompress_file(file_path: str, delete_compressed: bool = True) -> Optional[str]:
    """
    Decompress a gzipped file.
    
    Args:
        file_path: Path to the compressed file
        delete_compressed: Whether to delete the compressed file
        
    Returns:
        Path to the decompressed file or None if decompression failed
    """
    if not os.path.exists(file_path) or not file_path.endswith(".gz"):
        logger.warning(f"Invalid compressed file: {file_path}")
        return None
        
    try:
        # Decompressed file path
        decompressed_path = file_path[:-3]  # Remove .gz extension
        
        # Decompress the file
        with gzip.open(file_path, 'rb') as f_in:
            with open(decompressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
                
        # Check if decompression was successful
        if os.path.exists(decompressed_path) and os.path.getsize(decompressed_path) > 0:
            # Delete the compressed file if requested
            if delete_compressed:
                os.remove(file_path)
                
            logger.info(f"Decompressed {file_path} to {decompressed_path}")
            return decompressed_path
        else:
            logger.error(f"Decompression failed for {file_path}")
            
            # Clean up the decompressed file if it exists
            if os.path.exists(decompressed_path):
                os.remove(decompressed_path)
                
            return None
    except Exception as e:
        logger.error(f"Error decompressing {file_path}: {e}")
        
        # Clean up the decompressed file if it exists
        if os.path.exists(file_path[:-3]):
            os.remove(file_path[:-3])
            
        return None

def compress_old_data(data_dir: str, days: int = 30, dry_run: bool = True) -> Dict[str, Any]:
    """
    Compress data files that haven't been modified in the specified number of days.
    
    Args:
        data_dir: Directory containing data files
        days: Number of days threshold
        dry_run: If True, only simulate the compression
        
    Returns:
        Dictionary with compression statistics
    """
    from data.data_cleanup import get_data_files
    
    stats = {
        "total_files": 0,
        "compressed_files": 0,
        "compression_ratio": 0,
        "space_saved": 0
    }
    
    # Get all data files
    files = get_data_files(data_dir)
    stats["total_files"] = len(files)
    
    # Calculate cutoff date
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # Process each file
    for file_path in files:
        # Skip already compressed files
        if file_path.endswith(".gz"):
            continue
            
        # Check if the file is older than the cutoff date
        mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        
        if mod_time < cutoff_date:
            # Get original file size
            original_size = os.path.getsize(file_path)
            
            if not dry_run:
                # Compress the file
                compressed_path = compress_file(file_path)
                
                if compressed_path:
                    # Get compressed file size
                    compressed_size = os.path.getsize(compressed_path)
                    
                    # Update statistics
                    stats["compressed_files"] += 1
                    stats["space_saved"] += (original_size - compressed_size)
            else:
                # Estimate compression ratio (typical for CSV files)
                estimated_compressed_size = original_size * 0.3
                
                # Update statistics
                stats["compressed_files"] += 1
                stats["space_saved"] += (original_size - estimated_compressed_size)
    
    # Calculate compression ratio
    if stats["compressed_files"] > 0:
        stats["compression_ratio"] = stats["space_saved"] / (stats["space_saved"] + stats["compressed_files"])
    
    return stats

def optimize_database(db_path: str) -> Dict[str, Any]:
    """
    Optimize the SQLite database.
    
    Args:
        db_path: Path to the SQLite database
        
    Returns:
        Dictionary with optimization statistics
    """
    stats = {
        "original_size": 0,
        "optimized_size": 0,
        "space_saved": 0,
        "success": False
    }
    
    if not os.path.exists(db_path):
        logger.warning(f"Database not found: {db_path}")
        return stats
        
    try:
        # Get original database size
        stats["original_size"] = os.path.getsize(db_path)
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Run VACUUM to optimize the database
        cursor.execute("VACUUM")
        
        # Run ANALYZE to update statistics
        cursor.execute("ANALYZE")
        
        # Commit changes and close connection
        conn.commit()
        conn.close()
        
        # Get optimized database size
        stats["optimized_size"] = os.path.getsize(db_path)
        
        # Calculate space saved
        stats["space_saved"] = stats["original_size"] - stats["optimized_size"]
        stats["success"] = True
        
        logger.info(f"Optimized database {db_path}")
    except Exception as e:
        logger.error(f"Error optimizing database {db_path}: {e}")
    
    return stats
