"""
Data Manager for RapidTrader

This module provides a data manager for handling historical data.
"""

import logging
import os
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any, Tuple
import pandas as pd

from data.base_fetcher import BaseDataFetcher

# Configure logging
logger = logging.getLogger("data_manager")

class DataManager:
    """
    Data manager for handling historical data.

    This class provides methods for fetching, storing, and managing historical data.
    """

    def __init__(self,
                data_fetcher: BaseDataFetcher,
                db_path: str = "userdata/historical_data/rapidtrader.db",
                csv_dir: str = "userdata/historical_data"):
        """
        Initialize the data manager.

        Args:
            data_fetcher: Data fetcher to use
            db_path: Path to the SQLite database
            csv_dir: Directory for CSV files
        """
        self.data_fetcher = data_fetcher
        self.db_path = db_path
        self.csv_dir = csv_dir

        # Create directories if they don't exist
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        os.makedirs(csv_dir, exist_ok=True)

        # Initialize the database
        self._init_db()

    def _init_db(self) -> None:
        """Initialize the SQLite database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Create tables if they don't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS symbols (
            id INTEGER PRIMARY KEY,
            symbol TEXT NOT NULL,
            exchange TEXT,
            last_updated TIMESTAMP,
            UNIQUE(symbol, exchange)
        )
        ''')

        cursor.execute('''
        CREATE TABLE IF NOT EXISTS historical_data (
            id INTEGER PRIMARY KEY,
            symbol_id INTEGER,
            timeframe TEXT NOT NULL,
            start_date TIMESTAMP,
            end_date TIMESTAMP,
            csv_path TEXT NOT NULL,
            FOREIGN KEY (symbol_id) REFERENCES symbols(id),
            UNIQUE(symbol_id, timeframe)
        )
        ''')

        conn.commit()
        conn.close()

    def _get_symbol_id(self, symbol: str, exchange: Optional[str] = None) -> int:
        """
        Get the ID of a symbol from the database.

        Args:
            symbol: Trading symbol
            exchange: Exchange name

        Returns:
            Symbol ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check if the symbol exists
        if exchange:
            cursor.execute(
                "SELECT id FROM symbols WHERE symbol = ? AND exchange = ?",
                (symbol, exchange)
            )
        else:
            cursor.execute(
                "SELECT id FROM symbols WHERE symbol = ? AND exchange IS NULL",
                (symbol,)
            )

        result = cursor.fetchone()

        if result:
            # Symbol exists, return its ID
            symbol_id = result[0]
        else:
            # Symbol doesn't exist, insert it
            now = datetime.now()
            if exchange:
                cursor.execute(
                    "INSERT INTO symbols (symbol, exchange, last_updated) VALUES (?, ?, ?)",
                    (symbol, exchange, now)
                )
            else:
                cursor.execute(
                    "INSERT INTO symbols (symbol, last_updated) VALUES (?, ?)",
                    (symbol, now)
                )

            symbol_id = cursor.lastrowid

        conn.commit()
        conn.close()

        return symbol_id

    def _update_historical_data_record(self,
                                      symbol_id: int,
                                      timeframe: str,
                                      start_date: datetime,
                                      end_date: datetime,
                                      csv_path: str) -> None:
        """
        Update a historical data record in the database.

        Args:
            symbol_id: Symbol ID
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            csv_path: Path to the CSV file
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check if a record exists for this symbol and timeframe
        cursor.execute(
            "SELECT id FROM historical_data WHERE symbol_id = ? AND timeframe = ?",
            (symbol_id, timeframe)
        )

        result = cursor.fetchone()

        if result:
            # Record exists, update it
            cursor.execute(
                "UPDATE historical_data SET start_date = ?, end_date = ?, csv_path = ? WHERE id = ?",
                (start_date, end_date, csv_path, result[0])
            )
        else:
            # Record doesn't exist, insert it
            cursor.execute(
                "INSERT INTO historical_data (symbol_id, timeframe, start_date, end_date, csv_path) VALUES (?, ?, ?, ?, ?)",
                (symbol_id, timeframe, start_date, end_date, csv_path)
            )

        conn.commit()
        conn.close()

    def _get_historical_data_record(self, symbol_id: int, timeframe: str) -> Optional[Tuple]:
        """
        Get a historical data record from the database.

        Args:
            symbol_id: Symbol ID
            timeframe: Timeframe for the data

        Returns:
            Tuple with (id, start_date, end_date, csv_path) or None if not found
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            "SELECT id, start_date, end_date, csv_path FROM historical_data WHERE symbol_id = ? AND timeframe = ?",
            (symbol_id, timeframe)
        )

        result = cursor.fetchone()
        conn.close()

        return result

    def get_ohlcv(self,
                 symbol: str,
                 timeframe: str,
                 start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None,
                 exchange: Optional[str] = None,
                 force_refresh: bool = False) -> pd.DataFrame:
        """
        Get OHLCV data for a symbol.

        Args:
            symbol: Trading symbol
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from
            force_refresh: Whether to force a refresh from the data source

        Returns:
            DataFrame with OHLCV data
        """
        # Get the symbol ID
        symbol_id = self._get_symbol_id(symbol, exchange)

        # Check if we have data for this symbol and timeframe
        if not force_refresh:
            record = self._get_historical_data_record(symbol_id, timeframe)

            if record:
                _, db_start_date, db_end_date, csv_path = record

                # Convert string dates to datetime
                if isinstance(db_start_date, str):
                    db_start_date = datetime.fromisoformat(db_start_date)
                if isinstance(db_end_date, str):
                    db_end_date = datetime.fromisoformat(db_end_date)

                # Check if we have data for the requested date range
                have_data = True

                if start_date and db_start_date and start_date < db_start_date:
                    have_data = False

                if end_date and db_end_date and end_date > db_end_date:
                    have_data = False

                if have_data and os.path.exists(csv_path):
                    # Load data from CSV
                    df = pd.read_csv(csv_path, index_col=0, parse_dates=True)

                    # Filter by date range if specified
                    if start_date:
                        df = df[df.index >= pd.Timestamp(start_date)]
                    if end_date:
                        df = df[df.index <= pd.Timestamp(end_date)]

                    return df

        # Fetch data from the data fetcher
        df = self.data_fetcher.fetch_ohlcv(
            symbol=symbol,
            timeframe=timeframe,
            start_date=start_date,
            end_date=end_date,
            exchange=exchange
        )

        if not df.empty:
            # Save to CSV
            csv_path = os.path.join(self.csv_dir, f"{symbol}_{timeframe}.csv")
            if exchange:
                csv_path = os.path.join(self.csv_dir, f"{exchange}_{symbol}_{timeframe}.csv")

            df.to_csv(csv_path)

            # Update the database
            self._update_historical_data_record(
                symbol_id=symbol_id,
                timeframe=timeframe,
                start_date=df.index.min().strftime("%Y-%m-%d %H:%M:%S"),
                end_date=df.index.max().strftime("%Y-%m-%d %H:%M:%S"),
                csv_path=csv_path
            )

        return df

    def get_multiple_ohlcv(self,
                          symbols: List[str],
                          timeframe: str,
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          exchange: Optional[str] = None,
                          force_refresh: bool = False) -> Dict[str, pd.DataFrame]:
        """
        Get OHLCV data for multiple symbols.

        Args:
            symbols: List of trading symbols
            timeframe: Timeframe for the data
            start_date: Start date for the data
            end_date: End date for the data
            exchange: Exchange to fetch data from
            force_refresh: Whether to force a refresh from the data source

        Returns:
            Dictionary mapping symbols to DataFrames with OHLCV data
        """
        result = {}

        # Check which symbols we need to fetch
        symbols_to_fetch = []

        for symbol in symbols:
            # Get the symbol ID
            symbol_id = self._get_symbol_id(symbol, exchange)

            # Check if we have data for this symbol and timeframe
            if not force_refresh:
                record = self._get_historical_data_record(symbol_id, timeframe)

                if record:
                    _, db_start_date, db_end_date, csv_path = record

                    # Convert string dates to datetime
                    if isinstance(db_start_date, str):
                        db_start_date = datetime.fromisoformat(db_start_date)
                    if isinstance(db_end_date, str):
                        db_end_date = datetime.fromisoformat(db_end_date)

                    # Check if we have data for the requested date range
                    have_data = True

                    if start_date and db_start_date and start_date < db_start_date:
                        have_data = False

                    if end_date and db_end_date and end_date > db_end_date:
                        have_data = False

                    if have_data and os.path.exists(csv_path):
                        # Load data from CSV
                        df = pd.read_csv(csv_path, index_col=0, parse_dates=True)

                        # Filter by date range if specified
                        if start_date:
                            df = df[df.index >= pd.Timestamp(start_date)]
                        if end_date:
                            df = df[df.index <= pd.Timestamp(end_date)]

                        result[symbol] = df
                        continue

            # If we get here, we need to fetch this symbol
            symbols_to_fetch.append(symbol)

        if symbols_to_fetch:
            # Fetch data for the remaining symbols
            fetched_data = self.data_fetcher.fetch_multiple_ohlcv(
                symbols=symbols_to_fetch,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                exchange=exchange
            )

            # Process each fetched symbol
            for symbol, df in fetched_data.items():
                if not df.empty:
                    # Save to CSV
                    csv_path = os.path.join(self.csv_dir, f"{symbol}_{timeframe}.csv")
                    if exchange:
                        csv_path = os.path.join(self.csv_dir, f"{exchange}_{symbol}_{timeframe}.csv")

                    df.to_csv(csv_path)

                    # Update the database
                    symbol_id = self._get_symbol_id(symbol, exchange)
                    self._update_historical_data_record(
                        symbol_id=symbol_id,
                        timeframe=timeframe,
                        start_date=df.index.min().strftime("%Y-%m-%d %H:%M:%S"),
                        end_date=df.index.max().strftime("%Y-%m-%d %H:%M:%S"),
                        csv_path=csv_path
                    )

                result[symbol] = df

        return result

    def get_available_symbols(self, exchange: Optional[str] = None) -> List[str]:
        """
        Get the list of available symbols.

        Args:
            exchange: Exchange name

        Returns:
            List of available symbols
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        if exchange:
            cursor.execute("SELECT symbol FROM symbols WHERE exchange = ?", (exchange,))
        else:
            cursor.execute("SELECT symbol FROM symbols")

        symbols = [row[0] for row in cursor.fetchall()]
        conn.close()

        return symbols

    def get_available_timeframes(self) -> List[str]:
        """
        Get the list of available timeframes.

        Returns:
            List of available timeframes
        """
        return self.data_fetcher.get_available_timeframes()

    def get_exchange_info(self, exchange: str) -> Dict[str, Any]:
        """
        Get information about an exchange.

        Args:
            exchange: Exchange name

        Returns:
            Dictionary with exchange information
        """
        return self.data_fetcher.get_exchange_info(exchange)
