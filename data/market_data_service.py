#!/usr/bin/env python3
"""
Market Data Service for RapidTrader

This service fetches data from yfinance and serves it as live data for dry run simulation.
It provides realistic market data with 15-20 minute delay, perfect for paper trading.
"""

import yfinance as yf
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import threading
import time
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class MarketDataService:
    """
    Market Data Service that fetches yfinance data and serves it as live data.
    
    Features:
    - Fetches real market data from yfinance
    - Caches data for performance
    - Serves data in DhanHQ API format
    - Auto-refreshes data periodically
    - Handles Indian stock symbols (NSE/BSE)
    """
    
    def __init__(self, cache_duration: int = 300, auto_refresh: bool = True):
        """
        Initialize Market Data Service.
        
        Args:
            cache_duration: Cache duration in seconds (default: 5 minutes)
            auto_refresh: Enable automatic data refresh
        """
        self.cache_duration = cache_duration
        self.auto_refresh = auto_refresh
        self.cache = {}
        self.last_fetch = {}
        self.symbol_mapping = self._load_symbol_mapping()
        self.running = False
        self.refresh_thread = None
        
        logger.info(f"MarketDataService initialized - Cache: {cache_duration}s, Auto-refresh: {auto_refresh}")
    
    def _load_symbol_mapping(self) -> Dict[str, str]:
        """Load symbol mapping for Indian stocks."""
        # Common NSE to yfinance symbol mapping
        mapping = {
            "RELIANCE": "RELIANCE.NS",
            "TCS": "TCS.NS", 
            "HDFCBANK": "HDFCBANK.NS",
            "INFY": "INFY.NS",
            "HINDUNILVR": "HINDUNILVR.NS",
            "ICICIBANK": "ICICIBANK.NS",
            "KOTAKBANK": "KOTAKBANK.NS",
            "BHARTIARTL": "BHARTIARTL.NS",
            "ITC": "ITC.NS",
            "SBIN": "SBIN.NS",
            "ASIANPAINT": "ASIANPAINT.NS",
            "MARUTI": "MARUTI.NS",
            "BAJFINANCE": "BAJFINANCE.NS",
            "HCLTECH": "HCLTECH.NS",
            "WIPRO": "WIPRO.NS",
            "ULTRACEMCO": "ULTRACEMCO.NS",
            "TITAN": "TITAN.NS",
            "NESTLEIND": "NESTLEIND.NS",
            "POWERGRID": "POWERGRID.NS",
            "NTPC": "NTPC.NS"
        }
        
        # Try to load additional mappings from file
        try:
            mapping_file = Path(__file__).parent / "symbol_mapping.json"
            if mapping_file.exists():
                with open(mapping_file, 'r') as f:
                    file_mapping = json.load(f)
                    mapping.update(file_mapping)
                logger.info(f"Loaded {len(file_mapping)} additional symbol mappings")
        except Exception as e:
            logger.warning(f"Could not load symbol mapping file: {e}")
        
        logger.info(f"Symbol mapping loaded: {len(mapping)} symbols")
        return mapping
    
    def _get_yfinance_symbol(self, symbol: str, exchange: str = "NSE_EQ") -> str:
        """Convert trading symbol to yfinance format."""
        # Remove any suffixes
        clean_symbol = symbol.split('-')[0].split('.')[0]
        
        # Check mapping first
        if clean_symbol in self.symbol_mapping:
            return self.symbol_mapping[clean_symbol]
        
        # Default mapping based on exchange
        if exchange in ["NSE_EQ", "NSE_FNO"]:
            return f"{clean_symbol}.NS"
        elif exchange in ["BSE_EQ", "BSE_FNO"]:
            return f"{clean_symbol}.BO"
        else:
            return f"{clean_symbol}.NS"  # Default to NSE
    
    def _is_cache_valid(self, symbol: str) -> bool:
        """Check if cached data is still valid."""
        if symbol not in self.cache or symbol not in self.last_fetch:
            return False
        
        time_diff = datetime.now() - self.last_fetch[symbol]
        return time_diff.total_seconds() < self.cache_duration
    
    def _fetch_live_data(self, symbol: str, exchange: str = "NSE_EQ") -> Optional[Dict[str, Any]]:
        """Fetch live data from yfinance."""
        try:
            yf_symbol = self._get_yfinance_symbol(symbol, exchange)
            logger.debug(f"Fetching data for {symbol} -> {yf_symbol}")
            
            # Create ticker object
            ticker = yf.Ticker(yf_symbol)
            
            # Get current info and history
            info = ticker.info
            hist = ticker.history(period="2d", interval="1m")
            
            if hist.empty:
                logger.warning(f"No historical data available for {yf_symbol}")
                return None
            
            # Get latest data point
            latest = hist.iloc[-1]
            previous_close = info.get('previousClose', latest['Close'])
            
            # Calculate change
            current_price = float(latest['Close'])
            change = current_price - previous_close
            change_percent = (change / previous_close * 100) if previous_close > 0 else 0
            
            # Format data in DhanHQ style
            quote_data = {
                "symbol": symbol,
                "ltp": round(current_price, 2),
                "open": round(float(latest['Open']), 2),
                "high": round(float(latest['High']), 2),
                "low": round(float(latest['Low']), 2),
                "close": round(previous_close, 2),
                "volume": int(latest['Volume']),
                "change": round(change, 2),
                "changePercent": round(change_percent, 2),
                "timestamp": datetime.now().isoformat(),
                "source": "yfinance",
                "delay_minutes": 15  # Indicate data delay
            }
            
            logger.debug(f"Fetched quote for {symbol}: LTP=₹{quote_data['ltp']:.2f}")
            return quote_data
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            return None
    
    def get_quote(self, symbol: str, exchange: str = "NSE_EQ") -> Dict[str, Any]:
        """
        Get live quote for a symbol.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange segment
            
        Returns:
            Dictionary with quote data or error
        """
        cache_key = f"{symbol}_{exchange}"
        
        # Check cache first
        if self._is_cache_valid(cache_key):
            logger.debug(f"Returning cached data for {symbol}")
            return self.cache[cache_key]
        
        # Fetch fresh data
        quote_data = self._fetch_live_data(symbol, exchange)
        
        if quote_data:
            # Cache the data
            self.cache[cache_key] = quote_data
            self.last_fetch[cache_key] = datetime.now()
            return quote_data
        else:
            return {"error": f"Failed to fetch quote for {symbol}"}
    
    def get_multiple_quotes(self, symbols: List[str], exchange: str = "NSE_EQ") -> Dict[str, Any]:
        """Get quotes for multiple symbols."""
        results = {}
        
        for symbol in symbols:
            quote = self.get_quote(symbol, exchange)
            results[symbol] = quote
        
        return results
    
    def get_ohlc(self, symbol: str, exchange: str = "NSE_EQ", 
                 interval: str = "1d", count: int = 100) -> Dict[str, Any]:
        """
        Get OHLC data for a symbol.
        
        Args:
            symbol: Trading symbol
            exchange: Exchange segment
            interval: Data interval (1m, 5m, 15m, 1h, 1d)
            count: Number of candles to fetch
            
        Returns:
            Dictionary with OHLC data
        """
        try:
            yf_symbol = self._get_yfinance_symbol(symbol, exchange)
            
            # Map interval to yfinance format
            interval_map = {
                "1m": "1m", "5m": "5m", "15m": "15m", 
                "1h": "1h", "1d": "1d"
            }
            yf_interval = interval_map.get(interval, "1d")
            
            # Calculate period based on count and interval
            if interval in ["1m", "5m", "15m"]:
                period = "5d"  # Short intervals need recent period
            elif interval == "1h":
                period = "1mo"
            else:
                period = "1y"
            
            ticker = yf.Ticker(yf_symbol)
            hist = ticker.history(period=period, interval=yf_interval)
            
            if hist.empty:
                return {"error": f"No OHLC data available for {symbol}"}
            
            # Take last 'count' records
            hist = hist.tail(count)
            
            # Format data
            ohlc_data = []
            for index, row in hist.iterrows():
                ohlc_data.append({
                    "timestamp": index.isoformat(),
                    "open": round(float(row['Open']), 2),
                    "high": round(float(row['High']), 2),
                    "low": round(float(row['Low']), 2),
                    "close": round(float(row['Close']), 2),
                    "volume": int(row['Volume'])
                })
            
            return {
                "symbol": symbol,
                "interval": interval,
                "data": ohlc_data,
                "count": len(ohlc_data),
                "source": "yfinance"
            }
            
        except Exception as e:
            logger.error(f"Failed to get OHLC for {symbol}: {e}")
            return {"error": str(e)}
    
    def start_auto_refresh(self):
        """Start automatic data refresh in background."""
        if self.auto_refresh and not self.running:
            self.running = True
            self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
            self.refresh_thread.start()
            logger.info("Auto-refresh started")
    
    def stop_auto_refresh(self):
        """Stop automatic data refresh."""
        self.running = False
        if self.refresh_thread:
            self.refresh_thread.join(timeout=5)
        logger.info("Auto-refresh stopped")
    
    def _refresh_loop(self):
        """Background refresh loop."""
        while self.running:
            try:
                # Refresh cached symbols that are getting old
                current_time = datetime.now()
                symbols_to_refresh = []
                
                for cache_key, last_fetch_time in self.last_fetch.items():
                    time_diff = current_time - last_fetch_time
                    if time_diff.total_seconds() > (self.cache_duration * 0.8):  # Refresh at 80% of cache duration
                        symbols_to_refresh.append(cache_key)
                
                for cache_key in symbols_to_refresh:
                    symbol, exchange = cache_key.split('_', 1)
                    self.get_quote(symbol, exchange)
                
                # Sleep for 30 seconds before next refresh cycle
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in refresh loop: {e}")
                time.sleep(60)  # Wait longer on error
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cached_symbols": len(self.cache),
            "cache_duration": self.cache_duration,
            "auto_refresh": self.auto_refresh,
            "running": self.running,
            "symbol_mappings": len(self.symbol_mapping)
        }

# Global market data service instance
_market_data_service = None

def get_market_data_service() -> MarketDataService:
    """Get global market data service instance."""
    global _market_data_service
    if _market_data_service is None:
        _market_data_service = MarketDataService()
        _market_data_service.start_auto_refresh()
    return _market_data_service
