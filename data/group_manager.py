"""
Symbol Group Manager for RapidTrader

This module provides functionality for managing symbol groups.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any

# Configure logging
logger = logging.getLogger("group_manager")

# Constants
GROUPS_FILE = os.path.join("data", "symbols", "groups.json")

def load_groups() -> Dict[str, Any]:
    """
    Load symbol groups from the JSON file.

    Returns:
        Dictionary containing group definitions
    """
    try:
        if os.path.exists(GROUPS_FILE):
            with open(GROUPS_FILE, "r") as f:
                data = json.load(f)
            
            if "groups" in data:
                logger.info(f"Loaded {len(data['groups'])} symbol groups from {GROUPS_FILE}")
                return data
        
        logger.warning(f"Groups file not found or invalid: {GROUPS_FILE}")
        return {"groups": {}}
    except Exception as e:
        logger.error(f"Error loading groups: {e}")
        return {"groups": {}}

def get_group_names() -> List[str]:
    """
    Get the list of available group names.

    Returns:
        List of group names
    """
    data = load_groups()
    return list(data.get("groups", {}).keys())

def get_group_symbols(group_name: str) -> List[str]:
    """
    Get the symbols for a specific group.

    Args:
        group_name: Name of the group

    Returns:
        List of symbols in the group
    """
    data = load_groups()
    group = data.get("groups", {}).get(group_name, {})
    return group.get("symbols", [])

def get_group_exchange(group_name: str) -> Optional[str]:
    """
    Get the exchange for a specific group.

    Args:
        group_name: Name of the group

    Returns:
        Exchange name or None if not specified
    """
    data = load_groups()
    group = data.get("groups", {}).get(group_name, {})
    return group.get("exchange")

def get_group_description(group_name: str) -> str:
    """
    Get the description for a specific group.

    Args:
        group_name: Name of the group

    Returns:
        Group description or empty string if not found
    """
    data = load_groups()
    group = data.get("groups", {}).get(group_name, {})
    return group.get("description", "")

def add_group(group_name: str, description: str, exchange: str, symbols: List[str]) -> bool:
    """
    Add a new symbol group.

    Args:
        group_name: Name of the group
        description: Description of the group
        exchange: Exchange for the symbols
        symbols: List of symbols in the group

    Returns:
        True if successful, False otherwise
    """
    try:
        data = load_groups()
        
        # Add the new group
        data["groups"][group_name] = {
            "description": description,
            "exchange": exchange,
            "symbols": symbols
        }
        
        # Save the updated groups
        with open(GROUPS_FILE, "w") as f:
            json.dump(data, f, indent=2)
        
        logger.info(f"Added group {group_name} with {len(symbols)} symbols")
        return True
    except Exception as e:
        logger.error(f"Error adding group {group_name}: {e}")
        return False

def remove_group(group_name: str) -> bool:
    """
    Remove a symbol group.

    Args:
        group_name: Name of the group to remove

    Returns:
        True if successful, False otherwise
    """
    try:
        data = load_groups()
        
        if group_name in data["groups"]:
            # Remove the group
            del data["groups"][group_name]
            
            # Save the updated groups
            with open(GROUPS_FILE, "w") as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Removed group {group_name}")
            return True
        else:
            logger.warning(f"Group {group_name} not found")
            return False
    except Exception as e:
        logger.error(f"Error removing group {group_name}: {e}")
        return False

def update_group_symbols(group_name: str, symbols: List[str]) -> bool:
    """
    Update the symbols for a group.

    Args:
        group_name: Name of the group
        symbols: New list of symbols

    Returns:
        True if successful, False otherwise
    """
    try:
        data = load_groups()
        
        if group_name in data["groups"]:
            # Update the symbols
            data["groups"][group_name]["symbols"] = symbols
            
            # Save the updated groups
            with open(GROUPS_FILE, "w") as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Updated group {group_name} with {len(symbols)} symbols")
            return True
        else:
            logger.warning(f"Group {group_name} not found")
            return False
    except Exception as e:
        logger.error(f"Error updating group {group_name}: {e}")
        return False
