#!/usr/bin/env python3
"""
NSE Symbols Updater

This script downloads and updates the list of NSE symbols from the NSE website.
It saves the symbols to a JSON file in the data/symbols directory.
"""

import os
import sys
import json
import logging
import requests
import pandas as pd
from datetime import datetime
from io import StringIO

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("nse_symbols")

# Constants
SYMBOLS_DIR = os.path.join("data", "symbols")
NSE_SYMBOLS_FILE = os.path.join(SYMBOLS_DIR, "nse_symbols.json")
NSE_EQUITY_URL = "https://www1.nseindia.com/content/equities/EQUITY_L.csv"

def download_nse_symbols():
    """
    Download NSE symbols from the NSE website.
    
    Returns:
        List of symbols
    """
    try:
        # Set headers to mimic a browser request
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0"
        }
        
        # Download the CSV file
        logger.info(f"Downloading NSE symbols from {NSE_EQUITY_URL}")
        response = requests.get(NSE_EQUITY_URL, headers=headers)
        
        if response.status_code != 200:
            logger.error(f"Failed to download NSE symbols: {response.status_code}")
            return []
        
        # Parse the CSV file
        csv_data = StringIO(response.text)
        df = pd.read_csv(csv_data)
        
        # Extract the symbols
        symbols = []
        for _, row in df.iterrows():
            symbol = row["SYMBOL"]
            series = row["SERIES"]
            
            # Only include EQ series
            if series == "EQ":
                symbols.append(symbol)
        
        logger.info(f"Found {len(symbols)} NSE symbols")
        return symbols
    except Exception as e:
        logger.error(f"Error downloading NSE symbols: {e}")
        return []

def save_symbols(symbols):
    """
    Save symbols to a JSON file.
    
    Args:
        symbols: List of symbols
    
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(SYMBOLS_DIR, exist_ok=True)
        
        # Create a backup of the existing file
        if os.path.exists(NSE_SYMBOLS_FILE):
            backup_file = f"{NSE_SYMBOLS_FILE}.bak"
            import shutil
            shutil.copy2(NSE_SYMBOLS_FILE, backup_file)
            logger.info(f"Created backup of existing symbols file: {backup_file}")
        
        # Save the symbols
        data = {
            "symbols": symbols,
            "count": len(symbols),
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        with open(NSE_SYMBOLS_FILE, "w") as f:
            json.dump(data, f, indent=4)
        
        logger.info(f"Saved {len(symbols)} symbols to {NSE_SYMBOLS_FILE}")
        return True
    except Exception as e:
        logger.error(f"Error saving symbols: {e}")
        return False

def load_symbols():
    """
    Load symbols from the JSON file.
    
    Returns:
        List of symbols
    """
    try:
        if os.path.exists(NSE_SYMBOLS_FILE):
            with open(NSE_SYMBOLS_FILE, "r") as f:
                data = json.load(f)
            
            if "symbols" in data:
                logger.info(f"Loaded {len(data['symbols'])} symbols from {NSE_SYMBOLS_FILE}")
                return data["symbols"]
        
        logger.warning(f"Symbol file not found: {NSE_SYMBOLS_FILE}")
        return []
    except Exception as e:
        logger.error(f"Error loading symbols: {e}")
        return []

def refresh_symbols():
    """
    Download and update NSE symbols.
    
    Returns:
        Number of symbols updated
    """
    # Download symbols
    symbols = download_nse_symbols()
    
    if not symbols:
        logger.error("No symbols downloaded")
        return 0
    
    # Save symbols
    if save_symbols(symbols):
        return len(symbols)
    else:
        return 0

def get_symbols():
    """
    Get NSE symbols.
    
    Returns:
        List of symbols
    """
    symbols = load_symbols()
    
    if not symbols:
        logger.warning("No symbols found, downloading...")
        refresh_symbols()
        symbols = load_symbols()
    
    return symbols

def main():
    """
    Main function.
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="NSE Symbols Updater")
    parser.add_argument("--refresh", action="store_true", help="Refresh symbols")
    parser.add_argument("--list", action="store_true", help="List symbols")
    args = parser.parse_args()
    
    if args.refresh:
        count = refresh_symbols()
        logger.info(f"Updated {count} symbols")
    elif args.list:
        symbols = get_symbols()
        logger.info(f"Found {len(symbols)} symbols")
        for symbol in symbols:
            print(symbol)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
