{"name": "rapidtrader-frontend", "version": "1.0.0", "description": "RapidTrader Frontend - React-based UI with lazy loading", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "axios": "^1.4.0", "socket.io-client": "^4.7.2", "recharts": "^2.7.2", "antd": "^5.8.4", "@ant-design/icons": "^5.2.5", "moment": "^2.29.4", "lodash": "^4.17.21", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "serve": "npx serve -s build -l 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.195", "@types/node": "^20.4.5", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "typescript": "^5.1.6"}, "proxy": "http://localhost:8000"}