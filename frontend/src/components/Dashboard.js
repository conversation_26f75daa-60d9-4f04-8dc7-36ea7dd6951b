import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Button } from 'antd';
import { 
  DollarOutlined, 
  TrophyOutlined, 
  RocketOutlined,
  ReloadOutlined 
} from '@ant-design/icons';
import io from 'socket.io-client';
import axios from 'axios';

const Dashboard = () => {
  const [systemStatus, setSystemStatus] = useState({});
  const [containers, setContainers] = useState([]);
  const [pnlData, setPnlData] = useState({});
  const [loading, setLoading] = useState(true);
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    // Initialize WebSocket connection
    const newSocket = io('ws://localhost:8000');
    setSocket(newSocket);

    // Listen for real-time updates
    newSocket.on('connect', () => {
      console.log('Connected to API Gateway');
    });

    newSocket.on('system_status', (data) => {
      setSystemStatus(data);
    });

    // Initial data load
    loadDashboardData();

    return () => {
      if (newSocket) {
        newSocket.disconnect();
      }
    };
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load containers
      const containersResponse = await axios.get('/api/containers');
      setContainers(containersResponse.data.containers || []);

      // Load P&L data
      const pnlResponse = await axios.get('/api/pnl/aggregated');
      setPnlData(pnlResponse.data.pnl || {});

      // Load system status
      const statusResponse = await axios.get('/api/health');
      setSystemStatus(statusResponse.data);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const containerColumns = [
    {
      title: 'Container Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <strong>{text}</strong>
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'running' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Type',
      dataIndex: 'labels',
      key: 'type',
      render: (labels) => labels?.['rapidtrader.type'] || 'Unknown'
    },
    {
      title: 'Strategy',
      dataIndex: 'labels',
      key: 'strategy',
      render: (labels) => labels?.['rapidtrader.strategy'] || 'N/A'
    },
    {
      title: 'Created',
      dataIndex: 'created',
      key: 'created',
      render: (created) => new Date(created).toLocaleString()
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: 16 
          }}>
            <h1>🚀 RapidTrader Dashboard</h1>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={loadDashboardData}
              loading={loading}
            >
              Refresh
            </Button>
          </div>
        </Col>
      </Row>

      {/* Key Metrics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total P&L"
              value={pnlData.total_pnl || 0}
              precision={2}
              valueStyle={{ 
                color: (pnlData.total_pnl || 0) >= 0 ? '#3f8600' : '#cf1322' 
              }}
              prefix={<DollarOutlined />}
              suffix="INR"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Containers"
              value={systemStatus.containers?.running || 0}
              valueStyle={{ color: '#1890ff' }}
              prefix={<RocketOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Trades"
              value={pnlData.summary?.total_trades || 0}
              valueStyle={{ color: '#722ed1' }}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Win Rate"
              value={pnlData.summary?.win_rate || 0}
              precision={1}
              valueStyle={{ color: '#52c41a' }}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* Container Status */}
      <Row gutter={16}>
        <Col span={24}>
          <Card 
            title="Container Status" 
            extra={
              <Tag color="blue">
                {containers.length} Total
              </Tag>
            }
          >
            <Table
              columns={containerColumns}
              dataSource={containers}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* P&L Summary */}
      {pnlData.containers && pnlData.containers.length > 0 && (
        <Row gutter={16} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="P&L by Strategy">
              <Table
                columns={[
                  {
                    title: 'Strategy',
                    dataIndex: 'strategy',
                    key: 'strategy'
                  },
                  {
                    title: 'Total P&L',
                    dataIndex: 'total_pnl',
                    key: 'total_pnl',
                    render: (value) => (
                      <span style={{ 
                        color: value >= 0 ? '#3f8600' : '#cf1322',
                        fontWeight: 'bold'
                      }}>
                        ₹{value.toFixed(2)}
                      </span>
                    )
                  },
                  {
                    title: 'Total Trades',
                    dataIndex: 'total_trades',
                    key: 'total_trades'
                  },
                  {
                    title: 'Win Rate',
                    dataIndex: 'win_rate',
                    key: 'win_rate',
                    render: (value) => `${value.toFixed(1)}%`
                  }
                ]}
                dataSource={pnlData.containers}
                rowKey="strategy"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default Dashboard;
