import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout, Spin } from 'antd';
import './App.css';

// Lazy load components for better performance
const Dashboard = lazy(() => import('./components/Dashboard'));
const ContainerManager = lazy(() => import('./components/ContainerManager'));
const LogViewer = lazy(() => import('./components/LogViewer'));
const PnLAnalyzer = lazy(() => import('./components/PnLAnalyzer'));
const StrategyManager = lazy(() => import('./components/StrategyManager'));
const Settings = lazy(() => import('./components/Settings'));

const { Header, Content, Sider } = Layout;

// Loading component
const LoadingSpinner = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '200px' 
  }}>
    <Spin size="large" tip="Loading..." />
  </div>
);

function App() {
  return (
    <div className="App">
      <Router>
        <Layout style={{ minHeight: '100vh' }}>
          <Header style={{ 
            background: '#001529', 
            color: 'white', 
            fontSize: '20px',
            fontWeight: 'bold',
            padding: '0 24px'
          }}>
            🚀 RapidTrader - Production Dashboard
          </Header>
          
          <Layout>
            <Sider width={200} style={{ background: '#fff' }}>
              {/* Navigation will be added here */}
            </Sider>
            
            <Layout style={{ padding: '24px' }}>
              <Content style={{ 
                background: '#fff', 
                padding: 24, 
                margin: 0, 
                minHeight: 280 
              }}>
                <Suspense fallback={<LoadingSpinner />}>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/containers" element={<ContainerManager />} />
                    <Route path="/logs" element={<LogViewer />} />
                    <Route path="/pnl" element={<PnLAnalyzer />} />
                    <Route path="/strategies" element={<StrategyManager />} />
                    <Route path="/settings" element={<Settings />} />
                  </Routes>
                </Suspense>
              </Content>
            </Layout>
          </Layout>
        </Layout>
      </Router>
    </div>
  );
}

export default App;
