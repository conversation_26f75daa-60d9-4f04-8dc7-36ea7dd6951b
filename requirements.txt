aiohttp==3.9.3
aiosignal==1.3.2
asyncio==3.4.3
attrs==25.3.0
aws-lambda-powertools==1.25.5
aws-xray-sdk==2.14.0
beautifulsoup4==4.13.4
boto3==1.38.23
botocore==1.38.23
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.0
colorama==0.4.6
contourpy==1.3.2
cryptography==45.0.2
curl_cffi==0.11.1
cycler==0.12.1
dhanhq==2.0.2
fastjsonschema==2.21.1
fonttools==4.58.0
frozendict==2.4.6
frozenlist==1.6.0
fyers_apiv3==3.1.7
idna==3.10
jmespath==0.10.0
kiwisolver==1.4.8
markdown-it-py==3.0.0
matplotlib==3.10.3
mdurl==0.1.2
multidict==6.4.4
multitasking==0.0.11
numpy==1.26.4
packaging==25.0
pandas==2.2.3
pandas_ta==0.3.14b0
peewee==3.18.1
pillow==11.2.1
platformdirs==4.3.8
propcache==0.3.1
protobuf==5.29.3
psutil==7.0.0
pycparser==2.22
Pygments==2.19.1
pyOpenSSL==25.1.0
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
requests==2.31.0
rich==14.0.0
s3transfer==0.13.0
setuptools==80.7.1
six==1.17.0
soupsieve==2.7
# ta-lib==0.6.3  # Commented out due to Docker build issues, using pandas_ta instead
tabulate==0.9.0
typing_extensions==4.13.2
tzdata==2025.2
urllib3==2.4.0
websocket-client==1.6.1
websockets==15.0.1
wrapt==1.17.2
yarl==1.20.0
yfinance==0.2.61
flask==3.0.0
flask-cors==4.0.0
flask-socketio==5.3.6
