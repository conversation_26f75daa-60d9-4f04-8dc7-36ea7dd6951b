# 🚀 RapidTrader

**A comprehensive, production-ready algorithmic trading platform for Indian markets**

RapidTrader is a high-performance trading engine inspired by FreqTrade, specifically designed for Indian markets with native broker integrations, real-time data streaming, and professional-grade features.

## ✨ Key Features

### 🏦 **Broker Integrations**
- **Fyers API v3** - Complete integration with real-time WebSocket data
- **DhanHQ** - Full trading capabilities with enhanced features
- **Unified Symbol System** - Standardized symbol mapping across brokers
- **Rate Limiting** - Built-in protection against API limits

### 🐳 **Docker-First Architecture**
- **FreqTrade-like Experience** - Familiar Docker commands and workflows
- **Multi-Service Architecture** - API Gateway, Frontend, Trading Containers
- **Container Orchestration** - Dynamic container management
- **Production Ready** - Optimized Alpine-based images

### 🌐 **Modern Web Interface**
- **Real-time Dashboard** - Live trading monitoring and control
- **Portfolio Management** - Account balance, positions, P&L tracking
- **Market Data Visualization** - Live price feeds and charts
- **Trading Controls** - Start/stop trading, mode switching

### 📊 **Advanced Backtesting**
- **Vectorized Engine** - High-performance pandas-based backtesting
- **Enhanced Backtesting** - Automatic Docker deployment with data management
- **Multiple Strategies** - Run multiple strategies simultaneously
- **Comprehensive Metrics** - Detailed performance analysis

### 🔐 **Enterprise Security**
- **API Key Authentication** - OpenAlgo-inspired security model
- **Environment-based Credentials** - Secure credential management
- **Rate Limiting** - Protection against abuse
- **Audit Logging** - Comprehensive activity tracking

### 💰 **Money Management**
- **Risk Management** - Position sizing and risk controls
- **Portfolio Allocation** - Dynamic capital allocation
- **Rebalancing** - Automatic portfolio rebalancing
- **Performance Tracking** - Real-time P&L monitoring

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Trading account with supported broker (Fyers/DhanHQ)
- Basic understanding of algorithmic trading

### 1. Setup

```bash
# Clone the repository
git clone https://github.com/bvsaisujith/rapidtrader.git
cd rapidtrader

# Make scripts executable
chmod +x scripts/*.sh

# Setup Fyers credentials (interactive)
./scripts/rapidtrader-docker.sh fyers-setup
```

### 2. Configure Environment

Create and edit your `.env` file:
```bash
cp .env.example .env
# Edit .env with your broker credentials
```

### 3. Start Trading Platform

```bash
# Start complete platform (API + Frontend + Trading)
./scripts/rapidtrader-docker.sh fyers-web-trading

# Access web interface at: http://localhost:8080
# Access API docs at: http://localhost:8000/docs
```

## 📋 Usage Examples

### 🔄 **Trading Modes**

#### **Dry Run Trading**
```bash
# Start dry run with Fyers
./scripts/rapidtrader-docker.sh fyers-dryrun

# With custom configuration
./scripts/rapidtrader-docker.sh dryrun -c userdata/configs/my-config.json
```

#### **Live Trading**
```bash
# Start live trading (requires real credentials)
./scripts/rapidtrader-docker.sh fyers-live

# With specific strategy
./scripts/rapidtrader-docker.sh live -s MyStrategy
```

#### **Paper Trading (Independent)**
```bash
# Independent paper trading with yfinance data
./scripts/rapidtrader-docker.sh paper-trade --duration 3600 --capital 50000

# With custom configuration
./scripts/rapidtrader-docker.sh paper-trade -c userdata/config/paper-config.json
```

### 📊 **Backtesting**

#### **Standard Backtesting**
```bash
# Run backtest with specific strategy
./scripts/rapidtrader-docker.sh backtest -s MyStrategy -c backtest-config

# With timerange
./scripts/rapidtrader-docker.sh backtest -s MyStrategy --timerange 20240101-20240630
```

#### **Enhanced Backtesting**
```bash
# Enhanced backtest with automatic Docker deployment
python core/rapidtrader.py backtest enhanced -s MyStrategy -c test-config --symbols RELIANCE,TCS,INFY

# With custom timeframe
python core/rapidtrader.py backtest enhanced -s MyStrategy -c test-config -t 1h --timerange 20240101-20240630
```

### 🔧 **Management Commands**

#### **Container Management**
```bash
# List all containers
./scripts/rapidtrader-docker.sh status

# Stop all containers
./scripts/rapidtrader-docker.sh stop

# Clean up stopped containers
./scripts/rapidtrader-docker.sh clean

# Interactive shell
./scripts/rapidtrader-docker.sh shell
```

#### **API Gateway**
```bash
# Start API Gateway
./scripts/rapidtrader-api.sh start

# Stop API Gateway
./scripts/rapidtrader-api.sh stop

# View API documentation
# Open http://localhost:8000/docs
```

### 🖥️ **CLI Usage**

RapidTrader provides a comprehensive CLI similar to FreqTrade:

#### **Strategy Management**
```bash
# List available strategies
python core/rapidtrader.py strategy list

# Create new strategy
python core/rapidtrader.py strategy create MyStrategy --config

# Create configuration for strategy
python core/rapidtrader.py strategy config MyStrategy --params param1=20,param2=50
```

#### **Configuration Management**
```bash
# Create new configuration interactively
python core/rapidtrader.py create-config --interactive

# Create configuration for specific broker
python core/rapidtrader.py create-config --exchange fyers --dry-run
```

#### **Profile and Account Info**
```bash
# Show Fyers account profile
python core/rapidtrader.py profile show --broker fyers

# Test broker connection
python core/rapidtrader.py profile test --broker fyers
```

#### **Money Management**
```bash
# Check portfolio rebalancing
python core/rapidtrader.py money check-rebalancing

# Show money management status
python core/rapidtrader.py money status
```

## 🏗️ Architecture

### **System Components**

```
┌─────────────────────────────────────────────────────────────┐
│  🌐 Frontend (React/TypeScript)                            │
│  ├── 📊 Real-time Dashboard                                │
│  ├── 💼 Portfolio Management                               │
│  ├── 📈 Market Data Visualization                          │
│  └── ⚙️ Trading Controls                                   │
├─────────────────────────────────────────────────────────────┤
│  🚪 API Gateway (FastAPI)                                  │
│  ├── 🔐 API Key Authentication                             │
│  ├── 🏦 Unified Broker Interface                          │
│  ├── 📈 Symbol Management                                 │
│  └── 🐳 Container Orchestration                           │
├─────────────────────────────────────────────────────────────┤
│  🔌 Broker Adapters (Fyers, Dhan, etc.)                  │
└─────────────────────────────────────────────────────────────┘
```

### **Configuration Structure**

```
userdata/
├── configs/                    # Trading configurations
│   ├── backtest-config.json   # Backtesting settings
│   ├── dryrun-config.json     # Dry run settings
│   ├── live-config.json       # Live trading settings
│   └── fyers-config.json      # Broker-specific config
├── strategies/                 # Trading strategies
│   ├── base_strategy.py       # Base strategy class
│   ├── DefaultStrategy.py     # Default strategy
│   └── MyStrategy.py          # Custom strategies
├── historical_data/            # Price data cache
├── logs/                       # Application logs
└── results/                    # Backtest results
```

## 🛠️ Development

### **Project Structure**

```
rapidtrader/
├── api_gateway/                # Central API Gateway
│   ├── main.py                # FastAPI application
│   ├── auth.py                # Authentication system
│   ├── broker_interface.py    # Broker abstraction
│   └── unified_api.py         # Unified API endpoints
├── broker/                     # Broker integrations
│   ├── fyers_wrapper.py       # Fyers API integration
│   ├── dhan_wrapper.py        # DhanHQ integration
│   ├── symbol_mapper.py       # Symbol mapping system
│   └── fyers_websocket.py     # Real-time data streaming
├── core/                       # Core trading engine
│   ├── rapidtrader.py         # Main CLI application
│   ├── backtest_engine.py     # Backtesting engine
│   ├── dryrun.py              # Dry run engine
│   ├── data_manager.py        # Data management
│   └── money_manager.py       # Money management
├── frontend_v2/                # Modern React frontend
│   ├── src/components/        # React components
│   ├── src/pages/             # Application pages
│   └── src/services/          # API services
├── userdata/                   # User data directory
│   ├── configs/               # Trading configurations
│   ├── strategies/            # Trading strategies
│   ├── historical_data/       # Price data
│   └── results/               # Backtest results
└── scripts/                    # Management scripts
    ├── rapidtrader-docker.sh  # Docker management
    └── rapidtrader-api.sh     # API management
```

### **Adding New Brokers**

1. Create broker wrapper in `broker/` directory
2. Implement unified interface methods
3. Add symbol mapping support
4. Update broker registry
5. Add configuration templates

### **Creating Custom Strategies**

```python
# userdata/strategies/MyStrategy.py
from userdata.strategies.base_strategy import BaseStrategy
import pandas as pd

class MyStrategy(BaseStrategy):
    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        # Add your indicators
        dataframe['sma_20'] = dataframe['close'].rolling(20).mean()
        return dataframe

    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        # Define buy conditions
        dataframe['buy'] = (dataframe['close'] > dataframe['sma_20'])
        return dataframe

    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        # Define sell conditions
        dataframe['sell'] = (dataframe['close'] < dataframe['sma_20'])
        return dataframe
```

## 🆚 RapidTrader vs FreqTrade

### **Similarities**
- Docker-first architecture
- Web-based monitoring interface
- Configuration-driven setup
- Strategy framework
- Backtesting capabilities
- Dry-run and live modes

### **RapidTrader Advantages**
- **🇮🇳 Indian Market Focus** - Native support for Indian brokers
- **📡 Real-time Data** - WebSocket streaming for live market data
- **🏦 Direct Broker Integration** - No external dependencies
- **🔐 Enterprise Security** - API key authentication system
- **💰 Advanced Money Management** - Built-in risk management
- **🚀 Modern Tech Stack** - FastAPI, React, TypeScript
- **📊 Enhanced Backtesting** - Automatic Docker deployment
- **🔄 Container Orchestration** - Dynamic container management

### **Migration from FreqTrade**
1. Export your FreqTrade strategies
2. Adapt to RapidTrader strategy format
3. Configure Indian broker credentials
4. Test in dry-run mode
5. Deploy with Docker

## 🧪 Testing

### **Run Test Suite**
```bash
# Run all tests
python test/run_tests.py

# Run specific test modules
python -m pytest test/test_fyers_integration.py -v
python -m pytest test/test_api_gateway.py -v
python -m pytest test/test_backtesting.py -v
```

### **Test Coverage**
- ✅ Broker integrations (Fyers, DhanHQ)
- ✅ API Gateway functionality
- ✅ WebSocket data streaming
- ✅ Backtesting engine
- ✅ Money management
- ✅ Frontend integration
- ✅ Docker deployment

## 📚 Documentation

- [📖 Complete Docker Guide](docs/RAPIDTRADER_DOCKER_GUIDE.md)
- [🏗️ API Architecture](docs/API_ARCHITECTURE.md)
- [🌐 Frontend Integration](docs/FRONTEND_INTEGRATION.md)
- [📊 Backtesting Guide](docs/BACKTESTING.md)
- [💰 Money Management](docs/MONEY_MANAGEMENT.md)
- [🔌 Fyers WebSocket Guide](docs/FYERS_WEBSOCKET_GUIDE.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

[MIT License](LICENSE)

## 🙏 Acknowledgments

- Inspired by [FreqTrade](https://github.com/freqtrade/freqtrade)
- API design inspired by [OpenAlgo](https://github.com/marketcalls/openalgo)
- Built for the Indian trading community

---

**🚀 Ready to start algorithmic trading? Get started with RapidTrader today!**

```bash
# One command to rule them all
./scripts/rapidtrader-docker.sh fyers-web-trading
```