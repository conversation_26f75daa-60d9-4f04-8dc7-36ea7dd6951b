"""
Fyers Symbol Mapper for RapidTrader

This module provides symbol mapping functionality for Fyers API v3.
Fyers uses symbols in the format "EXCHANGE:SYMBOL-SERIES" (e.g., "NSE:SBIN-EQ")
while RapidTrader uses simplified symbols like "SBIN".

Features:
- Convert between RapidTrader symbols and Fyers symbols
- Support for NSE, BSE, MCX exchanges
- Reverse mapping from Fyers symbols to RapidTrader symbols
- Symbol validation and format checking
"""

import json
import logging
from pathlib import Path
from typing import Dict, Optional, List, Tuple
from enum import Enum

# Configure logging
logger = logging.getLogger("FyersSymbolMapper")


class FyersExchangeType(Enum):
    """Fyers exchange types"""
    NSE_EQ = "NSE_EQ"
    BSE_EQ = "BSE_EQ"
    NSE_FO = "NSE_FO"
    MCX = "MCX"


class FyersSymbolMapper:
    """
    Symbol mapper for Fyers API v3
    
    Handles conversion between RapidTrader symbols and Fyers API symbols
    """
    
    def __init__(self):
        """Initialize the symbol mapper with mapping data."""
        self.mapping_file = Path(__file__).parent / "fyers_symbol_mapping.json"
        self.symbol_mapping = self._load_symbol_mapping()
        
    def _load_symbol_mapping(self) -> Dict:
        """Load symbol mapping from JSON file."""
        try:
            with open(self.mapping_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"Symbol mapping file not found: {self.mapping_file}")
            return {}
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing symbol mapping file: {e}")
            return {}
    
    def to_fyers_symbol(self, symbol: str, exchange: str = "NSE_EQ") -> Optional[str]:
        """
        Convert RapidTrader symbol to Fyers symbol format.
        
        Args:
            symbol: RapidTrader symbol (e.g., "SBIN", "RELIANCE")
            exchange: Exchange type (NSE_EQ, BSE_EQ, NSE_FO, MCX)
            
        Returns:
            Fyers symbol (e.g., "NSE:SBIN-EQ") or None if not found
        """
        if not self.symbol_mapping:
            return None
            
        exchange_mapping = self.symbol_mapping.get(exchange, {})
        return exchange_mapping.get(symbol.upper())
    
    def from_fyers_symbol(self, fyers_symbol: str) -> Optional[str]:
        """
        Convert Fyers symbol to RapidTrader symbol format.
        
        Args:
            fyers_symbol: Fyers symbol (e.g., "NSE:SBIN-EQ")
            
        Returns:
            RapidTrader symbol (e.g., "SBIN") or None if not found
        """
        if not self.symbol_mapping:
            return None
            
        reverse_mapping = self.symbol_mapping.get("reverse_mapping", {})
        return reverse_mapping.get(fyers_symbol)
    
    def validate_fyers_symbol(self, symbol: str) -> bool:
        """
        Validate if a symbol is in correct Fyers format.
        
        Args:
            symbol: Symbol to validate
            
        Returns:
            True if valid Fyers symbol format, False otherwise
        """
        if not symbol or ":" not in symbol:
            return False
            
        try:
            exchange, symbol_part = symbol.split(":", 1)
            
            # Check if exchange is valid
            valid_exchanges = ["NSE", "BSE", "MCX", "NCDEX"]
            if exchange not in valid_exchanges:
                return False
                
            # Check if symbol part has series (for equity)
            if exchange in ["NSE", "BSE"] and "-" not in symbol_part:
                return False
                
            return True
            
        except ValueError:
            return False
    
    def get_exchange_from_fyers_symbol(self, fyers_symbol: str) -> Optional[str]:
        """
        Extract exchange from Fyers symbol.
        
        Args:
            fyers_symbol: Fyers symbol (e.g., "NSE:SBIN-EQ")
            
        Returns:
            Exchange name (e.g., "NSE") or None if invalid
        """
        if not self.validate_fyers_symbol(fyers_symbol):
            return None
            
        return fyers_symbol.split(":", 1)[0]
    
    def get_symbol_from_fyers_symbol(self, fyers_symbol: str) -> Optional[str]:
        """
        Extract symbol name from Fyers symbol.
        
        Args:
            fyers_symbol: Fyers symbol (e.g., "NSE:SBIN-EQ")
            
        Returns:
            Symbol name (e.g., "SBIN") or None if invalid
        """
        if not self.validate_fyers_symbol(fyers_symbol):
            return None
            
        try:
            _, symbol_part = fyers_symbol.split(":", 1)
            
            # For equity symbols, remove series suffix
            if "-" in symbol_part:
                symbol_name, _ = symbol_part.split("-", 1)
                return symbol_name
            else:
                return symbol_part
                
        except ValueError:
            return None
    
    def get_series_from_fyers_symbol(self, fyers_symbol: str) -> Optional[str]:
        """
        Extract series from Fyers symbol.
        
        Args:
            fyers_symbol: Fyers symbol (e.g., "NSE:SBIN-EQ")
            
        Returns:
            Series (e.g., "EQ") or None if invalid or no series
        """
        if not self.validate_fyers_symbol(fyers_symbol):
            return None
            
        try:
            _, symbol_part = fyers_symbol.split(":", 1)
            
            if "-" in symbol_part:
                _, series = symbol_part.split("-", 1)
                return series
            else:
                return None
                
        except ValueError:
            return None
    
    def search_symbols(self, query: str, exchange: Optional[str] = None) -> List[str]:
        """
        Search for symbols matching a query.
        
        Args:
            query: Search query (partial symbol name)
            exchange: Optional exchange filter (NSE_EQ, BSE_EQ, etc.)
            
        Returns:
            List of matching Fyers symbols
        """
        if not self.symbol_mapping:
            return []
            
        matches = []
        query_upper = query.upper()
        
        # Search in specific exchange or all exchanges
        exchanges_to_search = [exchange] if exchange else ["NSE_EQ", "BSE_EQ", "NSE_FO", "MCX"]
        
        for exch in exchanges_to_search:
            if exch not in self.symbol_mapping:
                continue
                
            exchange_mapping = self.symbol_mapping[exch]
            for symbol, fyers_symbol in exchange_mapping.items():
                if query_upper in symbol:
                    matches.append(fyers_symbol)
        
        return sorted(list(set(matches)))
    
    def get_all_symbols(self, exchange: Optional[str] = None) -> List[str]:
        """
        Get all available symbols for an exchange.
        
        Args:
            exchange: Exchange type (NSE_EQ, BSE_EQ, etc.) or None for all
            
        Returns:
            List of all Fyers symbols
        """
        if not self.symbol_mapping:
            return []
            
        all_symbols = []
        
        # Get symbols from specific exchange or all exchanges
        exchanges_to_get = [exchange] if exchange else ["NSE_EQ", "BSE_EQ", "NSE_FO", "MCX"]
        
        for exch in exchanges_to_get:
            if exch not in self.symbol_mapping:
                continue
                
            exchange_mapping = self.symbol_mapping[exch]
            all_symbols.extend(exchange_mapping.values())
        
        return sorted(list(set(all_symbols)))
    
    def add_symbol_mapping(self, symbol: str, fyers_symbol: str, exchange: str = "NSE_EQ") -> bool:
        """
        Add a new symbol mapping.
        
        Args:
            symbol: RapidTrader symbol
            fyers_symbol: Fyers symbol
            exchange: Exchange type
            
        Returns:
            True if added successfully, False otherwise
        """
        if not self.validate_fyers_symbol(fyers_symbol):
            logger.error(f"Invalid Fyers symbol format: {fyers_symbol}")
            return False
            
        # Add to forward mapping
        if exchange not in self.symbol_mapping:
            self.symbol_mapping[exchange] = {}
        self.symbol_mapping[exchange][symbol.upper()] = fyers_symbol
        
        # Add to reverse mapping
        if "reverse_mapping" not in self.symbol_mapping:
            self.symbol_mapping["reverse_mapping"] = {}
        self.symbol_mapping["reverse_mapping"][fyers_symbol] = symbol.upper()
        
        # Save to file
        return self._save_symbol_mapping()
    
    def _save_symbol_mapping(self) -> bool:
        """Save symbol mapping to JSON file."""
        try:
            with open(self.mapping_file, 'w') as f:
                json.dump(self.symbol_mapping, f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving symbol mapping: {e}")
            return False
    
    def get_mapping_stats(self) -> Dict[str, int]:
        """Get statistics about the symbol mapping."""
        if not self.symbol_mapping:
            return {}
            
        stats = {}
        for exchange in ["NSE_EQ", "BSE_EQ", "NSE_FO", "MCX"]:
            if exchange in self.symbol_mapping:
                stats[exchange] = len(self.symbol_mapping[exchange])
            else:
                stats[exchange] = 0
                
        stats["total_reverse_mappings"] = len(self.symbol_mapping.get("reverse_mapping", {}))
        return stats


# Global symbol mapper instance
_fyers_symbol_mapper = None

def get_fyers_symbol_mapper() -> FyersSymbolMapper:
    """Get the global Fyers symbol mapper instance (singleton pattern)."""
    global _fyers_symbol_mapper
    
    if _fyers_symbol_mapper is None:
        _fyers_symbol_mapper = FyersSymbolMapper()
    
    return _fyers_symbol_mapper


# Convenience functions
def to_fyers_symbol(symbol: str, exchange: str = "NSE_EQ") -> Optional[str]:
    """Convert RapidTrader symbol to Fyers symbol."""
    return get_fyers_symbol_mapper().to_fyers_symbol(symbol, exchange)


def from_fyers_symbol(fyers_symbol: str) -> Optional[str]:
    """Convert Fyers symbol to RapidTrader symbol."""
    return get_fyers_symbol_mapper().from_fyers_symbol(fyers_symbol)


def validate_fyers_symbol(symbol: str) -> bool:
    """Validate Fyers symbol format."""
    return get_fyers_symbol_mapper().validate_fyers_symbol(symbol)
