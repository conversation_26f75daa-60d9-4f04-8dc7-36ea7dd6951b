# DhanHQ Broker Wrapper for RapidTrader

## Overview

This is a comprehensive DhanHQ API v2.0 wrapper built according to the official documentation at https://dhanhq.co/docs/v2/orders/

## Features

### Complete Order Management
- **Order Placement**: Place orders with all DhanHQ API parameters
- **Order Modification**: Modify pending orders
- **Order Cancellation**: Cancel pending orders  
- **Order Slicing**: Handle large orders over freeze limits
- **Order Book**: Retrieve all orders for the day
- **Trade Book**: Retrieve all trades for the day

### Supported Order Types
- **LIMIT**: Limit orders with specified price
- **MARKET**: Market orders for immediate execution
- **STOP_LOSS**: Stop loss limit orders
- **STOP_LOSS_MARKET**: Stop loss market orders

### Supported Product Types
- **CNC**: Cash & Carry for equity delivery
- **INTRADAY**: Intraday trading
- **MARGIN**: Margin trading for F&O
- **MTF**: Margin Trading Facility
- **CO**: Cover Orders with stop loss
- **BO**: Bracket Orders with target and stop loss

### Exchange Support
- **NSE_EQ**: NSE Equity
- **NSE_FNO**: NSE Futures & Options
- **NSE_CURRENCY**: NSE Currency
- **BSE_EQ**: BSE Equity
- **BSE_FNO**: BSE Futures & Options
- **BSE_CURRENCY**: BSE Currency
- **MCX_COMM**: MCX Commodity

### Advanced Features
- **After Market Orders (AMO)**: Place orders after market hours
- **Disclosed Quantity**: Control visible quantity in market depth
- **Correlation ID**: Track orders with custom IDs
- **Bracket/Cover Orders**: Advanced order types with profit/loss targets
- **F&O Support**: Full futures and options support

### Dry Run Mode
- **Paper Trading**: Test strategies without real money
- **Order Simulation**: Simulate all order operations
- **Order Tracking**: Track simulated orders and trades
- **Summary Reports**: Get dry run statistics

## Usage Examples

### Basic Order Placement

```python
from broker.dhan_wrapper import DhanBroker, ExchangeSegment, TransactionType, ProductType, OrderType

# Initialize broker
broker = DhanBroker(dry_run=True)

# Place a simple equity order
response = broker.place_order(
    security_id="11536",  # TCS security ID
    exchange_segment=ExchangeSegment.NSE_EQ,
    transaction_type=TransactionType.BUY,
    quantity=10,
    product_type=ProductType.CNC,
    order_type=OrderType.LIMIT,
    price=3500.0
)

print(f"Order ID: {response['orderId']}")
```

### Using Helper Functions

```python
from broker.dhan_wrapper import DhanBroker, create_equity_order, create_bracket_order

broker = DhanBroker(dry_run=True)

# Simple equity order
equity_params = create_equity_order("11536", 10, "BUY", 3500.0)
response = broker.place_order(**equity_params)

# Bracket order with profit and stop loss
bracket_params = create_bracket_order("11536", 10, "BUY", 3500.0, 3600.0, 3450.0)
response = broker.place_order(**bracket_params)
```

### Order Management

```python
# Get all orders
orders = broker.get_orders()

# Get specific order
order = broker.get_order_by_id("order_id")

# Modify order
broker.modify_order(
    order_id="order_id",
    order_type="LIMIT",
    validity="DAY",
    price=3450.0,
    quantity=15
)

# Cancel order
broker.cancel_order("order_id")

# Get trades
trades = broker.get_trades()
```

### Advanced Features

```python
# After Market Order
response = broker.place_order(
    security_id="11536",
    exchange_segment="NSE_EQ",
    transaction_type="BUY",
    quantity=10,
    product_type="CNC",
    order_type="LIMIT",
    price=3500.0,
    after_market_order=True,
    amo_time="OPEN"
)

# Order with disclosed quantity
response = broker.place_order(
    security_id="11536",
    exchange_segment="NSE_EQ",
    transaction_type="BUY",
    quantity=100,
    product_type="CNC",
    order_type="LIMIT",
    price=3500.0,
    disclosed_quantity=30  # Only show 30 shares in market depth
)

# Bracket Order
response = broker.place_order(
    security_id="11536",
    exchange_segment="NSE_EQ",
    transaction_type="BUY",
    quantity=10,
    product_type="BO",
    order_type="LIMIT",
    price=3500.0,
    bo_profit_value=3600.0,  # Target price
    bo_stop_loss_value=3450.0  # Stop loss price
)
```

## Configuration

### Environment Variables (.env file)

```bash
# DhanHQ API Credentials
DHAN_CLIENT_ID=your_client_id
DHAN_ACCESS_TOKEN=your_access_token

# API Configuration
DHAN_API_URL=https://api.dhan.co/v2
API_REQUEST_TIMEOUT=30
API_MAX_RETRIES=3
API_RETRY_DELAY=1

# Trading Mode
DRY_RUN_ENABLED=true
```

### Initialization Options

```python
# Use environment variables
broker = DhanBroker()

# Override specific settings
broker = DhanBroker(
    client_id="your_client_id",
    access_token="your_token",
    dry_run=True,
    base_url="https://api.dhan.co/v2"
)
```

## Error Handling

The wrapper includes comprehensive error handling:

```python
response = broker.place_order(...)

if "error" in response:
    print(f"Order failed: {response['error']}")
else:
    print(f"Order placed: {response['orderId']}")
```

## Dry Run Features

```python
broker = DhanBroker(dry_run=True)

# Check if in dry run mode
print(f"Dry run: {broker.is_dry_run()}")

# Get dry run summary
summary = broker.get_dry_run_summary()
print(f"Total orders: {summary['total_orders']}")

# Clear dry run data
broker.clear_dry_run_data()
```

## Integration with RapidTrader

The wrapper is designed to integrate seamlessly with RapidTrader's trading engine:

1. **Dry Run Mode**: Use for paper trading and strategy testing
2. **Live Mode**: Use for actual trading with real money
3. **Configuration**: Controlled via RapidTrader config files
4. **Logging**: Integrated with RapidTrader's logging system

## API Compliance

This wrapper is fully compliant with DhanHQ API v2.0 specifications:

- All order parameters supported
- Proper error handling
- Rate limiting ready (can be added)
- Response format matching
- Enum validation for all parameters

## Files

- `dhan_wrapper.py`: Main wrapper implementation
- `README.md`: This documentation
- `test_dhan_wrapper.py`: Test script for validation

## Testing

Run the test script to verify functionality:

```bash
python test_dhan_wrapper.py
```

This will test all major features in dry run mode to ensure everything works correctly.
