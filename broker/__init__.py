"""
Broker module for RapidTrader.

This module provides broker implementations for various trading platforms.
"""

from broker.dhan_wrapper import (
    DhanBroker,
    ExchangeSegment,
    TransactionType,
    ProductType,
    OrderType,
    Validity,
    OrderStatus,
    AMOTime,
    OptionType,
    LegName,
    # Symbol-based helper functions
    create_equity_order_by_symbol,
    create_market_order_by_symbol,
    create_stop_loss_order_by_symbol,
    create_bracket_order_by_symbol
)

# Import symbol mapper
try:
    from broker.symbol_mapper import SymbolMapper, get_symbol_mapper, symbol_to_security_id, security_id_to_symbol
    _has_symbol_mapper = True
except ImportError:
    _has_symbol_mapper = False

# Import Fyers broker
try:
    from broker.fyers_wrapper import (
        FyersBroker,
        FyersTransactionType,
        FyersExchange,
        FyersProductType,
        FyersOrderType,
        FyersValidity,
        FyersOrderStatus
    )
    from broker.fyers_symbol_mapper import (
        FyersSymbolMapper,
        get_fyers_symbol_mapper,
        to_fyers_symbol,
        from_fyers_symbol,
        validate_fyers_symbol
    )
    from broker.fyers_websocket import (
        FyersWebSocketClient,
        FyersDataType
    )
    from broker.fyers_websocket_manager import (
        FyersWebSocketManager
    )
    _has_fyers = True
except ImportError:
    _has_fyers = False

__all__ = [
    'DhanBroker',
    'ExchangeSegment',
    'TransactionType',
    'ProductType',
    'OrderType',
    'Validity',
    'OrderStatus',
    'AMOTime',
    'OptionType',
    'LegName',
    # Symbol-based functions
    'create_equity_order_by_symbol',
    'create_market_order_by_symbol',
    'create_stop_loss_order_by_symbol',
    'create_bracket_order_by_symbol'
]

# Add symbol mapper to exports if available
if _has_symbol_mapper:
    __all__.extend([
        'SymbolMapper',
        'get_symbol_mapper',
        'symbol_to_security_id',
        'security_id_to_symbol'
    ])

# Add Fyers broker to exports if available
if _has_fyers:
    __all__.extend([
        'FyersBroker',
        'FyersTransactionType',
        'FyersExchange',
        'FyersProductType',
        'FyersOrderType',
        'FyersValidity',
        'FyersOrderStatus',
        'FyersSymbolMapper',
        'get_fyers_symbol_mapper',
        'to_fyers_symbol',
        'from_fyers_symbol',
        'validate_fyers_symbol',
        'FyersWebSocketClient',
        'FyersDataType',
        'FyersWebSocketManager'
    ])
