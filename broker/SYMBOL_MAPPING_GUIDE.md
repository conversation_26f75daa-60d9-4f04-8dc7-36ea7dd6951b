# Symbol Mapping Guide for DhanHQ Integration

## Problem Statement

DhanHQ API requires numeric `security_id` values (like "2885" for RELIANCE) while RapidTrader strategies use human-readable symbols (like "RELIANCE"). This creates a mismatch that needs to be resolved for seamless integration.

## Solution Overview

We've implemented a comprehensive **Symbol Mapping System** that:

1. **Automatically converts** RapidTrader symbols to DhanHQ security IDs
2. **Provides dual interfaces** - symbol-based and security-ID-based
3. **Maintains backward compatibility** with existing code
4. **Allows easy extension** for new symbols

## Architecture

```
RapidTrader Strategy
        ↓
   Symbol ("RELIANCE")
        ↓
   Symbol Mapper
        ↓
   Security ID ("2885")
        ↓
   DhanHQ API
```

## Components

### 1. SymbolMapper Class (`broker/symbol_mapper.py`)

**Core functionality:**
- Bidirectional mapping between symbols and security IDs
- JSON-based storage for persistence
- Support for multiple exchanges
- Default mappings for popular stocks

**Key methods:**
```python
mapper = SymbolMapper()
security_id = mapper.get_security_id("REL<PERSON>NC<PERSON>", "NSE_EQ")  # Returns "2885"
symbol = mapper.get_symbol("2885", "NSE_EQ")  # Returns "RELIANCE"
mapper.add_mapping("NEWSTOCK", "12345", "NSE_EQ")
```

### 2. Enhanced DhanBroker (`broker/dhan_wrapper.py`)

**New symbol-based methods:**
- `place_order_by_symbol()` - Place orders using symbols
- `get_security_id()` - Get security ID for a symbol
- `add_symbol_mapping()` - Add new mappings
- `get_available_symbols()` - List available symbols

**Dual interface support:**
```python
# Traditional approach (still works)
broker.place_order(security_id="2885", ...)

# New symbol-based approach
broker.place_order_by_symbol(symbol="RELIANCE", ...)
```

### 3. Symbol-based Helper Functions

**Easy order creation:**
```python
from broker.dhan_wrapper import create_equity_order_by_symbol

params = create_equity_order_by_symbol("RELIANCE", 10, "BUY", 2500.0)
response = broker.place_order_by_symbol(**params)
```

## Default Symbol Mappings

The system comes with pre-configured mappings for **50+ popular NSE stocks**:

| Symbol | Security ID | Company |
|--------|-------------|---------|
| RELIANCE | 2885 | Reliance Industries |
| TCS | 11536 | Tata Consultancy Services |
| HDFCBANK | 1333 | HDFC Bank |
| ICICIBANK | 4963 | ICICI Bank |
| INFY | 408 | Infosys |
| ... | ... | ... |

## Usage Examples

### 1. Basic Order Placement

```python
from broker.dhan_wrapper import DhanBroker

broker = DhanBroker(dry_run=True)

# Place order using symbol (recommended)
response = broker.place_order_by_symbol(
    symbol="RELIANCE",
    quantity=10,
    transaction_type="BUY",
    price=2500.0,
    product_type="CNC"
)
```

### 2. Strategy Integration

```python
class MyStrategy:
    def __init__(self):
        self.broker = DhanBroker()
    
    def buy_signal(self, symbol, price):
        # Strategy doesn't need to know security IDs
        return self.broker.place_order_by_symbol(
            symbol=symbol,
            quantity=10,
            transaction_type="BUY",
            price=price
        )
    
    def sell_signal(self, symbol, price):
        return self.broker.place_order_by_symbol(
            symbol=symbol,
            quantity=10,
            transaction_type="SELL",
            price=price
        )
```

### 3. Adding New Symbols

```python
# Add mapping for a new stock
broker.add_symbol_mapping("NEWSTOCK", "12345", "NSE_EQ")

# Now you can trade it using the symbol
broker.place_order_by_symbol("NEWSTOCK", 5, "BUY", 100.0)
```

### 4. Error Handling

```python
response = broker.place_order_by_symbol("UNKNOWNSTOCK", 1, "BUY", 100.0)

if "error" in response:
    print(f"Error: {response['error']}")
    print(f"Suggestion: {response['suggestion']}")
    
    # Add the mapping if you know the security ID
    broker.add_symbol_mapping("UNKNOWNSTOCK", "54321", "NSE_EQ")
```

## Configuration

### Symbol Mapping File

Mappings are stored in `broker/dhan_symbol_mapping.json`:

```json
{
  "symbol_to_id": {
    "RELIANCE": {
      "NSE_EQ": "2885"
    },
    "TCS": {
      "NSE_EQ": "11536"
    }
  },
  "id_to_symbol": {
    "2885": {
      "NSE_EQ": "RELIANCE"
    },
    "11536": {
      "NSE_EQ": "TCS"
    }
  }
}
```

### Environment Variables

No additional environment variables needed. The system uses existing DhanHQ credentials from `.env`.

## Benefits for RapidTrader

### 1. **Developer Experience**
- ✅ Use familiar symbols like "RELIANCE" instead of "2885"
- ✅ No need to memorize security IDs
- ✅ Cleaner, more readable strategy code

### 2. **Maintainability**
- ✅ Easy to add new symbols
- ✅ Centralized mapping management
- ✅ JSON-based configuration

### 3. **Flexibility**
- ✅ Dual interface support (symbols + security IDs)
- ✅ Backward compatibility
- ✅ Multi-exchange support

### 4. **Error Prevention**
- ✅ Validation before API calls
- ✅ Clear error messages
- ✅ Helpful suggestions

## Integration with RapidTrader Modules

### Backtesting
```python
# Backtest strategies can use symbols directly
class BacktestStrategy(BaseStrategy):
    def populate_buy_trend(self, dataframe, metadata):
        # metadata['pair'] contains symbol like "RELIANCE"
        symbol = metadata['pair']
        # Use symbol directly in orders
        return self.broker.place_order_by_symbol(symbol, ...)
```

### Live Trading
```python
# Live trading seamlessly converts symbols to security IDs
broker.place_order_by_symbol("RELIANCE", 10, "BUY", 2500.0)
# Internally converts to security_id="2885" for DhanHQ API
```

### Dry Run
```python
# Dry run mode works with both approaches
broker = DhanBroker(dry_run=True)
broker.place_order_by_symbol("TCS", 5, "BUY", 3500.0)  # Simulated
```

## Testing

Run the test suite to verify functionality:

```bash
python test_symbol_mapping.py
```

This tests:
- Symbol to security ID conversion
- Security ID to symbol conversion
- Order placement with symbols
- Error handling for unknown symbols
- Strategy integration scenarios

## Extending the System

### Adding Bulk Mappings

```python
from broker.symbol_mapper import get_symbol_mapper

mapper = get_symbol_mapper()

# Add multiple mappings
new_mappings = {
    "STOCK1": "11111",
    "STOCK2": "22222",
    "STOCK3": "33333"
}

for symbol, security_id in new_mappings.items():
    mapper.add_mapping(symbol, security_id, "NSE_EQ")

mapper.save_mappings()
```

### Custom Exchange Support

```python
# Add BSE mappings
mapper.add_mapping("RELIANCE", "500325", "BSE_EQ")

# Use with BSE
broker.place_order_by_symbol("RELIANCE", 10, "BUY", 2500.0, exchange_segment="BSE_EQ")
```

## Migration Guide

### For Existing Code

**Before (using security IDs):**
```python
broker.place_order(security_id="2885", exchange_segment="NSE_EQ", ...)
```

**After (using symbols):**
```python
broker.place_order_by_symbol(symbol="RELIANCE", exchange_segment="NSE_EQ", ...)
```

**Note:** Old code continues to work unchanged.

### For New Strategies

Always use symbol-based methods for new development:

```python
# Recommended approach
broker.place_order_by_symbol(symbol="RELIANCE", ...)

# Helper functions
from broker.dhan_wrapper import create_equity_order_by_symbol
params = create_equity_order_by_symbol("RELIANCE", 10, "BUY", 2500.0)
response = broker.place_order_by_symbol(**params)
```

## Troubleshooting

### Symbol Not Found Error

```
Error: Security ID not found for symbol: NEWSTOCK on NSE_EQ
Suggestion: Add the symbol mapping using add_symbol_mapping()
```

**Solution:**
1. Find the DhanHQ security ID for the symbol
2. Add the mapping: `broker.add_symbol_mapping("NEWSTOCK", "12345", "NSE_EQ")`

### Performance Considerations

- Mappings are loaded once at startup
- In-memory lookups are very fast
- JSON file is only read/written when needed
- No impact on order placement speed

## Conclusion

The Symbol Mapping System provides a seamless bridge between RapidTrader's symbol-based approach and DhanHQ's security-ID-based API. It maintains backward compatibility while offering a much more developer-friendly interface for new development.

**Key takeaway:** Strategies can now focus on trading logic using familiar symbols, while the system handles the technical details of API integration automatically.
