"""
Fyers WebSocket Manager for RapidTrader

This module provides a high-level WebSocket manager that integrates with the Fyers broker
for real-time market data streaming. It handles data caching, symbol management, and
provides a unified interface for live data access.

Features:
- Real-time quote streaming
- Market depth data
- Order update notifications
- Data caching and buffering
- Symbol subscription management
- Event-driven architecture
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from collections import defaultdict, deque
import json

from broker.fyers_websocket import FyersWebSocketClient, FyersDataType

logger = logging.getLogger(__name__)


class FyersWebSocketManager:
    """
    High-level WebSocket manager for Fyers real-time data.
    
    This manager provides a simplified interface for accessing real-time market data
    from Fyers WebSocket streams. It handles connection management, data caching,
    and provides callbacks for different types of market events.
    """
    
    def __init__(self, 
                 access_token: str,
                 client_id: str,
                 cache_size: int = 1000,
                 cache_duration: int = 300):  # 5 minutes
        """
        Initialize WebSocket manager.
        
        Args:
            access_token: Fyers access token
            client_id: Fyers client ID
            cache_size: Maximum number of cached quotes per symbol
            cache_duration: Cache duration in seconds
        """
        self.access_token = access_token
        self.client_id = client_id
        self.cache_size = cache_size
        self.cache_duration = cache_duration
        
        # WebSocket client
        self.ws_client = None
        self.is_connected = False
        
        # Data caches
        self.quote_cache = defaultdict(lambda: deque(maxlen=cache_size))
        self.depth_cache = defaultdict(dict)
        self.last_quotes = {}
        self.last_depths = {}
        
        # Cache timestamps
        self.quote_timestamps = defaultdict(lambda: deque(maxlen=cache_size))
        self.depth_timestamps = {}
        
        # Event callbacks
        self.quote_callbacks = []
        self.depth_callbacks = []
        self.order_callbacks = []
        self.connection_callbacks = []
        
        # Statistics
        self.stats = {
            "messages_received": 0,
            "quotes_processed": 0,
            "depths_processed": 0,
            "orders_processed": 0,
            "connection_time": None,
            "last_message_time": None
        }
        
        # Thread safety
        self.lock = threading.RLock()
        
        logger.info("Fyers WebSocket Manager initialized")
    
    def connect(self) -> bool:
        """
        Connect to Fyers WebSocket.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            if self.is_connected:
                logger.warning("Already connected to WebSocket")
                return True
            
            # Create WebSocket client
            self.ws_client = FyersWebSocketClient(
                access_token=self.access_token,
                client_id=self.client_id,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open
            )
            
            # Add data handlers
            self.ws_client.add_data_handler(FyersDataType.SYMBOL_DATA, self._handle_quote_data)
            self.ws_client.add_data_handler(FyersDataType.DEPTH_DATA, self._handle_depth_data)
            self.ws_client.add_data_handler(FyersDataType.ORDER_UPDATE, self._handle_order_update)
            
            # Connect
            if self.ws_client.connect():
                self.is_connected = True
                self.stats["connection_time"] = datetime.now()
                
                # Notify connection callbacks
                for callback in self.connection_callbacks:
                    try:
                        callback(True, "Connected")
                    except Exception as e:
                        logger.error(f"Error in connection callback: {e}")
                
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error connecting WebSocket manager: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from WebSocket."""
        try:
            if self.ws_client:
                self.ws_client.disconnect()
            
            self.is_connected = False
            
            # Notify connection callbacks
            for callback in self.connection_callbacks:
                try:
                    callback(False, "Disconnected")
                except Exception as e:
                    logger.error(f"Error in connection callback: {e}")
            
            logger.info("WebSocket manager disconnected")
            
        except Exception as e:
            logger.error(f"Error disconnecting WebSocket manager: {e}")
    
    def subscribe_quotes(self, symbols: List[str]) -> bool:
        """
        Subscribe to live quotes for symbols.
        
        Args:
            symbols: List of Fyers symbols
            
        Returns:
            True if subscription successful, False otherwise
        """
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False
        
        return self.ws_client.subscribe_symbols(symbols, "symbolData")
    
    def subscribe_depth(self, symbols: List[str]) -> bool:
        """
        Subscribe to market depth for symbols.
        
        Args:
            symbols: List of Fyers symbols
            
        Returns:
            True if subscription successful, False otherwise
        """
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False
        
        return self.ws_client.subscribe_symbols(symbols, "depthData")
    
    def unsubscribe_quotes(self, symbols: List[str]) -> bool:
        """
        Unsubscribe from live quotes.
        
        Args:
            symbols: List of Fyers symbols
            
        Returns:
            True if unsubscription successful, False otherwise
        """
        if not self.is_connected:
            return False
        
        return self.ws_client.unsubscribe_symbols(symbols, "symbolData")
    
    def unsubscribe_depth(self, symbols: List[str]) -> bool:
        """
        Unsubscribe from market depth.
        
        Args:
            symbols: List of Fyers symbols
            
        Returns:
            True if unsubscription successful, False otherwise
        """
        if not self.is_connected:
            return False
        
        return self.ws_client.unsubscribe_symbols(symbols, "depthData")
    
    def get_latest_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get latest quote for a symbol.
        
        Args:
            symbol: Fyers symbol
            
        Returns:
            Latest quote data or None if not available
        """
        with self.lock:
            return self.last_quotes.get(symbol)
    
    def get_latest_depth(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get latest market depth for a symbol.
        
        Args:
            symbol: Fyers symbol
            
        Returns:
            Latest depth data or None if not available
        """
        with self.lock:
            return self.last_depths.get(symbol)
    
    def get_quote_history(self, symbol: str, count: int = 100) -> List[Dict[str, Any]]:
        """
        Get historical quotes for a symbol.
        
        Args:
            symbol: Fyers symbol
            count: Number of quotes to return
            
        Returns:
            List of historical quotes
        """
        with self.lock:
            quotes = list(self.quote_cache[symbol])
            return quotes[-count:] if count < len(quotes) else quotes
    
    def add_quote_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        Add callback for quote updates.
        
        Args:
            callback: Function to call on quote update (symbol, quote_data)
        """
        self.quote_callbacks.append(callback)
        logger.info("Added quote callback")
    
    def add_depth_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        Add callback for depth updates.
        
        Args:
            callback: Function to call on depth update (symbol, depth_data)
        """
        self.depth_callbacks.append(callback)
        logger.info("Added depth callback")
    
    def add_order_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Add callback for order updates.
        
        Args:
            callback: Function to call on order update (order_data)
        """
        self.order_callbacks.append(callback)
        logger.info("Added order callback")
    
    def add_connection_callback(self, callback: Callable[[bool, str], None]):
        """
        Add callback for connection status changes.
        
        Args:
            callback: Function to call on connection change (is_connected, message)
        """
        self.connection_callbacks.append(callback)
        logger.info("Added connection callback")
    
    def get_subscribed_symbols(self) -> Dict[str, List[str]]:
        """
        Get currently subscribed symbols.
        
        Returns:
            Dictionary with subscribed symbols
        """
        if self.ws_client:
            return self.ws_client.get_subscribed_symbols()
        return {"symbolData": [], "depthData": []}
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get WebSocket statistics.
        
        Returns:
            Dictionary with statistics
        """
        with self.lock:
            stats = self.stats.copy()
            stats["cached_symbols"] = len(self.last_quotes)
            stats["total_cached_quotes"] = sum(len(cache) for cache in self.quote_cache.values())
            stats["is_connected"] = self.is_connected
            return stats
    
    def clear_cache(self, symbol: Optional[str] = None):
        """
        Clear cached data.
        
        Args:
            symbol: Specific symbol to clear, or None to clear all
        """
        with self.lock:
            if symbol:
                if symbol in self.quote_cache:
                    self.quote_cache[symbol].clear()
                    self.quote_timestamps[symbol].clear()
                if symbol in self.last_quotes:
                    del self.last_quotes[symbol]
                if symbol in self.last_depths:
                    del self.last_depths[symbol]
                if symbol in self.depth_timestamps:
                    del self.depth_timestamps[symbol]
            else:
                self.quote_cache.clear()
                self.quote_timestamps.clear()
                self.last_quotes.clear()
                self.last_depths.clear()
                self.depth_timestamps.clear()
        
        logger.info(f"Cleared cache for {'all symbols' if not symbol else symbol}")
    
    def _handle_quote_data(self, data: Dict[str, Any]):
        """Handle incoming quote data."""
        try:
            symbol = data.get("symbol")
            if not symbol:
                return
            
            with self.lock:
                # Update caches
                current_time = datetime.now()
                self.quote_cache[symbol].append(data)
                self.quote_timestamps[symbol].append(current_time)
                self.last_quotes[symbol] = data
                
                # Update statistics
                self.stats["quotes_processed"] += 1
                self.stats["last_message_time"] = current_time
            
            # Call callbacks
            for callback in self.quote_callbacks:
                try:
                    callback(symbol, data)
                except Exception as e:
                    logger.error(f"Error in quote callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling quote data: {e}")
    
    def _handle_depth_data(self, data: Dict[str, Any]):
        """Handle incoming depth data."""
        try:
            symbol = data.get("symbol")
            if not symbol:
                return
            
            with self.lock:
                # Update caches
                current_time = datetime.now()
                self.last_depths[symbol] = data
                self.depth_timestamps[symbol] = current_time
                
                # Update statistics
                self.stats["depths_processed"] += 1
                self.stats["last_message_time"] = current_time
            
            # Call callbacks
            for callback in self.depth_callbacks:
                try:
                    callback(symbol, data)
                except Exception as e:
                    logger.error(f"Error in depth callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling depth data: {e}")
    
    def _handle_order_update(self, data: Dict[str, Any]):
        """Handle incoming order update."""
        try:
            with self.lock:
                # Update statistics
                self.stats["orders_processed"] += 1
                self.stats["last_message_time"] = datetime.now()
            
            # Call callbacks
            for callback in self.order_callbacks:
                try:
                    callback(data)
                except Exception as e:
                    logger.error(f"Error in order callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling order update: {e}")
    
    def _on_message(self, ws, message):
        """Handle raw WebSocket messages."""
        with self.lock:
            self.stats["messages_received"] += 1
    
    def _on_error(self, ws, error):
        """Handle WebSocket errors."""
        logger.error(f"WebSocket error: {error}")
    
    def _on_close(self, ws):
        """Handle WebSocket close."""
        self.is_connected = False
        logger.warning("WebSocket connection closed")
    
    def _on_open(self, ws):
        """Handle WebSocket open."""
        logger.info("WebSocket connection opened")
