"""
Fyers WebSocket Client for Real-time Market Data

This module provides WebSocket connectivity to Fyers API v3 for real-time market data streaming.
Supports live quotes, market depth, and order updates.

Features:
- Real-time market data streaming
- Order update notifications
- Automatic reconnection
- Data validation and error handling
- Callback-based event system
"""

import logging
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from enum import Enum

# Import Fyers WebSocket
try:
    from fyers_apiv3.FyersWebsocket import data_ws
    _has_fyers_ws = True
except ImportError:
    _has_fyers_ws = False
    data_ws = None

logger = logging.getLogger(__name__)


class FyersDataType(Enum):
    """Fyers WebSocket data types."""
    SYMBOL_DATA = "symbolData"
    DEPTH_DATA = "depthData"
    ORDER_UPDATE = "orderUpdate"


class FyersWebSocketClient:
    """
    Fyers WebSocket client for real-time market data.

    This client handles WebSocket connections to Fyers API v3 for streaming
    real-time market data including quotes, depth, and order updates.
    """

    def __init__(self,
                 access_token: str,
                 client_id: str,
                 on_message: Optional[Callable] = None,
                 on_error: Optional[Callable] = None,
                 on_close: Optional[Callable] = None,
                 on_open: Optional[Callable] = None):
        """
        Initialize Fyers WebSocket client.

        Args:
            access_token: Fyers access token
            client_id: Fyers client ID
            on_message: Callback for incoming messages
            on_error: Callback for errors
            on_close: Callback for connection close
            on_open: Callback for connection open
        """
        if not _has_fyers_ws:
            raise ImportError("fyers-apiv3 package with WebSocket support is required")

        self.access_token = access_token
        self.client_id = client_id

        # WebSocket instance
        self.fyers_ws = None
        self.is_connected = False
        self.is_running = False

        # Subscribed symbols
        self.subscribed_symbols = set()
        self.subscribed_depth_symbols = set()

        # Callbacks
        self.on_message_callback = on_message
        self.on_error_callback = on_error
        self.on_close_callback = on_close
        self.on_open_callback = on_open

        # Data handlers
        self.data_handlers = {
            FyersDataType.SYMBOL_DATA: [],
            FyersDataType.DEPTH_DATA: [],
            FyersDataType.ORDER_UPDATE: []
        }

        # Connection monitoring
        self.last_heartbeat = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds

        logger.info("Fyers WebSocket client initialized")

    def connect(self) -> bool:
        """
        Connect to Fyers WebSocket.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            if not self.access_token or not self.client_id:
                logger.error("Access token and client ID are required")
                return False

            # Initialize Fyers WebSocket
            self.fyers_ws = data_ws.FyersDataSocket(
                access_token=self.access_token,
                log_path="",  # Disable file logging
                litemode=False,  # Full mode for all data
                write_to_file=False,  # Don't write to file
                reconnect=True,  # Enable auto-reconnect
                on_connect=self._on_connect,
                on_close=self._on_close,  # Use correct callback name
                on_error=self._on_error,
                on_message=self._on_message
            )

            # Connect to WebSocket
            self.fyers_ws.connect()
            self.is_running = True

            # Wait for connection
            timeout = 10  # seconds
            start_time = time.time()
            while not self.is_connected and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if self.is_connected:
                logger.info("Successfully connected to Fyers WebSocket")
                return True
            else:
                logger.error("Failed to connect to Fyers WebSocket within timeout")
                return False

        except Exception as e:
            logger.error(f"Error connecting to Fyers WebSocket: {e}")
            return False

    def disconnect(self):
        """Disconnect from Fyers WebSocket."""
        try:
            self.is_running = False

            if self.fyers_ws:
                self.fyers_ws.close_connection()

            self.is_connected = False
            self.subscribed_symbols.clear()
            self.subscribed_depth_symbols.clear()

            logger.info("Disconnected from Fyers WebSocket")

        except Exception as e:
            logger.error(f"Error disconnecting from Fyers WebSocket: {e}")

    def subscribe_symbols(self, symbols: List[str], data_type: str = "symbolData") -> bool:
        """
        Subscribe to symbol data.

        Args:
            symbols: List of Fyers symbols (e.g., ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ"])
            data_type: Type of data ("symbolData" or "depthData")

        Returns:
            True if subscription successful, False otherwise
        """
        try:
            if not self.is_connected:
                logger.error("WebSocket not connected")
                return False

            # Validate symbols
            valid_symbols = []
            for symbol in symbols:
                if self._validate_symbol(symbol):
                    valid_symbols.append(symbol)
                else:
                    logger.warning(f"Invalid symbol format: {symbol}")

            if not valid_symbols:
                logger.error("No valid symbols to subscribe")
                return False

            # Subscribe to symbols
            if data_type == "symbolData":
                self.fyers_ws.subscribe(symbols=valid_symbols, data_type=data_type)
                self.subscribed_symbols.update(valid_symbols)
                logger.info(f"Subscribed to {len(valid_symbols)} symbols for live data")

            elif data_type == "depthData":
                self.fyers_ws.subscribe(symbols=valid_symbols, data_type=data_type)
                self.subscribed_depth_symbols.update(valid_symbols)
                logger.info(f"Subscribed to {len(valid_symbols)} symbols for depth data")

            return True

        except Exception as e:
            logger.error(f"Error subscribing to symbols: {e}")
            return False

    def unsubscribe_symbols(self, symbols: List[str], data_type: str = "symbolData") -> bool:
        """
        Unsubscribe from symbol data.

        Args:
            symbols: List of Fyers symbols to unsubscribe
            data_type: Type of data ("symbolData" or "depthData")

        Returns:
            True if unsubscription successful, False otherwise
        """
        try:
            if not self.is_connected:
                logger.error("WebSocket not connected")
                return False

            # Unsubscribe from symbols
            self.fyers_ws.unsubscribe(symbols=symbols, data_type=data_type)

            if data_type == "symbolData":
                self.subscribed_symbols.difference_update(symbols)
            elif data_type == "depthData":
                self.subscribed_depth_symbols.difference_update(symbols)

            logger.info(f"Unsubscribed from {len(symbols)} symbols")
            return True

        except Exception as e:
            logger.error(f"Error unsubscribing from symbols: {e}")
            return False

    def add_data_handler(self, data_type: FyersDataType, handler: Callable):
        """
        Add a data handler for specific data type.

        Args:
            data_type: Type of data to handle
            handler: Callback function to handle the data
        """
        if data_type in self.data_handlers:
            self.data_handlers[data_type].append(handler)
            logger.info(f"Added data handler for {data_type.value}")

    def remove_data_handler(self, data_type: FyersDataType, handler: Callable):
        """
        Remove a data handler.

        Args:
            data_type: Type of data
            handler: Handler to remove
        """
        if data_type in self.data_handlers and handler in self.data_handlers[data_type]:
            self.data_handlers[data_type].remove(handler)
            logger.info(f"Removed data handler for {data_type.value}")

    def get_subscribed_symbols(self) -> Dict[str, List[str]]:
        """
        Get currently subscribed symbols.

        Returns:
            Dictionary with symbol data and depth data subscriptions
        """
        return {
            "symbolData": list(self.subscribed_symbols),
            "depthData": list(self.subscribed_depth_symbols)
        }

    def is_symbol_subscribed(self, symbol: str, data_type: str = "symbolData") -> bool:
        """
        Check if a symbol is subscribed.

        Args:
            symbol: Fyers symbol
            data_type: Type of data

        Returns:
            True if subscribed, False otherwise
        """
        if data_type == "symbolData":
            return symbol in self.subscribed_symbols
        elif data_type == "depthData":
            return symbol in self.subscribed_depth_symbols
        return False

    def _validate_symbol(self, symbol: str) -> bool:
        """
        Validate Fyers symbol format.

        Args:
            symbol: Symbol to validate

        Returns:
            True if valid, False otherwise
        """
        if not symbol or ":" not in symbol:
            return False

        try:
            exchange, symbol_part = symbol.split(":", 1)
            valid_exchanges = ["NSE", "BSE", "MCX", "NCDEX"]
            return exchange in valid_exchanges
        except ValueError:
            return False

    def _on_connect(self):
        """Handle WebSocket connection."""
        self.is_connected = True
        self.reconnect_attempts = 0
        self.last_heartbeat = datetime.now()

        logger.info("Fyers WebSocket connected")

        if self.on_open_callback:
            try:
                self.on_open_callback(None)
            except Exception as e:
                logger.error(f"Error in on_open callback: {e}")

    def _on_disconnect(self):
        """Handle WebSocket disconnection."""
        self.is_connected = False

        logger.warning("Fyers WebSocket disconnected")

        if self.on_close_callback:
            try:
                self.on_close_callback(None)
            except Exception as e:
                logger.error(f"Error in on_close callback: {e}")

        # Attempt reconnection if still running
        if self.is_running and self.reconnect_attempts < self.max_reconnect_attempts:
            self._attempt_reconnect()

    def _on_close(self):
        """Handle WebSocket close (alias for _on_disconnect)."""
        self._on_disconnect()

    def _on_error(self, error):
        """Handle WebSocket errors."""
        logger.error(f"Fyers WebSocket error: {error}")

        if self.on_error_callback:
            try:
                self.on_error_callback(None, error)
            except Exception as e:
                logger.error(f"Error in on_error callback: {e}")

    def _on_message(self, message):
        """Handle incoming WebSocket messages."""
        try:
            self.last_heartbeat = datetime.now()

            # Parse message
            if isinstance(message, str):
                data = json.loads(message)
            else:
                data = message

            # Determine data type and call appropriate handlers
            self._process_message(data)

            # Call user callback
            if self.on_message_callback:
                self.on_message_callback(None, data)

        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")

    def _process_message(self, data: Dict[str, Any]):
        """
        Process incoming message and call appropriate handlers.

        Args:
            data: Parsed message data
        """
        try:
            # Determine message type based on data structure
            if "symbol" in data and "ltp" in data:
                # Symbol data (live quotes)
                for handler in self.data_handlers[FyersDataType.SYMBOL_DATA]:
                    handler(data)

            elif "symbol" in data and "bid" in data and "ask" in data:
                # Depth data (market depth)
                for handler in self.data_handlers[FyersDataType.DEPTH_DATA]:
                    handler(data)

            elif "orderNumber" in data or "id" in data:
                # Order update
                for handler in self.data_handlers[FyersDataType.ORDER_UPDATE]:
                    handler(data)

        except Exception as e:
            logger.error(f"Error in message processing: {e}")

    def _attempt_reconnect(self):
        """Attempt to reconnect to WebSocket."""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("Max reconnection attempts reached")
            return

        self.reconnect_attempts += 1
        logger.info(f"Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")

        time.sleep(self.reconnect_delay)

        if self.connect():
            # Re-subscribe to symbols
            if self.subscribed_symbols:
                self.subscribe_symbols(list(self.subscribed_symbols), "symbolData")
            if self.subscribed_depth_symbols:
                self.subscribe_symbols(list(self.subscribed_depth_symbols), "depthData")
