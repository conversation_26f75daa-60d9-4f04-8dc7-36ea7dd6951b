"""
DhanHQ Broker Wrapper for RapidTrader

This module provides a comprehensive wrapper for the DhanHQ API v2.0
Based on official documentation: https://dhanhq.co/docs/v2/orders/

Features:
- Complete order management (place, modify, cancel, slice)
- Order book and trade book retrieval
- Dry run mode for testing
- Rate limiting compliance
- Error handling and validation
"""

import os
import logging
import requests
import time
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dotenv import load_dotenv

# Import symbol mapper
try:
    from broker.symbol_mapper import get_symbol_mapper, symbol_to_security_id
except ImportError:
    # Fallback if symbol mapper is not available
    def get_symbol_mapper():
        return None
    def symbol_to_security_id(symbol, exchange="NSE_EQ"):
        return None

# Import rate limiter
try:
    from broker.rate_limiter import get_rate_limiter
except ImportError:
    # Fallback if rate limiter is not available
    def get_rate_limiter():
        return None

# Load environment variables
try:
    root_dir = Path(__file__).resolve().parent.parent
    dotenv_path = root_dir / '.env'
    load_dotenv(dotenv_path=dotenv_path)
except Exception:
    # If dotenv loading fails, continue without it
    pass

# Configure logging
logger = logging.getLogger("DhanBroker")


class TransactionType(Enum):
    """Transaction types for DhanHQ API"""
    BUY = "BUY"
    SELL = "SELL"


class ExchangeSegment(Enum):
    """Exchange segments for DhanHQ API"""
    NSE_EQ = "NSE_EQ"          # NSE Equity
    NSE_FNO = "NSE_FNO"        # NSE Futures & Options
    NSE_CURRENCY = "NSE_CURRENCY"  # NSE Currency
    BSE_EQ = "BSE_EQ"          # BSE Equity
    BSE_FNO = "BSE_FNO"        # BSE Futures & Options
    BSE_CURRENCY = "BSE_CURRENCY"  # BSE Currency
    MCX_COMM = "MCX_COMM"      # MCX Commodity


class ProductType(Enum):
    """Product types for DhanHQ API"""
    CNC = "CNC"                # Cash & Carry
    INTRADAY = "INTRADAY"      # Intraday
    MARGIN = "MARGIN"          # Margin
    MTF = "MTF"                # Margin Trading Facility
    CO = "CO"                  # Cover Order
    BO = "BO"                  # Bracket Order


class OrderType(Enum):
    """Order types for DhanHQ API"""
    LIMIT = "LIMIT"
    MARKET = "MARKET"
    STOP_LOSS = "STOP_LOSS"
    STOP_LOSS_MARKET = "STOP_LOSS_MARKET"


class Validity(Enum):
    """Order validity for DhanHQ API"""
    DAY = "DAY"
    IOC = "IOC"


class OrderStatus(Enum):
    """Order status for DhanHQ API"""
    TRANSIT = "TRANSIT"
    PENDING = "PENDING"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"
    PART_TRADED = "PART_TRADED"
    TRADED = "TRADED"
    EXPIRED = "EXPIRED"
    CONFIRM = "CONFIRM"


class AMOTime(Enum):
    """After Market Order timing"""
    PRE_OPEN = "PRE_OPEN"
    OPEN = "OPEN"
    OPEN_30 = "OPEN_30"
    OPEN_60 = "OPEN_60"


class OptionType(Enum):
    """Option types for F&O"""
    CALL = "CALL"
    PUT = "PUT"


class LegName(Enum):
    """Leg names for Bracket/Cover orders"""
    ENTRY_LEG = "ENTRY_LEG"
    TARGET_LEG = "TARGET_LEG"
    STOP_LOSS_LEG = "STOP_LOSS_LEG"


class DhanBroker:
    """
    DhanHQ Broker implementation for RapidTrader

    Provides complete order management functionality with dry run support
    """

    def __init__(self,
                 client_id: Optional[str] = None,
                 access_token: Optional[str] = None,
                 dry_run: Optional[bool] = None,
                 base_url: Optional[str] = None,
                 live_data: Optional[bool] = None):
        """
        Initialize DhanBroker

        Args:
            client_id: DhanHQ client ID (from .env if not provided)
            access_token: DhanHQ access token (from .env if not provided)
            dry_run: Enable dry run mode for trading operations (from .env if not provided)
            base_url: API base URL (from .env if not provided)
            live_data: Enable live data fetching even in dry-run mode (default: True)
        """
        # Load configuration from environment
        self.client_id = client_id or os.getenv("DHAN_CLIENT_ID")
        self.access_token = access_token or os.getenv("DHAN_ACCESS_TOKEN")
        self.base_url = base_url or os.getenv("DHAN_API_URL", "https://api.dhan.co/v2")

        # Dry run mode for trading operations
        if dry_run is None:
            dry_run = os.getenv("DRY_RUN_ENABLED", "true").lower() == "true"
        self.dry_run = dry_run

        # Live data mode - allows fetching real balance/position data even in dry-run
        if live_data is None:
            live_data = os.getenv("LIVE_DATA_ENABLED", "true").lower() == "true"
        self.live_data = live_data

        # API configuration
        self.timeout = int(os.getenv("API_REQUEST_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("API_RETRY_DELAY", "1"))

        # Validate credentials
        if not self.client_id or not self.access_token:
            logger.warning("DhanHQ credentials not found. Set DHAN_CLIENT_ID and DHAN_ACCESS_TOKEN in .env")

        # Setup HTTP session
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "access-token": self.access_token
        })

        # Dry run order tracking
        self._dry_run_orders = {}
        self._dry_run_trades = {}
        self._next_order_id = 1

        # Initialize rate limiter
        self.rate_limiter = get_rate_limiter()

        logger.info(f"DhanBroker initialized - Dry Run: {self.dry_run}, Live Data: {self.live_data}")

    def is_dry_run(self) -> bool:
        """Check if broker is in dry run mode for trading operations."""
        return self.dry_run

    def is_live_data_enabled(self) -> bool:
        """Check if live data fetching is enabled."""
        return self.live_data

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Make HTTP request to DhanHQ API

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            data: Request data for POST/PUT requests

        Returns:
            API response as dictionary
        """
        url = f"{self.base_url}{endpoint}"

        # Determine if we should simulate this request
        should_simulate = False

        if self.dry_run:
            # Always simulate trading operations in dry-run mode
            if endpoint.startswith("/orders"):
                should_simulate = True
            # For data endpoints, only simulate if live_data is disabled
            elif not self.live_data and (endpoint.startswith("/holdings") or
                                       endpoint.startswith("/positions") or
                                       endpoint.startswith("/fundlimit") or
                                       endpoint.startswith("/user/profile")):
                should_simulate = True
            # Market data endpoints - allow live data even in dry run for realistic simulation
            # These endpoints (/marketfeed/quote, /charts/historical) should use live data when available

        if should_simulate:
            return self._simulate_request(method, endpoint, data)

        # Make actual API request
        for attempt in range(self.max_retries + 1):
            try:
                if method == "GET":
                    response = self.session.get(url, timeout=self.timeout)
                elif method == "POST":
                    response = self.session.post(url, json=data, timeout=self.timeout)
                elif method == "PUT":
                    response = self.session.put(url, json=data, timeout=self.timeout)
                elif method == "DELETE":
                    response = self.session.delete(url, timeout=self.timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                response.raise_for_status()
                return response.json()

            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries:
                    logger.warning(f"Request failed (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error(f"Request failed after {self.max_retries + 1} attempts: {e}")
                    return {"error": str(e), "status": "failed"}

    def _simulate_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Simulate API requests in dry run mode

        Args:
            method: HTTP method
            endpoint: API endpoint
            data: Request data

        Returns:
            Simulated response
        """
        if method == "POST" and endpoint == "/orders":
            return self._simulate_place_order(data)
        elif method == "PUT" and "/orders/" in endpoint:
            order_id = endpoint.split("/")[-1]
            return self._simulate_modify_order(order_id, data)
        elif method == "DELETE" and "/orders/" in endpoint:
            order_id = endpoint.split("/")[-1]
            return self._simulate_cancel_order(order_id)
        elif method == "GET" and endpoint == "/orders":
            return self._simulate_get_orders()
        elif method == "GET" and "/orders/" in endpoint:
            order_id = endpoint.split("/")[-1]
            return self._simulate_get_order(order_id)
        elif method == "GET" and endpoint == "/trades":
            return self._simulate_get_trades()
        elif method == "GET" and endpoint == "/holdings":
            return self._simulate_get_holdings()
        elif method == "GET" and endpoint == "/positions":
            return self._simulate_get_positions()
        elif method == "GET" and endpoint == "/fundlimit":
            return self._simulate_get_fund_limit()
        elif method == "POST" and endpoint == "/margincalculator":
            return self._simulate_calculate_margin(data)
        elif method == "GET" and endpoint == "/user/profile":
            return self._simulate_get_user_profile()
        else:
            return {"status": "success", "message": "Dry run simulation"}

    def place_order(self,
                   security_id: str,
                   exchange_segment: Union[str, ExchangeSegment],
                   transaction_type: Union[str, TransactionType],
                   quantity: int,
                   product_type: Union[str, ProductType],
                   order_type: Union[str, OrderType],
                   validity: Union[str, Validity] = "DAY",
                   price: Optional[float] = None,
                   trigger_price: Optional[float] = None,
                   disclosed_quantity: Optional[int] = None,
                   after_market_order: bool = False,
                   amo_time: Optional[Union[str, AMOTime]] = None,
                   bo_profit_value: Optional[float] = None,
                   bo_stop_loss_value: Optional[float] = None,
                   correlation_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Place a new order

        Args:
            security_id: Exchange standard ID for the scrip
            exchange_segment: Exchange segment (NSE_EQ, BSE_EQ, etc.)
            transaction_type: BUY or SELL
            quantity: Number of shares
            product_type: CNC, INTRADAY, MARGIN, MTF, CO, BO
            order_type: LIMIT, MARKET, STOP_LOSS, STOP_LOSS_MARKET
            validity: DAY or IOC
            price: Price for limit orders
            trigger_price: Trigger price for stop loss orders
            disclosed_quantity: Visible quantity in market depth
            after_market_order: Flag for AMO
            amo_time: AMO timing (PRE_OPEN, OPEN, OPEN_30, OPEN_60)
            bo_profit_value: Bracket order profit value
            bo_stop_loss_value: Bracket/Cover order stop loss value
            correlation_id: User-defined tracking ID

        Returns:
            Dictionary with orderId and orderStatus
        """
        # Convert enums to strings if needed
        if isinstance(exchange_segment, ExchangeSegment):
            exchange_segment = exchange_segment.value
        if isinstance(transaction_type, TransactionType):
            transaction_type = transaction_type.value
        if isinstance(product_type, ProductType):
            product_type = product_type.value
        if isinstance(order_type, OrderType):
            order_type = order_type.value
        if isinstance(validity, Validity):
            validity = validity.value
        if isinstance(amo_time, AMOTime):
            amo_time = amo_time.value

        # Validate required parameters
        if not security_id:
            return {"error": "security_id is required"}
        if quantity <= 0:
            return {"error": "quantity must be positive"}

        # Validate order type specific requirements
        if order_type in ["LIMIT", "STOP_LOSS"] and price is None:
            return {"error": f"price is required for {order_type} orders"}
        if order_type in ["STOP_LOSS", "STOP_LOSS_MARKET"] and trigger_price is None:
            return {"error": f"trigger_price is required for {order_type} orders"}

        # Validate bracket/cover order requirements
        if product_type == "BO" and (bo_profit_value is None or bo_stop_loss_value is None):
            return {"error": "BO orders require both bo_profit_value and bo_stop_loss_value"}
        if product_type == "CO" and bo_stop_loss_value is None:
            return {"error": "CO orders require bo_stop_loss_value"}

        # Prepare request data
        order_data = {
            "dhanClientId": self.client_id,
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "transactionType": transaction_type,
            "quantity": str(quantity),
            "productType": product_type,
            "orderType": order_type,
            "validity": validity,
            "afterMarketOrder": after_market_order
        }

        # Add optional parameters
        if price is not None:
            order_data["price"] = str(price)
        if trigger_price is not None:
            order_data["triggerPrice"] = str(trigger_price)
        if disclosed_quantity is not None:
            order_data["disclosedQuantity"] = str(disclosed_quantity)
        if amo_time is not None:
            order_data["amoTime"] = amo_time
        if bo_profit_value is not None:
            order_data["boProfitValue"] = str(bo_profit_value)
        if bo_stop_loss_value is not None:
            order_data["boStopLossValue"] = str(bo_stop_loss_value)
        if correlation_id is not None:
            order_data["correlationId"] = correlation_id

        # Make API request
        response = self._make_request("POST", "/orders", order_data)
        return response

    def modify_order(self,
                    order_id: str,
                    order_type: Union[str, OrderType],
                    validity: Union[str, Validity],
                    quantity: Optional[int] = None,
                    price: Optional[float] = None,
                    trigger_price: Optional[float] = None,
                    disclosed_quantity: Optional[int] = None,
                    leg_name: Optional[Union[str, LegName]] = None) -> Dict[str, Any]:
        """
        Modify a pending order

        Args:
            order_id: Order ID to modify
            order_type: LIMIT, MARKET, STOP_LOSS, STOP_LOSS_MARKET
            validity: DAY or IOC
            quantity: New quantity
            price: New price
            trigger_price: New trigger price
            disclosed_quantity: New disclosed quantity
            leg_name: For BO/CO orders (ENTRY_LEG, TARGET_LEG, STOP_LOSS_LEG)

        Returns:
            Dictionary with orderId and orderStatus
        """
        # Convert enums to strings if needed
        if isinstance(order_type, OrderType):
            order_type = order_type.value
        if isinstance(validity, Validity):
            validity = validity.value
        if isinstance(leg_name, LegName):
            leg_name = leg_name.value

        # Prepare request data
        modify_data = {
            "dhanClientId": self.client_id,
            "orderId": order_id,
            "orderType": order_type,
            "validity": validity
        }

        # Add optional parameters
        if quantity is not None:
            modify_data["quantity"] = str(quantity)
        if price is not None:
            modify_data["price"] = str(price)
        if trigger_price is not None:
            modify_data["triggerPrice"] = str(trigger_price)
        if disclosed_quantity is not None:
            modify_data["disclosedQuantity"] = str(disclosed_quantity)
        if leg_name is not None:
            modify_data["legName"] = leg_name

        # Make API request
        response = self._make_request("PUT", f"/orders/{order_id}", modify_data)
        return response

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """
        Cancel a pending order

        Args:
            order_id: Order ID to cancel

        Returns:
            Dictionary with orderId and orderStatus
        """
        response = self._make_request("DELETE", f"/orders/{order_id}")
        return response

    def slice_order(self,
                   security_id: str,
                   exchange_segment: Union[str, ExchangeSegment],
                   transaction_type: Union[str, TransactionType],
                   quantity: int,
                   product_type: Union[str, ProductType],
                   order_type: Union[str, OrderType],
                   validity: Union[str, Validity] = "DAY",
                   price: Optional[float] = None,
                   trigger_price: Optional[float] = None,
                   disclosed_quantity: Optional[int] = None,
                   after_market_order: bool = False,
                   amo_time: Optional[Union[str, AMOTime]] = None,
                   bo_profit_value: Optional[float] = None,
                   bo_stop_loss_value: Optional[float] = None,
                   correlation_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Place a sliced order (for quantities over freeze limit)

        Args: Same as place_order

        Returns:
            List of dictionaries with orderId and orderStatus for each slice
        """
        # Convert enums to strings if needed
        if isinstance(exchange_segment, ExchangeSegment):
            exchange_segment = exchange_segment.value
        if isinstance(transaction_type, TransactionType):
            transaction_type = transaction_type.value
        if isinstance(product_type, ProductType):
            product_type = product_type.value
        if isinstance(order_type, OrderType):
            order_type = order_type.value
        if isinstance(validity, Validity):
            validity = validity.value
        if isinstance(amo_time, AMOTime):
            amo_time = amo_time.value

        # Prepare request data (same structure as place_order)
        order_data = {
            "dhanClientId": self.client_id,
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "transactionType": transaction_type,
            "quantity": str(quantity),
            "productType": product_type,
            "orderType": order_type,
            "validity": validity,
            "afterMarketOrder": after_market_order
        }

        # Add optional parameters
        if price is not None:
            order_data["price"] = str(price)
        if trigger_price is not None:
            order_data["triggerPrice"] = str(trigger_price)
        if disclosed_quantity is not None:
            order_data["disclosedQuantity"] = str(disclosed_quantity)
        if amo_time is not None:
            order_data["amoTime"] = amo_time
        if bo_profit_value is not None:
            order_data["boProfitValue"] = str(bo_profit_value)
        if bo_stop_loss_value is not None:
            order_data["boStopLossValue"] = str(bo_stop_loss_value)
        if correlation_id is not None:
            order_data["correlationId"] = correlation_id

        # Make API request
        response = self._make_request("POST", "/orders/slicing", order_data)
        return response if isinstance(response, list) else [response]

    def get_orders(self) -> List[Dict[str, Any]]:
        """
        Get all orders for the day

        Returns:
            List of order dictionaries
        """
        response = self._make_request("GET", "/orders")
        return response if isinstance(response, list) else []

    def get_order_by_id(self, order_id: str) -> Dict[str, Any]:
        """
        Get order details by order ID

        Args:
            order_id: Order ID to retrieve

        Returns:
            Order details dictionary
        """
        response = self._make_request("GET", f"/orders/{order_id}")
        return response

    def get_order_by_correlation_id(self, correlation_id: str) -> Dict[str, Any]:
        """
        Get order details by correlation ID

        Args:
            correlation_id: User-defined correlation ID

        Returns:
            Order details dictionary
        """
        response = self._make_request("GET", f"/orders/external/{correlation_id}")
        return response

    def get_trades(self) -> List[Dict[str, Any]]:
        """
        Get all trades for the day

        Returns:
            List of trade dictionaries
        """
        response = self._make_request("GET", "/trades")
        return response if isinstance(response, list) else []

    def get_trades_by_order_id(self, order_id: str) -> List[Dict[str, Any]]:
        """
        Get trades for a specific order ID

        Args:
            order_id: Order ID to get trades for

        Returns:
            List of trade dictionaries for the order
        """
        response = self._make_request("GET", f"/trades/{order_id}")
        return response if isinstance(response, list) else [response] if response else []

    # Portfolio and Funds methods
    def get_holdings(self) -> List[Dict[str, Any]]:
        """
        Get all holdings in demat account

        Returns:
            List of holding dictionaries with details like quantity, avg cost price, etc.
        """
        response = self._make_request("GET", "/holdings")
        return response if isinstance(response, list) else []

    def get_positions(self) -> List[Dict[str, Any]]:
        """
        Get all open positions for the day

        Returns:
            List of position dictionaries with P&L, quantities, etc.
        """
        response = self._make_request("GET", "/positions")
        return response if isinstance(response, list) else []

    def convert_position(self,
                        security_id: str,
                        exchange_segment: Union[str, ExchangeSegment],
                        position_type: str,
                        from_product_type: Union[str, ProductType],
                        to_product_type: Union[str, ProductType],
                        convert_qty: int,
                        trading_symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert position from one product type to another

        Args:
            security_id: Security ID
            exchange_segment: Exchange segment
            position_type: LONG, SHORT, or CLOSED
            from_product_type: Current product type (CNC, INTRADAY, etc.)
            to_product_type: Target product type (CNC, INTRADAY, etc.)
            convert_qty: Quantity to convert
            trading_symbol: Trading symbol (optional)

        Returns:
            Conversion response
        """
        # Convert enums to strings if needed
        if isinstance(exchange_segment, ExchangeSegment):
            exchange_segment = exchange_segment.value
        if isinstance(from_product_type, ProductType):
            from_product_type = from_product_type.value
        if isinstance(to_product_type, ProductType):
            to_product_type = to_product_type.value

        convert_data = {
            "dhanClientId": self.client_id,
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "positionType": position_type,
            "fromProductType": from_product_type,
            "toProductType": to_product_type,
            "convertQty": str(convert_qty)
        }

        if trading_symbol:
            convert_data["tradingSymbol"] = trading_symbol

        response = self._make_request("POST", "/positions/convert", convert_data)
        return response

    def get_fund_limit(self) -> Dict[str, Any]:
        """
        Get trading account fund information

        Returns:
            Dictionary with available balance, utilized amount, etc.
        """
        response = self._make_request("GET", "/fundlimit")
        return response

    def calculate_margin(self,
                        security_id: str,
                        exchange_segment: Union[str, ExchangeSegment],
                        transaction_type: Union[str, TransactionType],
                        quantity: int,
                        product_type: Union[str, ProductType],
                        price: float,
                        trigger_price: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate margin requirement for an order

        Args:
            security_id: Security ID
            exchange_segment: Exchange segment
            transaction_type: BUY or SELL
            quantity: Order quantity
            product_type: Product type
            price: Order price
            trigger_price: Trigger price for SL orders

        Returns:
            Dictionary with margin requirements, leverage, etc.
        """
        # Convert enums to strings if needed
        if isinstance(exchange_segment, ExchangeSegment):
            exchange_segment = exchange_segment.value
        if isinstance(transaction_type, TransactionType):
            transaction_type = transaction_type.value
        if isinstance(product_type, ProductType):
            product_type = product_type.value

        margin_data = {
            "dhanClientId": self.client_id,
            "securityId": security_id,
            "exchangeSegment": exchange_segment,
            "transactionType": transaction_type,
            "quantity": quantity,
            "productType": product_type,
            "price": price
        }

        if trigger_price is not None:
            margin_data["triggerPrice"] = trigger_price

        response = self._make_request("POST", "/margincalculator", margin_data)
        return response

    # Symbol-based order methods (RapidTrader integration)
    def place_order_by_symbol(self,
                             symbol: str,
                             exchange_segment: Union[str, ExchangeSegment] = "NSE_EQ",
                             transaction_type: Union[str, TransactionType] = "BUY",
                             quantity: int = 1,
                             product_type: Union[str, ProductType] = "CNC",
                             order_type: Union[str, OrderType] = "LIMIT",
                             price: Optional[float] = None,
                             **kwargs) -> Dict[str, Any]:
        """
        Place an order using RapidTrader symbol (automatically converts to security_id).

        This method provides a RapidTrader-friendly interface that accepts symbols
        like "RELIANCE", "TCS" etc. and automatically converts them to DhanHQ security IDs.

        Args:
            symbol: RapidTrader symbol (e.g., "RELIANCE", "TCS")
            exchange_segment: Exchange segment (NSE_EQ, BSE_EQ, etc.)
            transaction_type: BUY or SELL
            quantity: Number of shares
            product_type: CNC, INTRADAY, MARGIN, MTF, CO, BO
            order_type: LIMIT, MARKET, STOP_LOSS, STOP_LOSS_MARKET
            price: Order price
            **kwargs: Additional order parameters

        Returns:
            Dictionary with order response
        """
        # Convert exchange segment to string if needed
        if isinstance(exchange_segment, ExchangeSegment):
            exchange_segment = exchange_segment.value

        # Convert symbol to security ID
        security_id = symbol_to_security_id(symbol, exchange_segment)

        if not security_id:
            return {
                "error": f"Security ID not found for symbol: {symbol} on {exchange_segment}",
                "symbol": symbol,
                "exchange": exchange_segment,
                "suggestion": "Add the symbol mapping using add_symbol_mapping() or use place_order() with security_id directly"
            }

        # Call the main place_order method with security_id
        return self.place_order(
            security_id=security_id,
            exchange_segment=exchange_segment,
            transaction_type=transaction_type,
            quantity=quantity,
            product_type=product_type,
            order_type=order_type,
            price=price,
            **kwargs
        )

    def add_symbol_mapping(self, symbol: str, security_id: str, exchange: str = "NSE_EQ") -> bool:
        """
        Add a new symbol to security ID mapping.

        Args:
            symbol: RapidTrader symbol (e.g., "NEWSTOCK")
            security_id: DhanHQ security ID (e.g., "12345")
            exchange: Exchange segment (e.g., "NSE_EQ")

        Returns:
            True if added successfully
        """
        mapper = get_symbol_mapper()
        if mapper:
            success = mapper.add_mapping(symbol, security_id, exchange)
            if success:
                mapper.save_mappings()
                logger.info(f"Added symbol mapping: {symbol} -> {security_id} on {exchange}")
            return success
        else:
            logger.error("Symbol mapper not available")
            return False

    def get_security_id(self, symbol: str, exchange: str = "NSE_EQ") -> Optional[str]:
        """
        Get DhanHQ security ID for a RapidTrader symbol.

        Args:
            symbol: RapidTrader symbol
            exchange: Exchange segment

        Returns:
            DhanHQ security ID or None
        """
        return symbol_to_security_id(symbol, exchange)

    def get_available_symbols(self, exchange: str = "NSE_EQ") -> List[str]:
        """
        Get list of available symbols for an exchange.

        Args:
            exchange: Exchange segment

        Returns:
            List of available symbols
        """
        mapper = get_symbol_mapper()
        if mapper:
            return mapper.get_all_symbols(exchange)
        else:
            return []

    def get_symbol_mapping_stats(self) -> Dict[str, Any]:
        """
        Get statistics about symbol mappings.

        Returns:
            Dictionary with mapping statistics
        """
        mapper = get_symbol_mapper()
        if mapper:
            return mapper.get_mapping_stats()
        else:
            return {"error": "Symbol mapper not available"}

    # Dry run simulation methods
    def _simulate_place_order(self, data: Dict) -> Dict[str, Any]:
        """Simulate order placement in dry run mode"""
        order_id = f"DRY_{self._next_order_id:010d}"
        self._next_order_id += 1

        order_info = {
            "dhanClientId": self.client_id,
            "orderId": order_id,
            "correlationId": data.get("correlationId", ""),
            "orderStatus": "PENDING",
            "transactionType": data.get("transactionType"),
            "exchangeSegment": data.get("exchangeSegment"),
            "productType": data.get("productType"),
            "orderType": data.get("orderType"),
            "validity": data.get("validity"),
            "securityId": data.get("securityId"),
            "quantity": int(data.get("quantity", 0)),
            "disclosedQuantity": int(data.get("disclosedQuantity", 0)),
            "price": float(data.get("price", 0)),
            "triggerPrice": float(data.get("triggerPrice", 0)),
            "afterMarketOrder": data.get("afterMarketOrder", False),
            "boProfitValue": float(data.get("boProfitValue", 0)),
            "boStopLossValue": float(data.get("boStopLossValue", 0)),
            "createTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "updateTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "exchangeTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "remainingQuantity": int(data.get("quantity", 0)),
            "averageTradedPrice": 0,
            "filledQty": 0
        }

        self._dry_run_orders[order_id] = order_info

        logger.info(f"DRY RUN: Order placed - {order_id} for {data.get('quantity')} "
                   f"shares of {data.get('securityId')} at ₹{data.get('price', 0)}")

        return {"orderId": order_id, "orderStatus": "PENDING"}

    def _simulate_modify_order(self, order_id: str, data: Dict) -> Dict[str, Any]:
        """Simulate order modification in dry run mode"""
        if order_id in self._dry_run_orders:
            order = self._dry_run_orders[order_id]
            if data.get("quantity"):
                order["quantity"] = int(data["quantity"])
                order["remainingQuantity"] = int(data["quantity"])
            if data.get("price"):
                order["price"] = float(data["price"])
            if data.get("triggerPrice"):
                order["triggerPrice"] = float(data["triggerPrice"])
            if data.get("orderType"):
                order["orderType"] = data["orderType"]
            if data.get("validity"):
                order["validity"] = data["validity"]

            order["updateTime"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            order["orderStatus"] = "TRANSIT"

            logger.info(f"DRY RUN: Order modified - {order_id}")
            return {"orderId": order_id, "orderStatus": "TRANSIT"}
        else:
            return {"error": "Order not found"}

    def _simulate_cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Simulate order cancellation in dry run mode"""
        if order_id in self._dry_run_orders:
            self._dry_run_orders[order_id]["orderStatus"] = "CANCELLED"
            self._dry_run_orders[order_id]["updateTime"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            logger.info(f"DRY RUN: Order cancelled - {order_id}")
            return {"orderId": order_id, "orderStatus": "CANCELLED"}
        else:
            return {"error": "Order not found"}

    def _simulate_get_orders(self) -> List[Dict[str, Any]]:
        """Simulate getting all orders in dry run mode"""
        return list(self._dry_run_orders.values())

    def _simulate_get_order(self, order_id: str) -> Dict[str, Any]:
        """Simulate getting specific order in dry run mode"""
        return self._dry_run_orders.get(order_id, {"error": "Order not found"})

    def _simulate_get_trades(self) -> List[Dict[str, Any]]:
        """Simulate getting all trades in dry run mode"""
        return list(self._dry_run_trades.values())

    def _simulate_get_holdings(self) -> List[Dict[str, Any]]:
        """Simulate getting holdings in dry run mode"""
        return [
            {
                "exchange": "NSE",
                "tradingSymbol": "RELIANCE",
                "securityId": "2885",
                "isin": "INE002A01018",
                "totalQty": 100,
                "dpQty": 100,
                "t1Qty": 0,
                "availableQty": 100,
                "collateralQty": 0,
                "avgCostPrice": 2450.0
            },
            {
                "exchange": "NSE",
                "tradingSymbol": "TCS",
                "securityId": "11536",
                "isin": "INE467B01029",
                "totalQty": 50,
                "dpQty": 50,
                "t1Qty": 0,
                "availableQty": 50,
                "collateralQty": 0,
                "avgCostPrice": 3200.0
            }
        ]

    def _simulate_get_positions(self) -> List[Dict[str, Any]]:
        """Simulate getting positions in dry run mode"""
        return [
            {
                "dhanClientId": self.client_id,
                "tradingSymbol": "HDFCBANK",
                "securityId": "1333",
                "positionType": "LONG",
                "exchangeSegment": "NSE_EQ",
                "productType": "INTRADAY",
                "buyAvg": 1650.0,
                "buyQty": 20,
                "costPrice": 1650.0,
                "sellAvg": 0.0,
                "sellQty": 0,
                "netQty": 20,
                "realizedProfit": 0.0,
                "unrealizedProfit": 500.0,
                "rbiReferenceRate": 1.0,
                "multiplier": 1,
                "carryForwardBuyQty": 0,
                "carryForwardSellQty": 0,
                "carryForwardBuyValue": 0.0,
                "carryForwardSellValue": 0.0,
                "dayBuyQty": 20,
                "daySellQty": 0,
                "dayBuyValue": 33000.0,
                "daySellValue": 0.0,
                "drvExpiryDate": "0001-01-01",
                "drvOptionType": None,
                "drvStrikePrice": 0.0,
                "crossCurrency": False
            }
        ]

    def _simulate_get_fund_limit(self) -> Dict[str, Any]:
        """Simulate getting fund limit in dry run mode"""
        return {
            "dhanClientId": self.client_id,
            "availableBalance": 150000.0,  # Fixed typo: was "availabelBalance"
            "totalBalance": 200000.0,
            "sodLimit": 200000.0,
            "collateralAmount": 0.0,
            "receiveableAmount": 0.0,
            "utilizedAmount": 50000.0,
            "blockedPayoutAmount": 0.0,
            "withdrawableBalance": 145000.0
        }

    def _simulate_calculate_margin(self, data: Dict) -> Dict[str, Any]:
        """Simulate margin calculation in dry run mode"""
        quantity = data.get("quantity", 1)
        price = data.get("price", 1000.0)
        product_type = data.get("productType", "CNC")

        # Simple margin calculation simulation
        order_value = quantity * price

        if product_type == "CNC":
            total_margin = order_value  # 100% margin for delivery
            leverage = "1.00"
        elif product_type == "INTRADAY":
            total_margin = order_value * 0.2  # 20% margin for intraday
            leverage = "5.00"
        else:
            total_margin = order_value * 0.1  # 10% margin for F&O
            leverage = "10.00"

        return {
            "totalMargin": total_margin,
            "spanMargin": total_margin * 0.6,
            "exposureMargin": total_margin * 0.3,
            "availableBalance": 150000.0,
            "variableMargin": total_margin * 0.1,
            "insufficientBalance": max(0, total_margin - 150000.0),
            "brokerage": 20.0,
            "leverage": leverage
        }

    def _simulate_get_user_profile(self) -> Dict[str, Any]:
        """Simulate getting user profile in dry run mode"""
        return {
            "dhanClientId": self.client_id or "DRY_RUN_CLIENT",
            "tokenValidity": "2024-12-31",
            "activeSegment": "NSE_EQ,NSE_FNO,BSE_EQ",
            "ddpi": "Y",
            "mtf": "Y",
            "dataPlan": "PREMIUM",
            "dataValidity": "2024-12-31"
        }

    # User Profile and Account Information methods
    def get_user_profile(self) -> Dict[str, Any]:
        """
        Get user profile information from DhanHQ API

        Returns:
            Dictionary with user profile information
        """
        if self.dry_run:
            # Return simulated user profile for dry run mode
            return {
                "dhanClientId": self.client_id or "DRY_RUN_CLIENT",
                "tokenValidity": "2024-12-31",
                "activeSegment": "NSE_EQ,NSE_FNO,BSE_EQ",
                "ddpi": "Y",
                "mtf": "Y",
                "dataPlan": "PREMIUM",
                "dataValidity": "2024-12-31"
            }

        try:
            response = self._make_request("GET", "/user/profile")
            return response
        except Exception as e:
            logger.error(f"Failed to get user profile: {e}")
            return {}

    # Market Data methods
    def get_quote(self, symbol: str, exchange: str = "NSE_EQ") -> Dict[str, Any]:
        """
        Get live market quote for a symbol.

        Args:
            symbol: Trading symbol
            exchange: Exchange segment

        Returns:
            Dictionary with live quote data
        """
        try:
            # Convert symbol to security ID
            security_id = symbol_to_security_id(symbol, exchange)

            if not security_id:
                logger.warning(f"Security ID not found for symbol: {symbol}")
                return {"error": f"Security ID not found for symbol: {symbol}"}

            # Make API request for live quote
            quote_data = {
                "securityId": security_id,
                "exchangeSegment": exchange
            }

            response = self._make_request("POST", "/marketfeed/quote", quote_data)

            if isinstance(response, dict) and "data" in response:
                quote = response["data"]
                return {
                    "symbol": symbol,
                    "ltp": quote.get("LTP", 0),
                    "open": quote.get("open", 0),
                    "high": quote.get("high", 0),
                    "low": quote.get("low", 0),
                    "close": quote.get("close", 0),
                    "volume": quote.get("volume", 0),
                    "change": quote.get("change", 0),
                    "changePercent": quote.get("pChange", 0)
                }
            else:
                return {"error": "Invalid response format"}

        except Exception as e:
            logger.error(f"Failed to get quote for {symbol}: {e}")
            return {"error": str(e)}

    def get_ohlc(self, symbol: str, exchange: str = "NSE_EQ",
                 interval: str = "1d", count: int = 100) -> Dict[str, Any]:
        """
        Get OHLC data for a symbol.

        Args:
            symbol: Trading symbol
            exchange: Exchange segment
            interval: Data interval (1m, 5m, 15m, 1h, 1d)
            count: Number of candles to fetch

        Returns:
            Dictionary with OHLC data
        """
        try:
            # Convert symbol to security ID
            security_id = symbol_to_security_id(symbol, exchange)

            if not security_id:
                logger.warning(f"Security ID not found for symbol: {symbol}")
                return {"error": f"Security ID not found for symbol: {symbol}"}

            # Make API request for OHLC data
            ohlc_data = {
                "securityId": security_id,
                "exchangeSegment": exchange,
                "instrument": interval,
                "count": count
            }

            response = self._make_request("POST", "/charts/historical", ohlc_data)

            if isinstance(response, dict) and "data" in response:
                return response["data"]
            else:
                return {"error": "Invalid response format"}

        except Exception as e:
            logger.error(f"Failed to get OHLC for {symbol}: {e}")
            return {"error": str(e)}

    def get_enhanced_user_info(self) -> Dict[str, Any]:
        """
        Get comprehensive user information including profile, financial data, and capabilities

        Returns:
            Dictionary with user_profile, broker_info, and trading_capabilities
        """
        try:
            # Get basic user profile
            user_profile = self.get_user_profile()

            # Get fund information
            fund_info = self.get_fund_limit()

            # Get holdings and positions for portfolio value calculation
            holdings = self.get_holdings()
            positions = self.get_positions()

            # Calculate portfolio metrics
            portfolio_value = 0
            current_risk = 0

            # Calculate portfolio value from holdings
            for holding in holdings:
                if isinstance(holding, dict):
                    quantity = holding.get('quantity', 0)
                    current_price = holding.get('currentPrice', holding.get('avgCostPrice', 0))
                    portfolio_value += quantity * current_price

            # Calculate current risk from positions
            for position in positions:
                if isinstance(position, dict):
                    unrealized_pnl = position.get('unrealizedPnl', 0)
                    if unrealized_pnl < 0:  # Only count losses as risk
                        current_risk += abs(unrealized_pnl)

            # Extract financial information
            available_balance = 0
            total_balance = 0
            used_margin = 0

            if isinstance(fund_info, dict):
                available_balance = fund_info.get('availableBalance', 0)
                total_balance = fund_info.get('totalBalance', available_balance)
                used_margin = fund_info.get('utilizedAmount', 0)

            # Calculate risk percentage
            risk_percentage = (current_risk / max(total_balance, 1)) * 100 if total_balance > 0 else 0

            # Determine trading capabilities based on profile
            active_segments = user_profile.get('activeSegment', '').split(',') if user_profile.get('activeSegment') else []

            broker_info = {
                'available_balance': available_balance,
                'total_balance': total_balance,
                'used_margin': used_margin,
                'portfolio_value': portfolio_value,
                'current_risk': current_risk,
                'risk_percentage': risk_percentage
            }

            trading_capabilities = {
                'can_trade_equity': any('EQ' in segment for segment in active_segments),
                'can_trade_derivatives': any('FNO' in segment for segment in active_segments),
                'can_trade_currency': any('CURRENCY' in segment for segment in active_segments),
                'can_trade_commodity': any('COMM' in segment for segment in active_segments),
                'ddpi_active': user_profile.get('ddpi') == 'Y',
                'mtf_active': user_profile.get('mtf') == 'Y',
                'data_plan_active': user_profile.get('dataPlan') in ['PREMIUM', 'BASIC']
            }

            return {
                'user_profile': user_profile,
                'broker_info': broker_info,
                'trading_capabilities': trading_capabilities
            }

        except Exception as e:
            logger.error(f"Failed to get enhanced user info: {e}")
            return {
                'user_profile': {},
                'broker_info': {},
                'trading_capabilities': {}
            }

    # Utility methods
    def is_dry_run(self) -> bool:
        """Check if broker is in dry run mode"""
        return self.dry_run

    def get_client_id(self) -> str:
        """Get the DhanHQ client ID"""
        return self.client_id

    def clear_dry_run_data(self) -> None:
        """Clear all dry run orders and trades data"""
        if self.dry_run:
            self._dry_run_orders.clear()
            self._dry_run_trades.clear()
            self._next_order_id = 1
            logger.info("DRY RUN: Cleared all orders and trades data")

    def get_dry_run_summary(self) -> Dict[str, Any]:
        """Get summary of dry run orders and trades"""
        if not self.dry_run:
            return {"error": "Not in dry run mode"}

        total_orders = len(self._dry_run_orders)
        pending_orders = len([o for o in self._dry_run_orders.values() if o["orderStatus"] == "PENDING"])
        cancelled_orders = len([o for o in self._dry_run_orders.values() if o["orderStatus"] == "CANCELLED"])
        total_trades = len(self._dry_run_trades)

        return {
            "total_orders": total_orders,
            "pending_orders": pending_orders,
            "cancelled_orders": cancelled_orders,
            "total_trades": total_trades,
            "next_order_id": self._next_order_id
        }


# Helper functions for easy order creation
def create_equity_order(security_id: str,
                       quantity: int,
                       transaction_type: str,
                       price: float,
                       product_type: str = "CNC",
                       exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create equity order parameters

    Args:
        security_id: Security ID
        quantity: Quantity
        transaction_type: BUY or SELL
        price: Order price
        product_type: CNC or INTRADAY
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters
    """
    return {
        "security_id": security_id,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": product_type,
        "order_type": "LIMIT",
        "price": price,
        "validity": "DAY"
    }


def create_market_order(security_id: str,
                       quantity: int,
                       transaction_type: str,
                       product_type: str = "CNC",
                       exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create market order parameters

    Args:
        security_id: Security ID
        quantity: Quantity
        transaction_type: BUY or SELL
        product_type: CNC or INTRADAY
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters
    """
    return {
        "security_id": security_id,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": product_type,
        "order_type": "MARKET",
        "validity": "DAY"
    }


def create_stop_loss_order(security_id: str,
                          quantity: int,
                          transaction_type: str,
                          price: float,
                          trigger_price: float,
                          product_type: str = "CNC",
                          exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create stop loss order parameters

    Args:
        security_id: Security ID
        quantity: Quantity
        transaction_type: BUY or SELL
        price: Limit price
        trigger_price: Trigger price
        product_type: CNC or INTRADAY
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters
    """
    return {
        "security_id": security_id,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": product_type,
        "order_type": "STOP_LOSS",
        "price": price,
        "trigger_price": trigger_price,
        "validity": "DAY"
    }


def create_bracket_order(security_id: str,
                        quantity: int,
                        transaction_type: str,
                        price: float,
                        profit_value: float,
                        stop_loss_value: float,
                        exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create bracket order parameters

    Args:
        security_id: Security ID
        quantity: Quantity
        transaction_type: BUY or SELL
        price: Entry price
        profit_value: Target profit value
        stop_loss_value: Stop loss value
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters
    """
    return {
        "security_id": security_id,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": "BO",
        "order_type": "LIMIT",
        "price": price,
        "bo_profit_value": profit_value,
        "bo_stop_loss_value": stop_loss_value,
        "validity": "DAY"
    }


# Symbol-based helper functions (RapidTrader integration)
def create_equity_order_by_symbol(symbol: str,
                                 quantity: int,
                                 transaction_type: str,
                                 price: float,
                                 product_type: str = "CNC",
                                 exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create equity order parameters using symbol.

    Args:
        symbol: RapidTrader symbol (e.g., "RELIANCE")
        quantity: Quantity
        transaction_type: BUY or SELL
        price: Order price
        product_type: CNC or INTRADAY
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters for place_order_by_symbol()
    """
    return {
        "symbol": symbol,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": product_type,
        "order_type": "LIMIT",
        "price": price,
        "validity": "DAY"
    }


def create_market_order_by_symbol(symbol: str,
                                 quantity: int,
                                 transaction_type: str,
                                 product_type: str = "CNC",
                                 exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create market order parameters using symbol.

    Args:
        symbol: RapidTrader symbol (e.g., "RELIANCE")
        quantity: Quantity
        transaction_type: BUY or SELL
        product_type: CNC or INTRADAY
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters for place_order_by_symbol()
    """
    return {
        "symbol": symbol,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": product_type,
        "order_type": "MARKET",
        "validity": "DAY"
    }


def create_stop_loss_order_by_symbol(symbol: str,
                                    quantity: int,
                                    transaction_type: str,
                                    price: float,
                                    trigger_price: float,
                                    product_type: str = "CNC",
                                    exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create stop loss order parameters using symbol.

    Args:
        symbol: RapidTrader symbol (e.g., "RELIANCE")
        quantity: Quantity
        transaction_type: BUY or SELL
        price: Limit price
        trigger_price: Trigger price
        product_type: CNC or INTRADAY
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters for place_order_by_symbol()
    """
    return {
        "symbol": symbol,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": product_type,
        "order_type": "STOP_LOSS",
        "price": price,
        "trigger_price": trigger_price,
        "validity": "DAY"
    }


def create_bracket_order_by_symbol(symbol: str,
                                  quantity: int,
                                  transaction_type: str,
                                  price: float,
                                  profit_value: float,
                                  stop_loss_value: float,
                                  exchange_segment: str = "NSE_EQ") -> Dict[str, Any]:
    """
    Helper function to create bracket order parameters using symbol.

    Args:
        symbol: RapidTrader symbol (e.g., "RELIANCE")
        quantity: Quantity
        transaction_type: BUY or SELL
        price: Entry price
        profit_value: Target profit value
        stop_loss_value: Stop loss value
        exchange_segment: NSE_EQ or BSE_EQ

    Returns:
        Dictionary with order parameters for place_order_by_symbol()
    """
    return {
        "symbol": symbol,
        "exchange_segment": exchange_segment,
        "transaction_type": transaction_type,
        "quantity": quantity,
        "product_type": "BO",
        "order_type": "LIMIT",
        "price": price,
        "bo_profit_value": profit_value,
        "bo_stop_loss_value": stop_loss_value,
        "validity": "DAY"
    }
