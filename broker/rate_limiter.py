"""
API Rate Limiter for RapidTrader

This module provides comprehensive rate limiting functionality for API requests
to ensure compliance with broker API limits and prevent rate limit violations.
"""

import time
import threading
import logging
import os
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from enum import Enum
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
root_dir = Path(__file__).resolve().parent.parent
dotenv_path = root_dir / '.env'
load_dotenv(dotenv_path=dotenv_path)

# Configure logging
logger = logging.getLogger("rate_limiter")
logger.setLevel(logging.WARNING)  # Reduce log noise

class APIEndpointType(Enum):
    """API endpoint types with different rate limits."""
    ORDER_PLACEMENT = "order_placement"
    OHLC_DATA = "ohlc_data"
    MARKET_QUOTES = "market_quotes"
    NON_TRADING = "non_trading"

class RateLimitViolation(Exception):
    """Exception raised when rate limit is exceeded."""
    pass

class RateLimiter:
    """
    Thread-safe rate limiter for API requests.

    Supports multiple time windows (second, minute, hour, day) and different
    endpoint types with their specific limits.
    """

    def __init__(self):
        """Initialize the rate limiter with configuration from environment variables."""
        self.enabled = os.getenv("RATE_LIMITING_ENABLED", "true").lower() == "true"
        self.strict_mode = os.getenv("RATE_LIMITING_STRICT_MODE", "true").lower() == "true"
        self.log_violations = os.getenv("RATE_LIMITING_LOG_VIOLATIONS", "true").lower() == "true"

        # Thread locks for thread safety
        self._locks = defaultdict(threading.Lock)

        # Request history for each endpoint type and time window
        self._request_history = defaultdict(lambda: defaultdict(deque))

        # Rate limits configuration
        self._rate_limits = self._load_rate_limits()

        # Order modification tracking
        self._order_modifications = defaultdict(int)
        self._order_mod_lock = threading.Lock()

        logger.info(f"RateLimiter initialized - Enabled: {self.enabled}, Strict: {self.strict_mode}")

    def _load_rate_limits(self) -> Dict[APIEndpointType, Dict[str, int]]:
        """Load rate limits from environment variables."""
        return {
            APIEndpointType.ORDER_PLACEMENT: {
                "second": int(os.getenv("DHAN_ORDER_RATE_LIMIT_PER_SECOND", "25")),
                "minute": int(os.getenv("DHAN_ORDER_RATE_LIMIT_PER_MINUTE", "250")),
                "hour": int(os.getenv("DHAN_ORDER_RATE_LIMIT_PER_HOUR", "1000")),
                "day": int(os.getenv("DHAN_ORDER_RATE_LIMIT_PER_DAY", "7000"))
            },
            APIEndpointType.OHLC_DATA: {
                "second": int(os.getenv("DHAN_OHLC_RATE_LIMIT_PER_SECOND", "10")),
                "day": int(os.getenv("DHAN_OHLC_RATE_LIMIT_PER_DAY", "100000"))
            },
            APIEndpointType.MARKET_QUOTES: {
                "second": int(os.getenv("DHAN_QUOTES_RATE_LIMIT_PER_SECOND", "1"))
            },
            APIEndpointType.NON_TRADING: {
                "second": int(os.getenv("DHAN_NON_TRADING_RATE_LIMIT_PER_SECOND", "20"))
            }
        }

    def _get_time_windows(self) -> Dict[str, int]:
        """Get time window durations in seconds."""
        return {
            "second": 1,
            "minute": 60,
            "hour": 3600,
            "day": 86400
        }

    def _cleanup_old_requests(self, endpoint_type: APIEndpointType, time_window: str):
        """Remove old requests outside the time window."""
        window_duration = self._get_time_windows()[time_window]
        cutoff_time = time.time() - window_duration

        history = self._request_history[endpoint_type][time_window]
        while history and history[0] < cutoff_time:
            history.popleft()

    def _check_rate_limit(self, endpoint_type: APIEndpointType, time_window: str) -> bool:
        """Check if request would exceed rate limit for given time window."""
        if not self.enabled:
            return True

        limits = self._rate_limits.get(endpoint_type, {})
        if time_window not in limits:
            return True  # No limit defined for this time window

        limit = limits[time_window]
        history = self._request_history[endpoint_type][time_window]

        # Clean up old requests
        self._cleanup_old_requests(endpoint_type, time_window)

        # Check if we're at the limit
        return len(history) < limit

    def can_make_request(self, endpoint_type: APIEndpointType) -> Dict[str, Any]:
        """
        Check if a request can be made without violating rate limits.

        Args:
            endpoint_type: Type of API endpoint

        Returns:
            Dictionary with status and details
        """
        if not self.enabled:
            return {"allowed": True, "reason": "Rate limiting disabled"}

        with self._locks[endpoint_type]:
            limits = self._rate_limits.get(endpoint_type, {})

            for time_window in limits.keys():
                if not self._check_rate_limit(endpoint_type, time_window):
                    limit = limits[time_window]
                    current_count = len(self._request_history[endpoint_type][time_window])

                    return {
                        "allowed": False,
                        "reason": f"Rate limit exceeded for {time_window}",
                        "limit": limit,
                        "current": current_count,
                        "time_window": time_window,
                        "endpoint_type": endpoint_type.value
                    }

            return {"allowed": True, "reason": "Within rate limits"}

    def record_request(self, endpoint_type: APIEndpointType, dry_run: bool = False):
        """
        Record a request and check rate limits.

        Args:
            endpoint_type: Type of API endpoint
            dry_run: If True, don't actually record the request (for dry-run mode)

        Raises:
            RateLimitViolation: If rate limit would be exceeded and strict mode is enabled
        """
        if not self.enabled:
            return

        # Check rate limits
        check_result = self.can_make_request(endpoint_type)

        if not check_result["allowed"]:
            message = (f"Rate limit violation: {check_result['reason']} "
                      f"({check_result.get('current', 0)}/{check_result.get('limit', 0)} "
                      f"for {check_result.get('time_window', 'unknown')})")

            if self.log_violations:
                logger.warning(message)

            if self.strict_mode and not dry_run:
                raise RateLimitViolation(message)

        # Record the request if not in dry-run mode
        if not dry_run:
            with self._locks[endpoint_type]:
                current_time = time.time()
                limits = self._rate_limits.get(endpoint_type, {})

                for time_window in limits.keys():
                    self._request_history[endpoint_type][time_window].append(current_time)

    def record_order_modification(self, order_id: str, dry_run: bool = False) -> bool:
        """
        Record an order modification and check if limit is exceeded.

        Args:
            order_id: Unique order identifier
            dry_run: If True, don't actually record the modification

        Returns:
            True if modification is allowed, False otherwise
        """
        if not self.enabled:
            return True

        max_modifications = int(os.getenv("DHAN_MAX_MODIFICATIONS_PER_ORDER", "25"))

        with self._order_mod_lock:
            current_mods = self._order_modifications[order_id]

            if current_mods >= max_modifications:
                if self.log_violations:
                    logger.warning(f"Order modification limit exceeded for order {order_id}: "
                                 f"{current_mods}/{max_modifications}")
                return False

            if not dry_run:
                self._order_modifications[order_id] += 1

            return True

    def get_rate_limit_status(self) -> Dict[str, Any]:
        """Get current rate limit status for all endpoint types."""
        if not self.enabled:
            return {"enabled": False, "message": "Rate limiting disabled"}

        status = {"enabled": True, "endpoints": {}}

        for endpoint_type in APIEndpointType:
            endpoint_status = {}
            limits = self._rate_limits.get(endpoint_type, {})

            with self._locks[endpoint_type]:
                for time_window, limit in limits.items():
                    self._cleanup_old_requests(endpoint_type, time_window)
                    current_count = len(self._request_history[endpoint_type][time_window])

                    endpoint_status[time_window] = {
                        "limit": limit,
                        "current": current_count,
                        "remaining": max(0, limit - current_count),
                        "percentage_used": (current_count / limit * 100) if limit > 0 else 0
                    }

            status["endpoints"][endpoint_type.value] = endpoint_status

        return status

    def wait_for_rate_limit(self, endpoint_type: APIEndpointType, max_wait: float = 60.0) -> bool:
        """
        Wait until a request can be made without violating rate limits.

        Args:
            endpoint_type: Type of API endpoint
            max_wait: Maximum time to wait in seconds

        Returns:
            True if request can now be made, False if max_wait exceeded
        """
        if not self.enabled:
            return True

        start_time = time.time()

        while time.time() - start_time < max_wait:
            check_result = self.can_make_request(endpoint_type)
            if check_result["allowed"]:
                return True

            # Wait for the shortest time window to reset
            time.sleep(0.1)  # Check every 100ms

        return False

# Global rate limiter instance
_rate_limiter = None
_rate_limiter_lock = threading.Lock()

def get_rate_limiter() -> RateLimiter:
    """Get the global rate limiter instance (singleton pattern)."""
    global _rate_limiter

    if _rate_limiter is None:
        with _rate_limiter_lock:
            if _rate_limiter is None:
                _rate_limiter = RateLimiter()

    return _rate_limiter
