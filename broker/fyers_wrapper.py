"""
Fyers API v3 Broker Wrapper for RapidTrader

This module provides a comprehensive wrapper for the Fyers API v3
Based on official documentation: https://myapi.fyers.in/docsv3

Features:
- Complete order management (place, modify, cancel)
- Order book and trade book retrieval
- Dry run mode for testing
- Rate limiting compliance
- Error handling and validation
- Token management and refresh
"""

import os
import logging
import time
import json
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Callable
from pathlib import Path
from dotenv import load_dotenv

# Import Fyers API v3
try:
    from fyers_apiv3 import fyersModel
    from fyers_apiv3.FyersWebsocket import data_ws
    _has_fyers_api = True
except ImportError:
    _has_fyers_api = False
    fyersModel = None
    data_ws = None

# Import WebSocket manager
try:
    from broker.fyers_websocket_manager import FyersWebSocketManager
    _has_fyers_ws_manager = True
except ImportError:
    _has_fyers_ws_manager = False
    FyersWebSocketManager = None

# Import rate limiter
try:
    from broker.rate_limiter import get_rate_limiter, APIEndpointType
except ImportError:
    # Fallback if rate limiter is not available
    def get_rate_limiter():
        return None
    class APIEndpointType:
        ORDER_PLACEMENT = "order_placement"
        MARKET_QUOTES = "market_quotes"
        NON_TRADING = "non_trading"

# Load environment variables
try:
    root_dir = Path(__file__).resolve().parent.parent
    dotenv_path = root_dir / '.env'
    load_dotenv(dotenv_path=dotenv_path)
except Exception:
    # If dotenv loading fails, continue without it
    pass

# Configure logging
logger = logging.getLogger("FyersBroker")


class FyersTransactionType(Enum):
    """Transaction types for Fyers API"""
    BUY = 1
    SELL = -1


class FyersExchange(Enum):
    """Exchange segments for Fyers API"""
    NSE = "NSE"
    BSE = "BSE"
    MCX = "MCX"
    NCDEX = "NCDEX"


class FyersProductType(Enum):
    """Product types for Fyers API"""
    CNC = "CNC"           # Cash & Carry
    INTRADAY = "INTRADAY" # Intraday
    MARGIN = "MARGIN"     # Margin
    CO = "CO"             # Cover Order
    BO = "BO"             # Bracket Order


class FyersOrderType(Enum):
    """Order types for Fyers API"""
    LIMIT = 1
    MARKET = 2
    STOP = 3
    STOPLIMIT = 4


class FyersValidity(Enum):
    """Order validity for Fyers API"""
    DAY = 1
    IOC = 3


class FyersOrderStatus(Enum):
    """Order status for Fyers API"""
    CANCELLED = 1
    TRADED = 2
    FOR_CANCEL = 3
    TRANSIT = 4
    REJECTED = 5
    PENDING = 6
    EXPIRED = 7


class FyersBroker:
    """
    Fyers API v3 Broker implementation for RapidTrader

    Provides complete order management functionality with dry run support
    """

    def __init__(self,
                 client_id: Optional[str] = None,
                 access_token: Optional[str] = None,
                 refresh_token: Optional[str] = None,
                 dry_run: Optional[bool] = None,
                 live_data: Optional[bool] = None):
        """
        Initialize FyersBroker

        Args:
            client_id: Fyers client ID (from .env if not provided)
            access_token: Fyers access token (from .env if not provided)
            refresh_token: Fyers refresh token (from .env if not provided)
            dry_run: Enable dry run mode for trading operations (from .env if not provided)
            live_data: Enable live data fetching even in dry-run mode (default: True)
        """
        if not _has_fyers_api:
            raise ImportError("fyers-apiv3 package is required. Install with: pip install fyers-apiv3")

        # Load configuration from environment
        self.client_id = client_id or os.getenv("FYERS_CLIENT_ID")
        self.access_token = access_token or os.getenv("FYERS_ACCESS_TOKEN")
        self.refresh_token = refresh_token or os.getenv("FYERS_REFRESH_TOKEN")

        # Dry run mode for trading operations
        if dry_run is None:
            dry_run = os.getenv("DRY_RUN_ENABLED", "true").lower() == "true"
        self.dry_run = dry_run

        # Live data mode - allows fetching real balance/position data even in dry-run
        if live_data is None:
            live_data = os.getenv("LIVE_DATA_ENABLED", "true").lower() == "true"
        self.live_data = live_data

        # API configuration
        self.timeout = int(os.getenv("API_REQUEST_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("API_MAX_RETRIES", "3"))
        self.retry_delay = float(os.getenv("API_RETRY_DELAY", "1"))

        # Validate credentials
        if not self.client_id or not self.access_token:
            logger.warning("Fyers credentials not found. Set FYERS_CLIENT_ID and FYERS_ACCESS_TOKEN in .env")

        # Initialize Fyers API client
        self.fyers = None
        if self.client_id and self.access_token:
            try:
                self.fyers = fyersModel.FyersModel(
                    client_id=self.client_id,
                    token=self.access_token,
                    log_path=""  # Disable logging to file
                )
                logger.info("Fyers API client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Fyers API client: {e}")

        # Dry run order tracking
        self._dry_run_orders = {}
        self._dry_run_trades = {}
        self._next_order_id = 1

        # Token refresh tracking
        self._token_expires_at = None
        self._last_token_refresh = None

        # Initialize rate limiter
        self.rate_limiter = get_rate_limiter()

        # WebSocket manager for real-time data
        self.ws_manager = None
        self.ws_enabled = False

        logger.info(f"FyersBroker initialized - Dry Run: {self.dry_run}, Live Data: {self.live_data}")

    def is_dry_run(self) -> bool:
        """Check if broker is in dry run mode for trading operations."""
        return self.dry_run

    def is_live_data_enabled(self) -> bool:
        """Check if live data fetching is enabled."""
        return self.live_data

    def _should_simulate_request(self, endpoint_type: str) -> bool:
        """
        Determine if a request should be simulated based on dry run and live data settings.

        Args:
            endpoint_type: Type of request ('trading', 'data', 'account')

        Returns:
            True if request should be simulated, False if real API call should be made
        """
        if not self.dry_run:
            return False

        if endpoint_type == 'trading':
            # Always simulate trading operations in dry-run mode
            return True
        elif endpoint_type == 'data' and not self.live_data:
            # Simulate data requests only if live_data is disabled
            return True
        elif endpoint_type == 'account' and not self.live_data:
            # Simulate account requests only if live_data is disabled
            return True

        return False

    def _check_token_validity(self) -> bool:
        """
        Check if the current access token is still valid.

        Returns:
            True if token is valid, False if needs refresh
        """
        if not self.fyers:
            return False

        try:
            # Try to get user profile to check token validity
            response = self.fyers.get_profile()
            return response.get('s') == 'ok'
        except Exception as e:
            logger.warning(f"Token validation failed: {e}")
            return False

    def refresh_access_token(self) -> Dict[str, Any]:
        """
        Refresh the access token using the refresh token.

        Returns:
            Dictionary with new token information or error
        """
        if not self.refresh_token:
            return {"error": "No refresh token available"}

        try:
            # Note: Fyers API v3 token refresh implementation
            # This is a placeholder - actual implementation depends on Fyers API documentation
            logger.info("Refreshing Fyers access token...")

            # For now, return success - implement actual refresh logic based on Fyers docs
            return {
                "status": "success",
                "message": "Token refresh not yet implemented",
                "access_token": self.access_token
            }

        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return {"error": str(e)}

    def _make_api_call(self, method_name: str, *args, **kwargs) -> Dict[str, Any]:
        """
        Make an API call with error handling and rate limiting.

        Args:
            method_name: Name of the Fyers API method to call
            *args: Positional arguments for the method
            **kwargs: Keyword arguments for the method

        Returns:
            API response as dictionary
        """
        if not self.fyers:
            return {"error": "Fyers API client not initialized"}

        # Determine endpoint type for rate limiting
        endpoint_type = APIEndpointType.NON_TRADING
        if 'order' in method_name.lower():
            endpoint_type = APIEndpointType.ORDER_PLACEMENT
        elif 'quote' in method_name.lower() or 'depth' in method_name.lower():
            endpoint_type = APIEndpointType.MARKET_QUOTES

        # Check rate limits
        if self.rate_limiter:
            try:
                self.rate_limiter.record_request(endpoint_type, dry_run=self.dry_run)
            except Exception as e:
                logger.warning(f"Rate limit check failed: {e}")

        # Make API call with retries
        for attempt in range(self.max_retries + 1):
            try:
                method = getattr(self.fyers, method_name)
                response = method(*args, **kwargs)

                # Check if token needs refresh
                if response.get('code') == 401 or response.get('s') == 'error':
                    if 'token' in response.get('message', '').lower():
                        logger.info("Token expired, attempting refresh...")
                        refresh_result = self.refresh_access_token()
                        if refresh_result.get('status') == 'success':
                            # Retry the original request
                            continue

                return response

            except Exception as e:
                if attempt < self.max_retries:
                    logger.warning(f"API call failed (attempt {attempt + 1}): {e}")
                    time.sleep(self.retry_delay * (attempt + 1))
                else:
                    logger.error(f"API call failed after {self.max_retries + 1} attempts: {e}")
                    return {"error": str(e), "s": "error"}

        return {"error": "Max retries exceeded", "s": "error"}

    def place_order(self,
                   symbol: str,
                   qty: int,
                   side: Union[int, FyersTransactionType],
                   type: Union[int, FyersOrderType],
                   productType: Union[str, FyersProductType] = "CNC",
                   limitPrice: Optional[float] = None,
                   stopPrice: Optional[float] = None,
                   validity: Union[int, FyersValidity] = 1,
                   disclosedQty: Optional[int] = None,
                   offlineOrder: bool = False,
                   stopLoss: Optional[float] = None,
                   takeProfit: Optional[float] = None) -> Dict[str, Any]:
        """
        Place a new order using Fyers API v3

        Args:
            symbol: Trading symbol in Fyers format (e.g., "NSE:SBIN-EQ", "BSE:RELIANCE-EQ")
            qty: Quantity to trade
            side: 1 for BUY, -1 for SELL
            type: Order type (1=LIMIT, 2=MARKET, 3=STOP, 4=STOPLIMIT)
            productType: Product type (CNC, INTRADAY, MARGIN, CO, BO)
            limitPrice: Limit price for limit orders
            stopPrice: Stop price for stop orders
            validity: Order validity (1=DAY, 3=IOC)
            disclosedQty: Disclosed quantity
            offlineOrder: Whether this is an offline order
            stopLoss: Stop loss price for bracket orders
            takeProfit: Take profit price for bracket orders

        Returns:
            Dictionary with order response
        """
        # Convert enums to values if needed
        if isinstance(side, FyersTransactionType):
            side = side.value
        if isinstance(type, FyersOrderType):
            type = type.value
        if isinstance(productType, FyersProductType):
            productType = productType.value
        if isinstance(validity, FyersValidity):
            validity = validity.value

        # Validate required parameters
        if not symbol:
            return {"error": "symbol is required", "s": "error"}
        if qty <= 0:
            return {"error": "qty must be positive", "s": "error"}
        if side not in [1, -1]:
            return {"error": "side must be 1 (BUY) or -1 (SELL)", "s": "error"}

        # Validate symbol format (should be like "NSE:SBIN-EQ")
        if ":" not in symbol:
            return {"error": "symbol must be in format 'EXCHANGE:SYMBOL-SERIES' (e.g., 'NSE:SBIN-EQ')", "s": "error"}

        # Validate order type specific requirements
        if type in [1, 4] and limitPrice is None:  # LIMIT or STOPLIMIT
            return {"error": f"limitPrice is required for order type {type}", "s": "error"}
        if type in [3, 4] and stopPrice is None:  # STOP or STOPLIMIT
            return {"error": f"stopPrice is required for order type {type}", "s": "error"}

        # Check if this should be simulated
        if self._should_simulate_request('trading'):
            return self._simulate_place_order(symbol, qty, side, type, productType,
                                            limitPrice, stopPrice, validity, disclosedQty)

        # Prepare order data according to Fyers API v3 format
        order_data = {
            "symbol": symbol,
            "qty": qty,
            "side": side,
            "type": type,
            "productType": productType,
            "validity": validity,
            "offlineOrder": offlineOrder
        }

        # Add optional parameters
        if limitPrice is not None:
            order_data["limitPrice"] = limitPrice
        if stopPrice is not None:
            order_data["stopPrice"] = stopPrice
        if disclosedQty is not None:
            order_data["disclosedQty"] = disclosedQty
        if stopLoss is not None:
            order_data["stopLoss"] = stopLoss
        if takeProfit is not None:
            order_data["takeProfit"] = takeProfit

        # Make API call
        response = self._make_api_call("place_order", data=order_data)
        return response

    def modify_order(self,
                    id: str,
                    qty: Optional[int] = None,
                    type: Optional[Union[int, FyersOrderType]] = None,
                    limitPrice: Optional[float] = None,
                    stopPrice: Optional[float] = None,
                    disclosedQty: Optional[int] = None) -> Dict[str, Any]:
        """
        Modify a pending order

        Args:
            id: Order ID to modify
            qty: New quantity
            type: New order type
            limitPrice: New limit price
            stopPrice: New stop price
            disclosedQty: New disclosed quantity

        Returns:
            Dictionary with modification response
        """
        # Convert enums to values if needed
        if isinstance(type, FyersOrderType):
            type = type.value

        # Check if this should be simulated
        if self._should_simulate_request('trading'):
            return self._simulate_modify_order(id, qty, type, limitPrice, stopPrice, disclosedQty)

        # Prepare modification data
        modify_data = {"id": id}

        # Add parameters to modify
        if qty is not None:
            modify_data["qty"] = qty
        if type is not None:
            modify_data["type"] = type
        if limitPrice is not None:
            modify_data["limitPrice"] = limitPrice
        if stopPrice is not None:
            modify_data["stopPrice"] = stopPrice
        if disclosedQty is not None:
            modify_data["disclosedQty"] = disclosedQty

        # Make API call
        response = self._make_api_call("modify_order", data=modify_data)
        return response

    def cancel_order(self, id: str) -> Dict[str, Any]:
        """
        Cancel a pending order

        Args:
            id: Order ID to cancel

        Returns:
            Dictionary with cancellation response
        """
        # Check if this should be simulated
        if self._should_simulate_request('trading'):
            return self._simulate_cancel_order(id)

        # Make API call
        response = self._make_api_call("cancel_order", data={"id": id})
        return response

    def get_orders(self) -> Dict[str, Any]:
        """
        Get all orders

        Returns:
            Dictionary with orders list
        """
        # Check if this should be simulated
        if self._should_simulate_request('trading'):
            return self._simulate_get_orders()

        # Make API call
        response = self._make_api_call("orderbook")
        return response

    def get_order_status(self, id: str) -> Dict[str, Any]:
        """
        Get status of a specific order

        Args:
            id: Order ID

        Returns:
            Dictionary with order status
        """
        # Check if this should be simulated
        if self._should_simulate_request('trading'):
            return self._simulate_get_order_status(id)

        # Make API call
        response = self._make_api_call("get_order_status", data={"id": id})
        return response

    def get_trades(self) -> Dict[str, Any]:
        """
        Get all trades

        Returns:
            Dictionary with trades list
        """
        # Check if this should be simulated
        if self._should_simulate_request('trading'):
            return self._simulate_get_trades()

        # Make API call
        response = self._make_api_call("tradebook")
        return response

    def get_positions(self) -> Dict[str, Any]:
        """
        Get current positions

        Returns:
            Dictionary with positions
        """
        # Check if this should be simulated
        if self._should_simulate_request('account'):
            return self._simulate_get_positions()

        # Make API call
        response = self._make_api_call("positions")
        return response

    def get_holdings(self) -> Dict[str, Any]:
        """
        Get current holdings

        Returns:
            Dictionary with holdings
        """
        # Check if this should be simulated
        if self._should_simulate_request('account'):
            return self._simulate_get_holdings()

        # Make API call
        response = self._make_api_call("holdings")
        return response

    def get_funds(self) -> Dict[str, Any]:
        """
        Get fund limits and available balance

        Returns:
            Dictionary with fund information
        """
        # Check if this should be simulated
        if self._should_simulate_request('account'):
            return self._simulate_get_funds()

        # Make API call
        response = self._make_api_call("funds")
        return response

    def get_profile(self) -> Dict[str, Any]:
        """
        Get user profile information

        Returns:
            Dictionary with profile information
        """
        # Check if this should be simulated
        if self._should_simulate_request('account'):
            return self._simulate_get_profile()

        # Make API call
        response = self._make_api_call("get_profile")
        return response

    # Simulation methods for dry run mode
    def _simulate_place_order(self, symbol: str, qty: int, side: int, type: int,
                             productType: str, limitPrice: Optional[float],
                             stopPrice: Optional[float], validity: int,
                             disclosedQty: Optional[int]) -> Dict[str, Any]:
        """Simulate placing an order in dry run mode."""
        order_id = f"FYERS_DRY_{self._next_order_id:06d}"
        self._next_order_id += 1

        order = {
            "id": order_id,
            "symbol": symbol,
            "qty": qty,
            "side": side,
            "type": type,
            "productType": productType,
            "limitPrice": limitPrice,
            "stopPrice": stopPrice,
            "validity": validity,
            "disclosedQty": disclosedQty,
            "status": FyersOrderStatus.PENDING.value,
            "filledQty": 0,
            "remainingQty": qty,
            "avgPrice": 0.0,
            "orderDateTime": datetime.now().isoformat(),
            "message": "Order placed successfully (DRY RUN)"
        }

        self._dry_run_orders[order_id] = order

        # Simulate immediate execution for market orders
        if type == 2:  # MARKET order
            self._simulate_order_execution(order_id)

        return {
            "s": "ok",
            "code": 200,
            "message": "Order placed successfully",
            "data": {"id": order_id}
        }

    def _simulate_modify_order(self, order_id: str, qty: Optional[int], type: Optional[int],
                              limitPrice: Optional[float], stopPrice: Optional[float],
                              disclosedQty: Optional[int]) -> Dict[str, Any]:
        """Simulate modifying an order in dry run mode."""
        if order_id not in self._dry_run_orders:
            return {
                "s": "error",
                "code": 404,
                "message": "Order not found"
            }

        order = self._dry_run_orders[order_id]

        # Update order parameters
        if qty is not None:
            order["qty"] = qty
            order["remainingQty"] = qty - order["filledQty"]
        if type is not None:
            order["type"] = type
        if limitPrice is not None:
            order["limitPrice"] = limitPrice
        if stopPrice is not None:
            order["stopPrice"] = stopPrice
        if disclosedQty is not None:
            order["disclosedQty"] = disclosedQty

        order["message"] = "Order modified successfully (DRY RUN)"

        return {
            "s": "ok",
            "code": 200,
            "message": "Order modified successfully",
            "data": {"id": order_id}
        }

    def _simulate_cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Simulate canceling an order in dry run mode."""
        if order_id not in self._dry_run_orders:
            return {
                "s": "error",
                "code": 404,
                "message": "Order not found"
            }

        order = self._dry_run_orders[order_id]
        order["status"] = FyersOrderStatus.CANCELLED.value
        order["message"] = "Order cancelled successfully (DRY RUN)"

        return {
            "s": "ok",
            "code": 200,
            "message": "Order cancelled successfully",
            "data": {"id": order_id}
        }

    def _simulate_order_execution(self, order_id: str, price: Optional[float] = None):
        """Simulate order execution for market orders."""
        if order_id not in self._dry_run_orders:
            return

        order = self._dry_run_orders[order_id]

        # Simulate execution price (use limit price or a mock price)
        if price is None:
            if order["limitPrice"]:
                price = order["limitPrice"]
            else:
                # Mock price based on symbol (simplified)
                price = 100.0  # Default mock price

        # Execute the order
        order["status"] = FyersOrderStatus.TRADED.value
        order["filledQty"] = order["qty"]
        order["remainingQty"] = 0
        order["avgPrice"] = price
        order["message"] = "Order executed successfully (DRY RUN)"

        # Create a trade record
        trade_id = f"FYERS_TRADE_{len(self._dry_run_trades) + 1:06d}"
        trade = {
            "id": trade_id,
            "orderId": order_id,
            "symbol": order["symbol"],
            "qty": order["qty"],
            "side": order["side"],
            "price": price,
            "productType": order["productType"],
            "tradeDateTime": datetime.now().isoformat(),
            "message": "Trade executed (DRY RUN)"
        }

        self._dry_run_trades[trade_id] = trade

    def _simulate_get_orders(self) -> Dict[str, Any]:
        """Simulate getting all orders in dry run mode."""
        orders = list(self._dry_run_orders.values())

        return {
            "s": "ok",
            "code": 200,
            "message": "Orders retrieved successfully",
            "data": {"orderBook": orders}
        }

    def _simulate_get_order_status(self, order_id: str) -> Dict[str, Any]:
        """Simulate getting order status in dry run mode."""
        if order_id not in self._dry_run_orders:
            return {
                "s": "error",
                "code": 404,
                "message": "Order not found"
            }

        order = self._dry_run_orders[order_id]

        return {
            "s": "ok",
            "code": 200,
            "message": "Order status retrieved successfully",
            "data": order
        }

    def _simulate_get_trades(self) -> Dict[str, Any]:
        """Simulate getting all trades in dry run mode."""
        trades = list(self._dry_run_trades.values())

        return {
            "s": "ok",
            "code": 200,
            "message": "Trades retrieved successfully",
            "data": {"tradeBook": trades}
        }

    def _simulate_get_positions(self) -> Dict[str, Any]:
        """Simulate getting positions in dry run mode."""
        # Calculate positions from trades
        positions = {}

        for trade in self._dry_run_trades.values():
            symbol = trade["symbol"]
            qty = trade["qty"] if trade["side"] == 1 else -trade["qty"]
            price = trade["price"]

            if symbol not in positions:
                positions[symbol] = {
                    "symbol": symbol,
                    "qty": 0,
                    "avgPrice": 0.0,
                    "pnl": 0.0,
                    "productType": trade["productType"]
                }

            pos = positions[symbol]
            total_value = pos["qty"] * pos["avgPrice"] + qty * price
            pos["qty"] += qty

            if pos["qty"] != 0:
                pos["avgPrice"] = total_value / pos["qty"]
            else:
                pos["avgPrice"] = 0.0

        position_list = [pos for pos in positions.values() if pos["qty"] != 0]

        return {
            "s": "ok",
            "code": 200,
            "message": "Positions retrieved successfully (DRY RUN)",
            "data": {"netPositions": position_list}
        }

    def _simulate_get_holdings(self) -> Dict[str, Any]:
        """Simulate getting holdings in dry run mode."""
        return {
            "s": "ok",
            "code": 200,
            "message": "Holdings retrieved successfully (DRY RUN)",
            "data": {"holdings": []}
        }

    def _simulate_get_funds(self) -> Dict[str, Any]:
        """Simulate getting fund information in dry run mode."""
        return {
            "s": "ok",
            "code": 200,
            "message": "Fund limits retrieved successfully (DRY RUN)",
            "data": {
                "fund_limit": [
                    {
                        "title": "Total Balance",
                        "equityAmount": 100000.0,
                        "commodityAmount": 0.0
                    },
                    {
                        "title": "Available Balance",
                        "equityAmount": 95000.0,
                        "commodityAmount": 0.0
                    },
                    {
                        "title": "Used Balance",
                        "equityAmount": 5000.0,
                        "commodityAmount": 0.0
                    }
                ]
            }
        }

    def _simulate_get_profile(self) -> Dict[str, Any]:
        """Simulate getting user profile in dry run mode."""
        return {
            "s": "ok",
            "code": 200,
            "message": "Profile retrieved successfully (DRY RUN)",
            "data": {
                "fy_id": "DRY_RUN_USER",
                "email_id": "<EMAIL>",
                "name": "Dry Run User",
                "display_name": "DRY RUN",
                "PAN": "DRYRUN1234",
                "image": "",
                "totp": False,
                "pin": False
            }
        }

    # Market data methods
    def get_quotes(self, symbols: Union[str, List[str]]) -> Dict[str, Any]:
        """
        Get market quotes for symbols

        Args:
            symbols: Single symbol or list of symbols (e.g., "NSE:SBIN-EQ")

        Returns:
            Dictionary with quote data
        """
        if isinstance(symbols, str):
            symbols = [symbols]

        # Make API call
        response = self._make_api_call("quotes", data={"symbols": ",".join(symbols)})
        return response

    def get_quote(self, symbol: str, exchange: str = "NSE_EQ") -> Dict[str, Any]:
        """
        Get market quote for a single symbol (compatibility method for dry-run engine)

        Args:
            symbol: Trading symbol (e.g., "SBIN")
            exchange: Exchange segment (ignored for Fyers, kept for compatibility)

        Returns:
            Dictionary with quote data for the symbol in DhanBroker format
        """
        # Convert symbol to Fyers format if needed
        if ":" not in symbol:
            # Convert DhanHQ format to Fyers format
            fyers_symbol = f"NSE:{symbol}-EQ"
        else:
            fyers_symbol = symbol

        response = self.get_quotes([fyers_symbol])

        # Extract the quote for the specific symbol from the response
        if response.get('s') == 'ok' and 'd' in response:
            quotes_data = response['d']
            if quotes_data and len(quotes_data) > 0:
                quote_item = quotes_data[0]
                # The actual quote data is in the 'v' field
                quote_data = quote_item.get('v', {})
                # Convert to DhanBroker format for compatibility
                return {
                    "symbol": symbol,
                    "ltp": quote_data.get("lp", 0),  # Last price
                    "open": quote_data.get("open_price", 0),
                    "high": quote_data.get("high_price", 0),
                    "low": quote_data.get("low_price", 0),
                    "close": quote_data.get("prev_close_price", 0),
                    "volume": quote_data.get("volume", 0),
                    "change": quote_data.get("ch", 0),
                    "changePercent": quote_data.get("chp", 0)
                }

        # Return simulated data if live data is not available (market closed or API issues)
        if self.dry_run:
            return self._get_simulated_quote(symbol)

        # Return error in DhanBroker format
        return {"error": f"Failed to get quote for {symbol}"}

    def _get_simulated_quote(self, symbol: str) -> Dict[str, Any]:
        """
        Generate simulated quote data for dry-run mode when live data is not available.

        Args:
            symbol: Trading symbol

        Returns:
            Dictionary with simulated quote data
        """
        import random

        # Base prices for common symbols (approximate market prices)
        base_prices = {
            "SBIN": 850.0,
            "RELIANCE": 2800.0,
            "TCS": 4200.0,
            "INFY": 1850.0,
            "HDFCBANK": 1650.0,
            "ICICIBANK": 1250.0,
            "WIPRO": 550.0,
            "LT": 3600.0,
            "BHARTIARTL": 1550.0,
            "ITC": 460.0
        }

        # Extract symbol name from various formats
        clean_symbol = symbol.replace("NSE:", "").replace("-EQ", "").upper()

        # Get base price or use default
        base_price = base_prices.get(clean_symbol, 1000.0)

        # Add some random variation (±2%)
        variation = random.uniform(-0.02, 0.02)
        current_price = base_price * (1 + variation)

        # Generate OHLC data
        open_price = current_price * random.uniform(0.98, 1.02)
        high_price = max(open_price, current_price) * random.uniform(1.0, 1.01)
        low_price = min(open_price, current_price) * random.uniform(0.99, 1.0)

        # Generate volume
        volume = random.randint(100000, 1000000)

        # Calculate change
        prev_close = base_price
        change = current_price - prev_close
        change_percent = (change / prev_close) * 100

        return {
            "symbol": symbol,
            "ltp": round(current_price, 2),
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(prev_close, 2),
            "volume": volume,
            "change": round(change, 2),
            "changePercent": round(change_percent, 2)
        }

    def get_market_depth(self, symbol: str, ohlcv_flag: int = 1) -> Dict[str, Any]:
        """
        Get market depth for a symbol

        Args:
            symbol: Trading symbol (e.g., "NSE:SBIN-EQ")
            ohlcv_flag: Include OHLCV data (1=yes, 0=no)

        Returns:
            Dictionary with market depth data
        """
        # Make API call
        response = self._make_api_call("depth", data={
            "symbol": symbol,
            "ohlcv_flag": ohlcv_flag
        })
        return response

    def get_historical_data(self, symbol: str, resolution: str,
                           date_from: str, date_to: str,
                           cont_flag: int = 1) -> Dict[str, Any]:
        """
        Get historical data for a symbol

        Args:
            symbol: Trading symbol (e.g., "NSE:SBIN-EQ")
            resolution: Data resolution ("1", "2", "3", "5", "10", "15", "30", "60", "120", "240", "1D")
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            cont_flag: Continuous flag (1=yes, 0=no)

        Returns:
            Dictionary with historical data
        """
        # Make API call
        response = self._make_api_call("history", data={
            "symbol": symbol,
            "resolution": resolution,
            "date_from": date_from,
            "date_to": date_to,
            "cont_flag": cont_flag
        })
        return response

    # Utility methods
    def get_symbol_master(self, exchange: Optional[str] = None) -> Dict[str, Any]:
        """
        Get symbol master data

        Args:
            exchange: Exchange name (NSE, BSE, MCX, NCDEX) or None for all

        Returns:
            Dictionary with symbol master data
        """
        data = {}
        if exchange:
            data["exchange"] = exchange

        response = self._make_api_call("symbol_master", data=data)
        return response

    def search_symbols(self, query: str) -> Dict[str, Any]:
        """
        Search for symbols

        Args:
            query: Search query

        Returns:
            Dictionary with search results
        """
        response = self._make_api_call("search_symbols", data={"symbol_query": query})
        return response

    # Helper methods for order creation
    def create_market_order(self, symbol: str, qty: int, side: Union[int, FyersTransactionType],
                           productType: Union[str, FyersProductType] = "CNC") -> Dict[str, Any]:
        """
        Create a market order (convenience method)

        Args:
            symbol: Trading symbol
            qty: Quantity
            side: 1 for BUY, -1 for SELL
            productType: Product type

        Returns:
            Order response
        """
        return self.place_order(
            symbol=symbol,
            qty=qty,
            side=side,
            type=FyersOrderType.MARKET,
            productType=productType
        )

    def create_limit_order(self, symbol: str, qty: int, side: Union[int, FyersTransactionType],
                          price: float, productType: Union[str, FyersProductType] = "CNC") -> Dict[str, Any]:
        """
        Create a limit order (convenience method)

        Args:
            symbol: Trading symbol
            qty: Quantity
            side: 1 for BUY, -1 for SELL
            price: Limit price
            productType: Product type

        Returns:
            Order response
        """
        return self.place_order(
            symbol=symbol,
            qty=qty,
            side=side,
            type=FyersOrderType.LIMIT,
            productType=productType,
            limitPrice=price
        )

    def create_stop_loss_order(self, symbol: str, qty: int, side: Union[int, FyersTransactionType],
                              stop_price: float, productType: Union[str, FyersProductType] = "CNC") -> Dict[str, Any]:
        """
        Create a stop loss order (convenience method)

        Args:
            symbol: Trading symbol
            qty: Quantity
            side: 1 for BUY, -1 for SELL
            stop_price: Stop price
            productType: Product type

        Returns:
            Order response
        """
        return self.place_order(
            symbol=symbol,
            qty=qty,
            side=side,
            type=FyersOrderType.STOP,
            productType=productType,
            stopPrice=stop_price
        )

    def create_bracket_order(self, symbol: str, qty: int, side: Union[int, FyersTransactionType],
                            price: float, stop_loss: float, take_profit: float) -> Dict[str, Any]:
        """
        Create a bracket order (convenience method)

        Args:
            symbol: Trading symbol
            qty: Quantity
            side: 1 for BUY, -1 for SELL
            price: Entry price
            stop_loss: Stop loss price
            take_profit: Take profit price

        Returns:
            Order response
        """
        return self.place_order(
            symbol=symbol,
            qty=qty,
            side=side,
            type=FyersOrderType.LIMIT,
            productType=FyersProductType.BO,
            limitPrice=price,
            stopLoss=stop_loss,
            takeProfit=take_profit
        )

    # Status and utility methods
    def get_broker_info(self) -> Dict[str, Any]:
        """Get broker information and status."""
        return {
            "broker_name": "Fyers",
            "api_version": "v3",
            "dry_run": self.dry_run,
            "live_data": self.live_data,
            "client_id": self.client_id[:8] + "..." if self.client_id else None,
            "token_valid": self._check_token_validity(),
            "rate_limiter_enabled": self.rate_limiter is not None
        }

    def __str__(self) -> str:
        """String representation of the broker."""
        return f"FyersBroker(dry_run={self.dry_run}, live_data={self.live_data})"

    def __repr__(self) -> str:
        """Detailed string representation of the broker."""
        return (f"FyersBroker(client_id='{self.client_id[:8]}...', "
                f"dry_run={self.dry_run}, live_data={self.live_data})")

    # WebSocket methods for real-time data
    def enable_websocket(self) -> bool:
        """
        Enable WebSocket for real-time data streaming.

        Returns:
            True if WebSocket enabled successfully, False otherwise
        """
        if not _has_fyers_ws_manager:
            logger.error("WebSocket manager not available. Check fyers_websocket_manager import.")
            return False

        if not self.client_id or not self.access_token:
            logger.error("Client ID and access token required for WebSocket")
            return False

        try:
            if self.ws_manager is None:
                self.ws_manager = FyersWebSocketManager(
                    access_token=self.access_token,
                    client_id=self.client_id
                )

            if self.ws_manager.connect():
                self.ws_enabled = True
                logger.info("WebSocket enabled successfully")
                return True
            else:
                logger.error("Failed to connect WebSocket")
                return False

        except Exception as e:
            logger.error(f"Error enabling WebSocket: {e}")
            return False

    def disable_websocket(self):
        """Disable WebSocket connection."""
        try:
            if self.ws_manager:
                self.ws_manager.disconnect()
            self.ws_enabled = False
            logger.info("WebSocket disabled")
        except Exception as e:
            logger.error(f"Error disabling WebSocket: {e}")

    def is_websocket_connected(self) -> bool:
        """
        Check if WebSocket is connected.

        Returns:
            True if WebSocket is connected, False otherwise
        """
        return self.ws_enabled and self.ws_manager and self.ws_manager.is_connected

    def subscribe_live_quotes(self, symbols: List[str]) -> bool:
        """
        Subscribe to live quotes for symbols.

        Args:
            symbols: List of Fyers symbols (e.g., ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ"])

        Returns:
            True if subscription successful, False otherwise
        """
        if not self.is_websocket_connected():
            logger.error("WebSocket not connected. Call enable_websocket() first.")
            return False

        return self.ws_manager.subscribe_quotes(symbols)

    def subscribe_market_depth(self, symbols: List[str]) -> bool:
        """
        Subscribe to market depth for symbols.

        Args:
            symbols: List of Fyers symbols

        Returns:
            True if subscription successful, False otherwise
        """
        if not self.is_websocket_connected():
            logger.error("WebSocket not connected. Call enable_websocket() first.")
            return False

        return self.ws_manager.subscribe_depth(symbols)

    def unsubscribe_live_quotes(self, symbols: List[str]) -> bool:
        """
        Unsubscribe from live quotes.

        Args:
            symbols: List of Fyers symbols

        Returns:
            True if unsubscription successful, False otherwise
        """
        if not self.is_websocket_connected():
            return False

        return self.ws_manager.unsubscribe_quotes(symbols)

    def unsubscribe_market_depth(self, symbols: List[str]) -> bool:
        """
        Unsubscribe from market depth.

        Args:
            symbols: List of Fyers symbols

        Returns:
            True if unsubscription successful, False otherwise
        """
        if not self.is_websocket_connected():
            return False

        return self.ws_manager.unsubscribe_depth(symbols)

    def get_live_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get latest live quote for a symbol from WebSocket cache.

        Args:
            symbol: Fyers symbol

        Returns:
            Latest quote data or None if not available
        """
        if not self.is_websocket_connected():
            return None

        return self.ws_manager.get_latest_quote(symbol)

    def get_live_depth(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get latest market depth for a symbol from WebSocket cache.

        Args:
            symbol: Fyers symbol

        Returns:
            Latest depth data or None if not available
        """
        if not self.is_websocket_connected():
            return None

        return self.ws_manager.get_latest_depth(symbol)

    def add_quote_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        Add callback for live quote updates.

        Args:
            callback: Function to call on quote update (symbol, quote_data)
        """
        if self.ws_manager:
            self.ws_manager.add_quote_callback(callback)
        else:
            logger.warning("WebSocket manager not initialized")

    def add_depth_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        Add callback for market depth updates.

        Args:
            callback: Function to call on depth update (symbol, depth_data)
        """
        if self.ws_manager:
            self.ws_manager.add_depth_callback(callback)
        else:
            logger.warning("WebSocket manager not initialized")

    def add_order_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Add callback for order updates.

        Args:
            callback: Function to call on order update (order_data)
        """
        if self.ws_manager:
            self.ws_manager.add_order_callback(callback)
        else:
            logger.warning("WebSocket manager not initialized")

    def get_websocket_statistics(self) -> Dict[str, Any]:
        """
        Get WebSocket statistics.

        Returns:
            Dictionary with WebSocket statistics
        """
        if self.ws_manager:
            return self.ws_manager.get_statistics()
        return {"error": "WebSocket manager not initialized"}

    def get_subscribed_symbols(self) -> Dict[str, List[str]]:
        """
        Get currently subscribed symbols.

        Returns:
            Dictionary with subscribed symbols
        """
        if self.ws_manager:
            return self.ws_manager.get_subscribed_symbols()
        return {"symbolData": [], "depthData": []}