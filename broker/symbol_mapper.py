"""
Symbol Mapper for DhanHQ Integration

This module provides mapping between RapidTrader symbols and DhanHQ security IDs.
It handles the conversion from human-readable symbols (like "RELIANCE") to 
DhanHQ's numeric security IDs (like "2885").
"""

import os
import json
import logging
from typing import Dict, Optional, List, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


class SymbolMapper:
    """
    Maps RapidTrader symbols to DhanHQ security IDs and vice versa.
    """
    
    def __init__(self, mapping_file: Optional[str] = None):
        """
        Initialize the symbol mapper.
        
        Args:
            mapping_file: Path to the symbol mapping file
        """
        if mapping_file is None:
            # Default mapping file location
            broker_dir = Path(__file__).parent
            mapping_file = broker_dir / "dhan_symbol_mapping.json"
        
        self.mapping_file = Path(mapping_file)
        self.symbol_to_id: Dict[str, Dict[str, str]] = {}  # {symbol: {exchange: security_id}}
        self.id_to_symbol: Dict[str, Dict[str, str]] = {}  # {security_id: {exchange: symbol}}
        
        # Load existing mappings
        self.load_mappings()
    
    def load_mappings(self) -> bool:
        """
        Load symbol mappings from file.
        
        Returns:
            True if loaded successfully, False otherwise
        """
        try:
            if self.mapping_file.exists():
                with open(self.mapping_file, 'r') as f:
                    data = json.load(f)
                
                self.symbol_to_id = data.get('symbol_to_id', {})
                self.id_to_symbol = data.get('id_to_symbol', {})
                
                logger.info(f"Loaded {len(self.symbol_to_id)} symbol mappings from {self.mapping_file}")
                return True
            else:
                logger.info(f"Mapping file not found: {self.mapping_file}. Will create with default mappings.")
                self._create_default_mappings()
                return True
                
        except Exception as e:
            logger.error(f"Error loading symbol mappings: {e}")
            self._create_default_mappings()
            return False
    
    def save_mappings(self) -> bool:
        """
        Save symbol mappings to file.
        
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            self.mapping_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'symbol_to_id': self.symbol_to_id,
                'id_to_symbol': self.id_to_symbol,
                'metadata': {
                    'version': '1.0',
                    'description': 'Symbol to DhanHQ Security ID mapping',
                    'total_mappings': len(self.symbol_to_id)
                }
            }
            
            with open(self.mapping_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Saved {len(self.symbol_to_id)} symbol mappings to {self.mapping_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving symbol mappings: {e}")
            return False
    
    def _create_default_mappings(self):
        """Create default symbol mappings for common stocks."""
        # Popular NSE stocks with their DhanHQ security IDs
        default_nse_mappings = {
            # Major stocks
            "RELIANCE": "2885",
            "TCS": "11536", 
            "HDFCBANK": "1333",
            "ICICIBANK": "4963",
            "INFY": "408",
            "HINDUNILVR": "356",
            "ITC": "424",
            "SBIN": "3045",
            "BHARTIARTL": "10604",
            "KOTAKBANK": "1922",
            "LT": "2939",
            "BAJFINANCE": "317",
            "AXISBANK": "5900",
            "ASIANPAINT": "236",
            "MARUTI": "10999",
            "HCLTECH": "7229",
            "SUNPHARMA": "3351",
            "TITAN": "3506",
            "ULTRACEMCO": "11532",
            "TATAMOTORS": "3456",
            "ADANIPORTS": "15083",
            "WIPRO": "3787",
            "POWERGRID": "14977",
            "NTPC": "11630",
            "BAJAJ-AUTO": "16669",
            "ONGC": "2475",
            "JSWSTEEL": "11723",
            "GRASIM": "1232",
            "TATASTEEL": "3499",
            "INDUSINDBK": "5258",
            "DRREDDY": "881",
            "NESTLEIND": "17963",
            "COALINDIA": "20374",
            "TECHM": "13538",
            "BPCL": "526",
            "HINDALCO": "1363",
            "DIVISLAB": "10940",
            "SHREECEM": "3103",
            "HEROMOTOCO": "1348",
            "BRITANNIA": "547",
            "CIPLA": "694",
            "EICHERMOT": "910",
            "M&M": "1776",
            "UPL": "11557",
            "IOC": "1624",
            "TATACONSUM": "3432",
            "HDFCLIFE": "1175",
            "SBILIFE": "21808",
            "BAJAJFINSV": "16675"
        }
        
        # Add NSE mappings
        for symbol, security_id in default_nse_mappings.items():
            self.add_mapping(symbol, security_id, "NSE_EQ")
        
        # Save the default mappings
        self.save_mappings()
        logger.info(f"Created default mappings for {len(default_nse_mappings)} NSE symbols")
    
    def add_mapping(self, symbol: str, security_id: str, exchange: str = "NSE_EQ") -> bool:
        """
        Add a symbol to security ID mapping.
        
        Args:
            symbol: RapidTrader symbol (e.g., "RELIANCE")
            security_id: DhanHQ security ID (e.g., "2885")
            exchange: Exchange segment (e.g., "NSE_EQ")
            
        Returns:
            True if added successfully
        """
        try:
            # Add to symbol_to_id mapping
            if symbol not in self.symbol_to_id:
                self.symbol_to_id[symbol] = {}
            self.symbol_to_id[symbol][exchange] = security_id
            
            # Add to id_to_symbol mapping
            if security_id not in self.id_to_symbol:
                self.id_to_symbol[security_id] = {}
            self.id_to_symbol[security_id][exchange] = symbol
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding mapping {symbol} -> {security_id}: {e}")
            return False
    
    def get_security_id(self, symbol: str, exchange: str = "NSE_EQ") -> Optional[str]:
        """
        Get DhanHQ security ID for a symbol.
        
        Args:
            symbol: RapidTrader symbol (e.g., "RELIANCE")
            exchange: Exchange segment (e.g., "NSE_EQ")
            
        Returns:
            DhanHQ security ID or None if not found
        """
        # Handle symbol variations
        clean_symbol = self._clean_symbol(symbol)
        
        # Try exact match first
        if clean_symbol in self.symbol_to_id:
            if exchange in self.symbol_to_id[clean_symbol]:
                return self.symbol_to_id[clean_symbol][exchange]
        
        # Try with different exchange if not found
        if clean_symbol in self.symbol_to_id:
            # Return first available exchange
            exchanges = list(self.symbol_to_id[clean_symbol].keys())
            if exchanges:
                logger.warning(f"Symbol {clean_symbol} not found for {exchange}, using {exchanges[0]}")
                return self.symbol_to_id[clean_symbol][exchanges[0]]
        
        logger.warning(f"Security ID not found for symbol: {symbol} on {exchange}")
        return None
    
    def get_symbol(self, security_id: str, exchange: str = "NSE_EQ") -> Optional[str]:
        """
        Get RapidTrader symbol for a security ID.
        
        Args:
            security_id: DhanHQ security ID (e.g., "2885")
            exchange: Exchange segment (e.g., "NSE_EQ")
            
        Returns:
            RapidTrader symbol or None if not found
        """
        if security_id in self.id_to_symbol:
            if exchange in self.id_to_symbol[security_id]:
                return self.id_to_symbol[security_id][exchange]
            
            # Try with different exchange if not found
            exchanges = list(self.id_to_symbol[security_id].keys())
            if exchanges:
                logger.warning(f"Security ID {security_id} not found for {exchange}, using {exchanges[0]}")
                return self.id_to_symbol[security_id][exchanges[0]]
        
        logger.warning(f"Symbol not found for security ID: {security_id} on {exchange}")
        return None
    
    def _clean_symbol(self, symbol: str) -> str:
        """
        Clean and standardize symbol format.
        
        Args:
            symbol: Input symbol
            
        Returns:
            Cleaned symbol
        """
        # Remove common suffixes
        symbol = symbol.upper().strip()
        
        # Remove -EQ suffix if present
        if symbol.endswith("-EQ"):
            symbol = symbol[:-3]
        
        # Remove .NS or .BO suffix if present
        if symbol.endswith(".NS") or symbol.endswith(".BO"):
            symbol = symbol[:-3]
        
        return symbol
    
    def get_all_symbols(self, exchange: str = "NSE_EQ") -> List[str]:
        """
        Get all available symbols for an exchange.
        
        Args:
            exchange: Exchange segment
            
        Returns:
            List of symbols
        """
        symbols = []
        for symbol, exchanges in self.symbol_to_id.items():
            if exchange in exchanges:
                symbols.append(symbol)
        return sorted(symbols)
    
    def get_mapping_stats(self) -> Dict[str, int]:
        """
        Get statistics about the mappings.
        
        Returns:
            Dictionary with mapping statistics
        """
        stats = {
            'total_symbols': len(self.symbol_to_id),
            'total_security_ids': len(self.id_to_symbol)
        }
        
        # Count by exchange
        exchange_counts = {}
        for symbol, exchanges in self.symbol_to_id.items():
            for exchange in exchanges:
                exchange_counts[exchange] = exchange_counts.get(exchange, 0) + 1
        
        stats['by_exchange'] = exchange_counts
        return stats


# Global symbol mapper instance
_symbol_mapper = None

def get_symbol_mapper() -> SymbolMapper:
    """
    Get the global symbol mapper instance.
    
    Returns:
        SymbolMapper instance
    """
    global _symbol_mapper
    if _symbol_mapper is None:
        _symbol_mapper = SymbolMapper()
    return _symbol_mapper


def symbol_to_security_id(symbol: str, exchange: str = "NSE_EQ") -> Optional[str]:
    """
    Convert RapidTrader symbol to DhanHQ security ID.
    
    Args:
        symbol: RapidTrader symbol
        exchange: Exchange segment
        
    Returns:
        DhanHQ security ID or None
    """
    mapper = get_symbol_mapper()
    return mapper.get_security_id(symbol, exchange)


def security_id_to_symbol(security_id: str, exchange: str = "NSE_EQ") -> Optional[str]:
    """
    Convert DhanHQ security ID to RapidTrader symbol.
    
    Args:
        security_id: DhanHQ security ID
        exchange: Exchange segment
        
    Returns:
        RapidTrader symbol or None
    """
    mapper = get_symbol_mapper()
    return mapper.get_symbol(security_id, exchange)
