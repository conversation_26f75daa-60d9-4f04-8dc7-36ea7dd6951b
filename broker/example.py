"""
Example usage of the DhanBroker.

This file demonstrates how to use the DhanBroker class for trading operations.
The DhanBroker automatically loads credentials from the .env file in the root directory.
"""

from datetime import datetime, timedelta
from broker.dhan_wrapper import <PERSON>han<PERSON><PERSON>r, OrderType, ProductType, OrderSide, Exchange

def main():
    """Example usage of DhanBroker."""

    # Initialize broker - credentials are loaded from .env file
    broker = DhanBroker()

    # Authenticate
    if broker.authenticate():
        print("Authentication successful")
    else:
        print("Authentication failed")
        return

    # Get account balance
    balance = broker.get_balance()
    print(f"Account balance: {balance}")

    # Place a market order
    order_result = broker.place_order(
        symbol="SBIN-EQ",
        exchange=Exchange.NSE,
        quantity=1,
        side=OrderSide.BUY,
        order_type=OrderType.MARKET,
        product_type=ProductType.INTRADAY
    )
    print(f"Order placed: {order_result}")

    # Get order status
    order_id = order_result.get("data", {}).get("order_id", "")
    if order_id:
        order_status = broker.get_order_status(order_id)
        print(f"Order status: {order_status}")

    # Get positions
    positions = broker.get_positions()
    print(f"Positions: {positions}")

    # Get holdings
    holdings = broker.get_holdings()
    print(f"Holdings: {holdings}")

    # Get historical data
    from_date = datetime.now() - timedelta(days=7)
    to_date = datetime.now()
    candles = broker.get_candles(
        symbol="SBIN-EQ",
        exchange=Exchange.NSE,
        interval="1d",
        from_date=from_date,
        to_date=to_date
    )
    print(f"Historical data: {candles}")

    # Get instruments
    instruments = broker.get_instruments(exchange=Exchange.NSE)
    print(f"Instruments: {instruments}")

if __name__ == "__main__":
    main()
