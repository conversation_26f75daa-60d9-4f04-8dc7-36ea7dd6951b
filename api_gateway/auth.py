#!/usr/bin/env python3
"""
Authentication and Security Module for RapidTrader API Gateway

Inspired by OpenAlgo's security model, this module provides:
- API key generation and management
- JWT token authentication
- Rate limiting
- Permission-based access control
- Secure credential storage
"""

import os
import secrets
import hashlib
import sqlite3
import logging
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from fastapi import HTTPException, status, Depends
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from passlib.context import CryptContext
import jwt

logger = logging.getLogger(__name__)

# Security configuration
SECRET_KEY = os.getenv("RAPIDTRADER_SECRET_KEY", secrets.token_urlsafe(32))
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

class AuthManager:
    """Manages authentication and authorization for RapidTrader API Gateway"""
    
    def __init__(self, db_path: str = "api_gateway/auth.db"):
        """Initialize the authentication manager"""
        self.db_path = db_path
        self._init_database()
        
    def _init_database(self):
        """Initialize the authentication database"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # API Keys table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    key_hash TEXT NOT NULL UNIQUE,
                    description TEXT,
                    permissions TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_used TIMESTAMP,
                    usage_count INTEGER DEFAULT 0
                )
            """)
            
            # Users table (for future web UI authentication)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT NOT NULL UNIQUE,
                    email TEXT,
                    password_hash TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """)
            
            # Sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sessions (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    api_key_id TEXT,
                    token_hash TEXT NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address TEXT,
                    user_agent TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (api_key_id) REFERENCES api_keys (id)
                )
            """)
            
            # Rate limiting table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS rate_limits (
                    id TEXT PRIMARY KEY,
                    endpoint TEXT NOT NULL,
                    requests INTEGER DEFAULT 0,
                    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(id, endpoint)
                )
            """)
            
            conn.commit()
            logger.info("Authentication database initialized")
    
    def generate_api_key(self, name: str, description: str = None, 
                        permissions: List[str] = None, 
                        expires_at: datetime = None) -> Tuple[str, str]:
        """Generate a new API key"""
        if permissions is None:
            permissions = ["read", "write"]
            
        # Generate API key
        api_key = f"rt_{secrets.token_urlsafe(32)}"
        key_hash = self._hash_key(api_key)
        key_id = secrets.token_urlsafe(16)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO api_keys (id, name, key_hash, description, permissions, expires_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (key_id, name, key_hash, description, ",".join(permissions), expires_at))
            conn.commit()
            
        logger.info(f"Generated API key: {name} (ID: {key_id})")
        return key_id, api_key
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify an API key and return key information"""
        key_hash = self._hash_key(api_key)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, name, permissions, expires_at, is_active
                FROM api_keys 
                WHERE key_hash = ? AND is_active = TRUE
            """, (key_hash,))
            
            result = cursor.fetchone()
            if not result:
                return None
                
            key_id, name, permissions, expires_at, is_active = result
            
            # Check expiration
            if expires_at:
                expires_dt = datetime.fromisoformat(expires_at)
                if datetime.now() > expires_dt:
                    logger.warning(f"API key expired: {name}")
                    return None
            
            # Update usage statistics
            cursor.execute("""
                UPDATE api_keys 
                SET last_used = CURRENT_TIMESTAMP, usage_count = usage_count + 1
                WHERE id = ?
            """, (key_id,))
            conn.commit()
            
            return {
                "id": key_id,
                "name": name,
                "permissions": permissions.split(",") if permissions else [],
                "expires_at": expires_at,
                "is_active": is_active
            }
    
    def _hash_key(self, key: str) -> str:
        """Hash an API key for secure storage"""
        return hashlib.sha256(key.encode()).hexdigest()
    
    def revoke_api_key(self, key_id: str) -> bool:
        """Revoke an API key"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE api_keys SET is_active = FALSE WHERE id = ?
            """, (key_id,))
            
            if cursor.rowcount > 0:
                conn.commit()
                logger.info(f"Revoked API key: {key_id}")
                return True
            return False
    
    def list_api_keys(self) -> List[Dict[str, Any]]:
        """List all API keys (without the actual keys)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, name, description, permissions, created_at, expires_at, 
                       is_active, last_used, usage_count
                FROM api_keys
                ORDER BY created_at DESC
            """)
            
            keys = []
            for row in cursor.fetchall():
                keys.append({
                    "id": row[0],
                    "name": row[1],
                    "description": row[2],
                    "permissions": row[3].split(",") if row[3] else [],
                    "created_at": row[4],
                    "expires_at": row[5],
                    "is_active": bool(row[6]),
                    "last_used": row[7],
                    "usage_count": row[8]
                })
            
            return keys

# Global auth manager instance
auth_manager = AuthManager()

async def get_current_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Dependency to get current API key from request"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    api_key_info = auth_manager.verify_api_key(credentials.credentials)
    if not api_key_info:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired API key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return api_key_info

def require_permission(permission: str):
    """Decorator to require specific permission"""
    def permission_checker(api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
        if permission not in api_key_info.get("permissions", []):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission '{permission}' required"
            )
        return api_key_info
    return permission_checker

# Rate limiting
class RateLimiter:
    """Simple rate limiter for API endpoints"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.window_size = 60  # seconds
    
    def is_allowed(self, key_id: str, endpoint: str) -> bool:
        """Check if request is allowed under rate limit"""
        with sqlite3.connect(auth_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # Clean old entries
            cutoff = datetime.now() - timedelta(seconds=self.window_size)
            cursor.execute("""
                DELETE FROM rate_limits 
                WHERE window_start < ?
            """, (cutoff,))
            
            # Check current rate
            cursor.execute("""
                SELECT requests FROM rate_limits 
                WHERE id = ? AND endpoint = ?
            """, (key_id, endpoint))
            
            result = cursor.fetchone()
            current_requests = result[0] if result else 0
            
            if current_requests >= self.requests_per_minute:
                return False
            
            # Update counter
            cursor.execute("""
                INSERT OR REPLACE INTO rate_limits (id, endpoint, requests, window_start)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """, (key_id, endpoint, current_requests + 1))
            
            conn.commit()
            return True

# Global rate limiter
rate_limiter = RateLimiter()
