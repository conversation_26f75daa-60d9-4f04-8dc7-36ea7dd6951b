#!/usr/bin/env python3
"""
Unified Broker Interface for RapidTrader API Gateway

Inspired by OpenAlgo's broker abstraction, this module provides:
- Unified API interface for all brokers
- Broker-agnostic order management
- Standardized data formats
- Error handling and validation
- Broker configuration management
"""

import os
import sys
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)

class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"

class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    SL = "SL"
    SL_M = "SL-M"

class ProductType(Enum):
    MIS = "MIS"
    CNC = "CNC"
    NRML = "NRML"

class OrderStatus(Enum):
    PENDING = "PENDING"
    OPEN = "OPEN"
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

class BrokerInterface(ABC):
    """Abstract base class for all broker implementations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.broker_name = config.get("broker_name", "unknown")
        self.dry_run = config.get("dry_run", True)
        self.live_data = config.get("live_data", True)
        
    @abstractmethod
    def authenticate(self) -> Dict[str, Any]:
        """Authenticate with the broker"""
        pass
    
    @abstractmethod
    def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Place an order"""
        pass
    
    @abstractmethod
    def modify_order(self, order_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Modify an existing order"""
        pass
    
    @abstractmethod
    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order"""
        pass
    
    @abstractmethod
    def get_orderbook(self) -> Dict[str, Any]:
        """Get order book"""
        pass
    
    @abstractmethod
    def get_tradebook(self) -> Dict[str, Any]:
        """Get trade book"""
        pass
    
    @abstractmethod
    def get_positions(self) -> Dict[str, Any]:
        """Get positions"""
        pass
    
    @abstractmethod
    def get_holdings(self) -> Dict[str, Any]:
        """Get holdings"""
        pass
    
    @abstractmethod
    def get_funds(self) -> Dict[str, Any]:
        """Get account funds"""
        pass
    
    @abstractmethod
    def get_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """Get market quotes"""
        pass

class FyersBrokerInterface(BrokerInterface):
    """Fyers broker implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self._init_broker()
    
    def _init_broker(self):
        """Initialize Fyers broker"""
        try:
            from broker.fyers_wrapper import FyersBroker
            
            credentials = self.config.get("credentials", {})
            self.broker = FyersBroker(
                client_id=credentials.get("client_id"),
                access_token=credentials.get("access_token"),
                refresh_token=credentials.get("refresh_token"),
                dry_run=self.dry_run,
                live_data=self.live_data
            )
            logger.info("Fyers broker initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Fyers broker: {e}")
            raise
    
    def authenticate(self) -> Dict[str, Any]:
        """Authenticate with Fyers"""
        try:
            profile = self.broker.get_profile()
            if profile and profile.get('s') == 'ok':
                return {
                    "status": "success",
                    "broker": "fyers",
                    "profile": profile.get('data', {})
                }
            else:
                return {
                    "status": "error",
                    "message": "Authentication failed",
                    "details": profile
                }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Place order with Fyers"""
        try:
            # Convert to Fyers format
            fyers_order = self._convert_to_fyers_order(order_data)
            result = self.broker.place_order(**fyers_order)
            
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def modify_order(self, order_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Modify Fyers order"""
        try:
            fyers_order = self._convert_to_fyers_order(order_data)
            fyers_order["id"] = order_id
            result = self.broker.modify_order(**fyers_order)
            
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel Fyers order"""
        try:
            result = self.broker.cancel_order(order_id)
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_orderbook(self) -> Dict[str, Any]:
        """Get Fyers orderbook"""
        try:
            result = self.broker.get_orders()
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_tradebook(self) -> Dict[str, Any]:
        """Get Fyers tradebook"""
        try:
            result = self.broker.get_trades()
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_positions(self) -> Dict[str, Any]:
        """Get Fyers positions"""
        try:
            result = self.broker.get_positions()
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_holdings(self) -> Dict[str, Any]:
        """Get Fyers holdings"""
        try:
            result = self.broker.get_holdings()
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_funds(self) -> Dict[str, Any]:
        """Get Fyers funds"""
        try:
            result = self.broker.get_funds()
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """Get Fyers quotes"""
        try:
            result = self.broker.get_quotes(symbols)
            return self._standardize_response(result)
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    def _convert_to_fyers_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert standard order format to Fyers format"""
        return {
            "symbol": order_data["symbol"],
            "qty": order_data["quantity"],
            "type": order_data.get("order_type", "2"),  # Market order
            "side": "1" if order_data["side"] == "BUY" else "-1",
            "productType": order_data.get("product_type", "INTRADAY"),
            "limitPrice": order_data.get("price", 0),
            "stopPrice": order_data.get("trigger_price", 0),
            "validity": order_data.get("validity", "DAY"),
            "disclosedQty": 0,
            "offlineOrder": False
        }
    
    def _standardize_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Standardize broker response format"""
        if response and response.get('s') == 'ok':
            return {
                "status": "success",
                "data": response.get('data', {}),
                "message": response.get('message', 'Success')
            }
        else:
            return {
                "status": "error",
                "message": response.get('message', 'Unknown error'),
                "code": response.get('code'),
                "data": response.get('data', {})
            }

class DhanBrokerInterface(BrokerInterface):
    """Dhan broker implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self._init_broker()
    
    def _init_broker(self):
        """Initialize Dhan broker"""
        try:
            from broker.dhan_wrapper import DhanBroker
            
            credentials = self.config.get("credentials", {})
            self.broker = DhanBroker(
                client_id=credentials.get("client_id"),
                access_token=credentials.get("access_token"),
                dry_run=self.dry_run,
                live_data=self.live_data
            )
            logger.info("Dhan broker initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Dhan broker: {e}")
            raise
    
    # Implement all abstract methods similar to Fyers
    # (Implementation details would be similar but adapted for Dhan API)
    
    def authenticate(self) -> Dict[str, Any]:
        """Authenticate with Dhan"""
        # Implementation for Dhan authentication
        pass
    
    def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Place order with Dhan"""
        # Implementation for Dhan order placement
        pass
    
    # ... other methods

class BrokerFactory:
    """Factory class to create broker instances"""
    
    _brokers = {
        "fyers": FyersBrokerInterface,
        "dhan": DhanBrokerInterface,
    }
    
    @classmethod
    def create_broker(cls, config: Dict[str, Any]) -> BrokerInterface:
        """Create a broker instance based on configuration"""
        broker_name = config.get("broker_name", "").lower()
        
        if broker_name not in cls._brokers:
            raise ValueError(f"Unsupported broker: {broker_name}")
        
        broker_class = cls._brokers[broker_name]
        return broker_class(config)
    
    @classmethod
    def get_supported_brokers(cls) -> List[str]:
        """Get list of supported brokers"""
        return list(cls._brokers.keys())

# Global broker manager
class BrokerManager:
    """Manages multiple broker instances"""
    
    def __init__(self):
        self.brokers: Dict[str, BrokerInterface] = {}
    
    def add_broker(self, name: str, config: Dict[str, Any]) -> bool:
        """Add a broker configuration"""
        try:
            broker = BrokerFactory.create_broker(config)
            self.brokers[name] = broker
            logger.info(f"Added broker: {name}")
            return True
        except Exception as e:
            logger.error(f"Failed to add broker {name}: {e}")
            return False
    
    def get_broker(self, name: str) -> Optional[BrokerInterface]:
        """Get a broker instance"""
        return self.brokers.get(name)
    
    def remove_broker(self, name: str) -> bool:
        """Remove a broker"""
        if name in self.brokers:
            del self.brokers[name]
            logger.info(f"Removed broker: {name}")
            return True
        return False
    
    def list_brokers(self) -> List[str]:
        """List configured brokers"""
        return list(self.brokers.keys())

# Global broker manager instance
broker_manager = BrokerManager()
