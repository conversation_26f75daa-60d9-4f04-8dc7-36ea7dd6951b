# API Gateway specific requirements
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
docker==6.1.3
websockets==12.0
python-multipart==0.0.6

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.1.0

# Database (sqlite3 is built into Python)

# Shared dependencies
requests==2.31.0
aiofiles==23.2.1

# Broker dependencies
fyers-apiv3==3.1.7
dhanhq==2.0.2

# Utilities
click==8.2.0
rich==14.0.0
