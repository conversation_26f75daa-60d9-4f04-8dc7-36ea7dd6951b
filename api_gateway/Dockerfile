# Alpine-based Dockerfile for API Gateway
FROM python:3.11-alpine

LABEL maintainer="RapidTrader API Gateway <<EMAIL>>"

# Set up environment
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Create api user
RUN addgroup -g 1000 api \
    && adduser -D -u 1000 -G api -s /bin/sh api

# Create directories
RUN mkdir -p /app /app/logs \
    && chown -R api:api /app

WORKDIR /app

# Copy requirements and install dependencies
COPY api_gateway/requirements.txt /app/
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=api:api api_gateway/ /app/
COPY --chown=api:api core/ /app/core/
COPY --chown=api:api broker/ /app/broker/

# Switch to api user
USER api

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose port
EXPOSE 8000

# Run the API Gateway
CMD ["python", "main.py", "--host", "0.0.0.0", "--port", "8000"]
