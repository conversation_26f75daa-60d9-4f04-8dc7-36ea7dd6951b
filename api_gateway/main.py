#!/usr/bin/env python3
"""
RapidTrader Central API Gateway

FastAPI-based central API gateway inspired by OpenAlgo architecture.
Provides unified API access with API key authentication, broker abstraction,
and modular container management.

Features:
- API key-based authentication
- Unified broker API interface
- Container management and orchestration
- Log aggregation from all containers
- PnL aggregation and reporting
- Real-time WebSocket connections
- Health monitoring
- Rate limiting and security
"""

import os
import sys
import json
import asyncio
import logging
import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

import docker
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Depends, Security
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn
from passlib.context import CryptContext
import sqlite3

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Security setup
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Pydantic models
class APIKeyCreate(BaseModel):
    name: str
    description: Optional[str] = None
    permissions: List[str] = ["read", "write"]
    expires_at: Optional[datetime] = None

class APIKeyResponse(BaseModel):
    id: str
    name: str
    key: str
    description: Optional[str]
    permissions: List[str]
    created_at: datetime
    expires_at: Optional[datetime]
    is_active: bool

class BrokerConfig(BaseModel):
    broker_name: str  # fyers, dhan, etc.
    credentials: Dict[str, str]
    dry_run: bool = True
    live_data: bool = True

class OrderRequest(BaseModel):
    symbol: str
    quantity: int
    side: str  # BUY, SELL
    order_type: str  # MARKET, LIMIT, SL, SL-M
    price: Optional[float] = None
    trigger_price: Optional[float] = None
    product_type: str = "MIS"
    validity: str = "DAY"

class ContainerRequest(BaseModel):
    action: str  # start, stop, restart
    container_type: str  # backtest, dryrun, live, etc.
    config: Optional[Dict[str, Any]] = None
    strategy: Optional[str] = None
    symbols: Optional[List[str]] = None

class LogQuery(BaseModel):
    container_id: Optional[str] = None
    level: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = 100

class PnLQuery(BaseModel):
    container_ids: Optional[List[str]] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    strategy: Optional[str] = None

class APIGateway:
    """Central API Gateway for RapidTrader"""

    def __init__(self):
        """Initialize the API Gateway"""
        self.app = FastAPI(
            title="RapidTrader API Gateway",
            description="Unified API Gateway for RapidTrader - Inspired by OpenAlgo",
            version="2.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )

        # Configure CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # Import auth and broker modules
        from api_gateway.auth import auth_manager, get_current_api_key, require_permission, rate_limiter
        from api_gateway.broker_interface import broker_manager

        self.auth_manager = auth_manager
        self.broker_manager = broker_manager
        self.rate_limiter = rate_limiter

        # Docker client
        self.docker_client = docker.from_env()

        # Active WebSocket connections
        self.websocket_connections: List[WebSocket] = []

        # Container registry
        self.containers: Dict[str, Dict[str, Any]] = {}

        # Setup routes
        self._setup_routes()

        logger.info("RapidTrader API Gateway v2.0 initialized")

    def _setup_routes(self):
        """Setup FastAPI routes"""

        @self.app.get("/")
        async def root():
            return {
                "message": "RapidTrader API Gateway v2.0",
                "version": "2.0.0",
                "description": "Unified API Gateway inspired by OpenAlgo",
                "docs": "/docs",
                "supported_brokers": ["fyers", "dhan"]
            }

        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "containers": len(self.containers),
                "websockets": len(self.websocket_connections),
                "brokers": len(self.broker_manager.list_brokers())
            }

        # Authentication endpoints
        @self.app.post("/auth/api-keys", response_model=APIKeyResponse)
        async def create_api_key(request: APIKeyCreate):
            """Create a new API key"""
            try:
                key_id, api_key = self.auth_manager.generate_api_key(
                    name=request.name,
                    description=request.description,
                    permissions=request.permissions,
                    expires_at=request.expires_at
                )

                return APIKeyResponse(
                    id=key_id,
                    name=request.name,
                    key=api_key,
                    description=request.description,
                    permissions=request.permissions,
                    created_at=datetime.now(),
                    expires_at=request.expires_at,
                    is_active=True
                )
            except Exception as e:
                logger.error(f"Error creating API key: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/auth/api-keys")
        async def list_api_keys(api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
            """List all API keys (admin only)"""
            try:
                keys = self.auth_manager.list_api_keys()
                return {"api_keys": keys}
            except Exception as e:
                logger.error(f"Error listing API keys: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.delete("/auth/api-keys/{key_id}")
        async def revoke_api_key(key_id: str, api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
            """Revoke an API key"""
            try:
                success = self.auth_manager.revoke_api_key(key_id)
                if success:
                    return {"message": "API key revoked successfully"}
                else:
                    raise HTTPException(status_code=404, detail="API key not found")
            except Exception as e:
                logger.error(f"Error revoking API key: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Broker management endpoints
        @self.app.post("/brokers")
        async def add_broker(config: BrokerConfig, api_key_info: Dict[str, Any] = Depends(require_permission("write"))):
            """Add a new broker configuration"""
            try:
                broker_name = f"{config.broker_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                success = self.broker_manager.add_broker(broker_name, config.dict())

                if success:
                    return {"message": "Broker added successfully", "broker_name": broker_name}
                else:
                    raise HTTPException(status_code=400, detail="Failed to add broker")
            except Exception as e:
                logger.error(f"Error adding broker: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/brokers")
        async def list_brokers(api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
            """List configured brokers"""
            try:
                brokers = self.broker_manager.list_brokers()
                return {"brokers": brokers}
            except Exception as e:
                logger.error(f"Error listing brokers: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.delete("/brokers/{broker_name}")
        async def remove_broker(broker_name: str, api_key_info: Dict[str, Any] = Depends(require_permission("write"))):
            """Remove a broker configuration"""
            try:
                success = self.broker_manager.remove_broker(broker_name)
                if success:
                    return {"message": "Broker removed successfully"}
                else:
                    raise HTTPException(status_code=404, detail="Broker not found")
            except Exception as e:
                logger.error(f"Error removing broker: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        # Trading endpoints (OpenAlgo-style)
        @self.app.post("/api/v1/placeorder")
        async def place_order(order: OrderRequest, broker_name: str, api_key_info: Dict[str, Any] = Depends(require_permission("write"))):
            """Place an order through specified broker"""
            try:
                # Rate limiting check
                if not self.rate_limiter.is_allowed(api_key_info["id"], "placeorder"):
                    raise HTTPException(status_code=429, detail="Rate limit exceeded")

                broker = self.broker_manager.get_broker(broker_name)
                if not broker:
                    raise HTTPException(status_code=404, detail="Broker not found")

                result = broker.place_order(order.dict())
                return result
            except Exception as e:
                logger.error(f"Error placing order: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/v1/orderbook")
        async def get_orderbook(broker_name: str, api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
            """Get order book from specified broker"""
            try:
                broker = self.broker_manager.get_broker(broker_name)
                if not broker:
                    raise HTTPException(status_code=404, detail="Broker not found")

                result = broker.get_orderbook()
                return result
            except Exception as e:
                logger.error(f"Error getting orderbook: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/v1/positions")
        async def get_positions(broker_name: str, api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
            """Get positions from specified broker"""
            try:
                broker = self.broker_manager.get_broker(broker_name)
                if not broker:
                    raise HTTPException(status_code=404, detail="Broker not found")

                result = broker.get_positions()
                return result
            except Exception as e:
                logger.error(f"Error getting positions: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/containers")
        async def list_containers(api_key_info: Dict[str, Any] = Depends(get_current_api_key)):
            """List all managed containers"""
            try:
                containers = self.docker_client.containers.list(
                    filters={"label": "rapidtrader.managed=true"}
                )

                result = []
                for container in containers:
                    result.append({
                        "id": container.id[:12],
                        "name": container.name,
                        "status": container.status,
                        "image": container.image.tags[0] if container.image.tags else "unknown",
                        "created": container.attrs["Created"],
                        "labels": container.labels
                    })

                return {"containers": result}

            except Exception as e:
                logger.error(f"Error listing containers: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/containers/manage")
        async def manage_container(request: ContainerRequest):
            """Manage container lifecycle"""
            try:
                if request.action == "start":
                    return await self._start_container(request)
                elif request.action == "stop":
                    return await self._stop_container(request)
                elif request.action == "restart":
                    return await self._restart_container(request)
                else:
                    raise HTTPException(status_code=400, detail="Invalid action")

            except Exception as e:
                logger.error(f"Error managing container: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/logs/aggregated")
        async def get_aggregated_logs(query: LogQuery = Depends()):
            """Get aggregated logs from all containers"""
            try:
                logs = await self._aggregate_logs(query)
                return {"logs": logs}

            except Exception as e:
                logger.error(f"Error getting logs: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/pnl/aggregated")
        async def get_aggregated_pnl(query: PnLQuery = Depends()):
            """Get aggregated P&L from all containers"""
            try:
                pnl_data = await self._aggregate_pnl(query)
                return {"pnl": pnl_data}

            except Exception as e:
                logger.error(f"Error getting P&L: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await websocket.accept()
            self.websocket_connections.append(websocket)

            try:
                while True:
                    # Send periodic updates
                    await asyncio.sleep(5)

                    # Get container status
                    status = await self._get_system_status()
                    await websocket.send_json(status)

            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
                logger.info("WebSocket client disconnected")
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                if websocket in self.websocket_connections:
                    self.websocket_connections.remove(websocket)

    async def _start_container(self, request: ContainerRequest) -> Dict[str, Any]:
        """Start a new container"""
        container_name = f"rapidtrader-{request.container_type}-{datetime.now().strftime('%Y%m%d-%H%M%S')}"

        # Prepare environment variables
        env_vars = {
            "RAPIDTRADER_MODE": request.container_type,
            "RAPIDTRADER_STRATEGY": request.strategy or "DefaultStrategy",
        }

        if request.symbols:
            env_vars["RAPIDTRADER_SYMBOLS"] = ",".join(request.symbols)

        # Prepare volumes
        volumes = {
            str(project_root / "userdata"): {"bind": "/rapidtrader/userdata", "mode": "rw"},
            str(project_root / "logs"): {"bind": "/rapidtrader/logs", "mode": "rw"}
        }

        # Start container
        container = self.docker_client.containers.run(
            image="rapidtrader:alpine",
            name=container_name,
            environment=env_vars,
            volumes=volumes,
            labels={
                "rapidtrader.managed": "true",
                "rapidtrader.type": request.container_type,
                "rapidtrader.strategy": request.strategy or "DefaultStrategy"
            },
            detach=True,
            remove=False  # Keep for log collection
        )

        # Register container
        self.containers[container.id] = {
            "name": container_name,
            "type": request.container_type,
            "strategy": request.strategy,
            "symbols": request.symbols,
            "started_at": datetime.now().isoformat(),
            "container": container
        }

        logger.info(f"Started container: {container_name}")

        return {
            "container_id": container.id[:12],
            "container_name": container_name,
            "status": "started"
        }

    async def _stop_container(self, request: ContainerRequest) -> Dict[str, Any]:
        """Stop a container"""
        # Implementation for stopping containers
        pass

    async def _restart_container(self, request: ContainerRequest) -> Dict[str, Any]:
        """Restart a container"""
        # Implementation for restarting containers
        pass

    async def _aggregate_logs(self, query: LogQuery) -> List[Dict[str, Any]]:
        """Aggregate logs from all containers"""
        logs = []

        try:
            containers = self.docker_client.containers.list(
                filters={"label": "rapidtrader.managed=true"}
            )

            for container in containers:
                try:
                    # Get logs from container
                    container_logs = container.logs(
                        since=query.start_time or datetime.now() - timedelta(hours=1),
                        until=query.end_time or datetime.now(),
                        tail=query.limit
                    ).decode('utf-8')

                    # Parse and format logs
                    for line in container_logs.split('\n'):
                        if line.strip():
                            logs.append({
                                "container_id": container.id[:12],
                                "container_name": container.name,
                                "timestamp": datetime.now().isoformat(),
                                "message": line.strip(),
                                "level": self._extract_log_level(line)
                            })

                except Exception as e:
                    logger.error(f"Error getting logs from {container.name}: {e}")

            # Sort by timestamp and limit
            logs.sort(key=lambda x: x["timestamp"], reverse=True)
            return logs[:query.limit]

        except Exception as e:
            logger.error(f"Error aggregating logs: {e}")
            return []

    async def _aggregate_pnl(self, query: PnLQuery) -> Dict[str, Any]:
        """Aggregate P&L data from all containers"""
        pnl_data = {
            "total_pnl": 0.0,
            "containers": [],
            "summary": {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0
            }
        }

        try:
            # Read P&L data from result files
            results_dir = project_root / "userdata" / "results"

            if results_dir.exists():
                for result_file in results_dir.glob("*.json"):
                    try:
                        with open(result_file, 'r') as f:
                            data = json.load(f)

                        # Extract P&L information
                        if "results" in data:
                            for strategy, results in data["results"].items():
                                container_pnl = {
                                    "strategy": strategy,
                                    "file": result_file.name,
                                    "total_pnl": results.get("total_profit_loss", 0),
                                    "total_trades": results.get("total_trades", 0),
                                    "win_rate": results.get("win_rate", 0)
                                }

                                pnl_data["containers"].append(container_pnl)
                                pnl_data["total_pnl"] += container_pnl["total_pnl"]
                                pnl_data["summary"]["total_trades"] += container_pnl["total_trades"]

                    except Exception as e:
                        logger.error(f"Error reading result file {result_file}: {e}")

            # Calculate summary statistics
            if pnl_data["summary"]["total_trades"] > 0:
                winning_trades = sum(1 for c in pnl_data["containers"] if c["total_pnl"] > 0)
                pnl_data["summary"]["winning_trades"] = winning_trades
                pnl_data["summary"]["losing_trades"] = len(pnl_data["containers"]) - winning_trades
                pnl_data["summary"]["win_rate"] = (winning_trades / len(pnl_data["containers"])) * 100

            return pnl_data

        except Exception as e:
            logger.error(f"Error aggregating P&L: {e}")
            return pnl_data

    async def _get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        try:
            containers = self.docker_client.containers.list(
                filters={"label": "rapidtrader.managed=true"}
            )

            status = {
                "timestamp": datetime.now().isoformat(),
                "containers": {
                    "total": len(containers),
                    "running": len([c for c in containers if c.status == "running"]),
                    "stopped": len([c for c in containers if c.status == "exited"])
                },
                "websockets": len(self.websocket_connections)
            }

            return status

        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {"error": str(e)}

    def _extract_log_level(self, log_line: str) -> str:
        """Extract log level from log line"""
        if "ERROR" in log_line.upper():
            return "ERROR"
        elif "WARNING" in log_line.upper() or "WARN" in log_line.upper():
            return "WARNING"
        elif "INFO" in log_line.upper():
            return "INFO"
        elif "DEBUG" in log_line.upper():
            return "DEBUG"
        else:
            return "INFO"

    def run(self, host: str = "0.0.0.0", port: int = 8000):
        """Run the API Gateway"""
        uvicorn.run(self.app, host=host, port=port)


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description='RapidTrader API Gateway')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8000, help='Port to listen on')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')

    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    gateway = APIGateway()
    gateway.run(host=args.host, port=args.port)


if __name__ == "__main__":
    main()
