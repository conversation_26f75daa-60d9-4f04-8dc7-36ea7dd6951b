#!/usr/bin/env python3
"""
RapidTrader Unified API Gateway

Inspired by OpenAlgo and FreqTrade architecture but designed specifically for RapidTrader.
Uses RapidTrader's existing unified symbol system and broker abstractions.

Key Features:
- API key authentication (inspired by OpenAlgo)
- Unified symbol system (RapidTrader standard symbols)
- Broker abstraction layer
- Container management (inspired by FreqTrade)
- Simplified Docker architecture
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, Depends, Security
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import RapidTrader components
from broker.symbol_mapper import get_symbol_mapper, symbol_to_security_id, security_id_to_symbol
from broker.fyers_symbol_mapper import get_fyers_symbol_mapper, to_fyers_symbol, from_fyers_symbol

logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

# Pydantic Models
class StandardOrderRequest(BaseModel):
    """Standard order request using RapidTrader symbols"""
    symbol: str  # RapidTrader standard symbol (e.g., "SBIN", "RELIANCE")
    exchange: str = "NSE_EQ"  # Standard exchange format
    quantity: int
    side: str  # BUY, SELL
    order_type: str = "MARKET"  # MARKET, LIMIT, SL, SL-M
    price: Optional[float] = None
    trigger_price: Optional[float] = None
    product_type: str = "MIS"
    validity: str = "DAY"

class BrokerConfig(BaseModel):
    """Broker configuration"""
    broker_name: str  # fyers, dhan
    credentials: Dict[str, str]
    dry_run: bool = True
    live_data: bool = True

class SymbolInfo(BaseModel):
    """Symbol information response"""
    rapidtrader_symbol: str
    exchange: str
    fyers_symbol: Optional[str] = None
    dhan_security_id: Optional[str] = None
    is_available: bool = True

class APIKeyInfo(BaseModel):
    """API key information"""
    name: str
    permissions: List[str] = ["read", "write"]
    expires_at: Optional[datetime] = None

class RapidTraderUnifiedAPI:
    """RapidTrader Unified API Gateway"""
    
    def __init__(self):
        self.app = FastAPI(
            title="RapidTrader Unified API",
            description="Unified API Gateway for RapidTrader - Inspired by OpenAlgo & FreqTrade",
            version="2.0.0"
        )
        
        # Configure CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Initialize symbol mappers
        self.symbol_mapper = get_symbol_mapper()
        self.fyers_mapper = get_fyers_symbol_mapper()
        
        # Simple API key storage (in production, use database)
        self.api_keys = {
            "rt_demo_key_12345": {
                "name": "demo",
                "permissions": ["read", "write"],
                "active": True
            }
        }
        
        # Broker instances
        self.brokers = {}
        
        self._setup_routes()
        
    def _verify_api_key(self, credentials: HTTPAuthorizationCredentials = Security(security)) -> Dict[str, Any]:
        """Verify API key"""
        if not credentials:
            raise HTTPException(status_code=401, detail="API key required")
        
        api_key = credentials.credentials
        if api_key not in self.api_keys or not self.api_keys[api_key]["active"]:
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        return self.api_keys[api_key]
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/")
        async def root():
            return {
                "message": "RapidTrader Unified API Gateway",
                "version": "2.0.0",
                "inspired_by": ["OpenAlgo", "FreqTrade"],
                "features": [
                    "Unified Symbol System",
                    "Multi-Broker Support",
                    "API Key Authentication",
                    "Container Management"
                ]
            }
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "symbol_mappers": {
                    "dhan_symbols": len(self.symbol_mapper.symbol_to_id) if self.symbol_mapper else 0,
                    "fyers_symbols": len(self.fyers_mapper.symbol_mapping.get("NSE_EQ", {})) if self.fyers_mapper else 0
                },
                "brokers": len(self.brokers)
            }
        
        # Symbol Management Endpoints
        @self.app.get("/symbols/search/{symbol}")
        async def search_symbol(symbol: str, exchange: str = "NSE_EQ", 
                              api_key: Dict = Depends(self._verify_api_key)):
            """Search for symbol information across all brokers"""
            try:
                # Get Dhan security ID
                dhan_id = symbol_to_security_id(symbol.upper(), exchange)
                
                # Get Fyers symbol
                fyers_symbol = to_fyers_symbol(symbol.upper(), exchange)
                
                return SymbolInfo(
                    rapidtrader_symbol=symbol.upper(),
                    exchange=exchange,
                    fyers_symbol=fyers_symbol,
                    dhan_security_id=dhan_id,
                    is_available=(dhan_id is not None or fyers_symbol is not None)
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/symbols/list")
        async def list_symbols(exchange: str = "NSE_EQ", limit: int = 100,
                             api_key: Dict = Depends(self._verify_api_key)):
            """List available symbols"""
            try:
                symbols = []
                
                # Get symbols from Dhan mapper
                if self.symbol_mapper:
                    dhan_symbols = self.symbol_mapper.get_all_symbols(exchange)
                    symbols.extend(dhan_symbols[:limit])
                
                return {
                    "exchange": exchange,
                    "symbols": symbols[:limit],
                    "total_available": len(symbols)
                }
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/symbols/add")
        async def add_symbol_mapping(symbol: str, dhan_security_id: Optional[str] = None,
                                   fyers_symbol: Optional[str] = None, exchange: str = "NSE_EQ",
                                   api_key: Dict = Depends(self._verify_api_key)):
            """Add new symbol mapping"""
            try:
                success = False
                
                # Add to Dhan mapper
                if dhan_security_id and self.symbol_mapper:
                    success = self.symbol_mapper.add_mapping(symbol.upper(), dhan_security_id, exchange)
                    if success:
                        self.symbol_mapper.save_mappings()
                
                # Add to Fyers mapper
                if fyers_symbol and self.fyers_mapper:
                    fyers_success = self.fyers_mapper.add_symbol_mapping(symbol.upper(), fyers_symbol, exchange)
                    if fyers_success:
                        self.fyers_mapper.save_mappings()
                        success = True
                
                if success:
                    return {"message": "Symbol mapping added successfully", "symbol": symbol.upper()}
                else:
                    raise HTTPException(status_code=400, detail="Failed to add symbol mapping")
                    
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        # Broker Management
        @self.app.post("/brokers/add")
        async def add_broker(config: BrokerConfig, api_key: Dict = Depends(self._verify_api_key)):
            """Add broker configuration"""
            try:
                broker_name = config.broker_name.lower()
                
                if broker_name == "fyers":
                    from broker.fyers_wrapper import FyersBroker
                    broker = FyersBroker(
                        client_id=config.credentials.get("client_id"),
                        access_token=config.credentials.get("access_token"),
                        refresh_token=config.credentials.get("refresh_token"),
                        dry_run=config.dry_run,
                        live_data=config.live_data
                    )
                elif broker_name == "dhan":
                    from broker.dhan_wrapper import DhanBroker
                    broker = DhanBroker(
                        client_id=config.credentials.get("client_id"),
                        access_token=config.credentials.get("access_token"),
                        dry_run=config.dry_run,
                        live_data=config.live_data
                    )
                else:
                    raise HTTPException(status_code=400, detail=f"Unsupported broker: {broker_name}")
                
                # Store broker instance
                broker_id = f"{broker_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                self.brokers[broker_id] = broker
                
                return {
                    "message": "Broker added successfully",
                    "broker_id": broker_id,
                    "broker_type": broker_name
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/brokers/list")
        async def list_brokers(api_key: Dict = Depends(self._verify_api_key)):
            """List configured brokers"""
            return {
                "brokers": [
                    {
                        "id": broker_id,
                        "type": broker_id.split("_")[0],
                        "dry_run": broker.is_dry_run() if hasattr(broker, 'is_dry_run') else True
                    }
                    for broker_id, broker in self.brokers.items()
                ]
            }
        
        # Trading Endpoints (OpenAlgo-inspired but using RapidTrader symbols)
        @self.app.post("/api/v1/placeorder")
        async def place_order(order: StandardOrderRequest, broker_id: str,
                            api_key: Dict = Depends(self._verify_api_key)):
            """Place order using RapidTrader standard symbols"""
            try:
                if broker_id not in self.brokers:
                    raise HTTPException(status_code=404, detail="Broker not found")
                
                broker = self.brokers[broker_id]
                broker_type = broker_id.split("_")[0]
                
                # Convert RapidTrader symbol to broker-specific format
                if broker_type == "fyers":
                    broker_symbol = to_fyers_symbol(order.symbol, order.exchange)
                    if not broker_symbol:
                        raise HTTPException(status_code=400, detail=f"Symbol {order.symbol} not found for Fyers")
                elif broker_type == "dhan":
                    broker_symbol = symbol_to_security_id(order.symbol, order.exchange)
                    if not broker_symbol:
                        raise HTTPException(status_code=400, detail=f"Symbol {order.symbol} not found for Dhan")
                else:
                    raise HTTPException(status_code=400, detail="Unsupported broker type")
                
                # Place order using broker-specific symbol
                if broker_type == "fyers":
                    result = broker.place_order(
                        symbol=broker_symbol,
                        qty=order.quantity,
                        type="2" if order.order_type == "MARKET" else "1",
                        side="1" if order.side == "BUY" else "-1",
                        productType=order.product_type,
                        limitPrice=order.price or 0,
                        stopPrice=order.trigger_price or 0,
                        validity=order.validity
                    )
                elif broker_type == "dhan":
                    result = broker.place_order(
                        security_id=broker_symbol,
                        exchange_segment=order.exchange,
                        transaction_type=order.side,
                        quantity=order.quantity,
                        order_type=order.order_type,
                        product_type=order.product_type,
                        price=order.price,
                        trigger_price=order.trigger_price,
                        validity=order.validity
                    )
                
                return {
                    "status": "success",
                    "rapidtrader_symbol": order.symbol,
                    "broker_symbol": broker_symbol,
                    "broker_response": result
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/positions")
        async def get_positions(broker_id: str, api_key: Dict = Depends(self._verify_api_key)):
            """Get positions from broker"""
            try:
                if broker_id not in self.brokers:
                    raise HTTPException(status_code=404, detail="Broker not found")
                
                broker = self.brokers[broker_id]
                positions = broker.get_positions()
                
                return {
                    "status": "success",
                    "broker_id": broker_id,
                    "positions": positions
                }
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

# Create global app instance
api_gateway = RapidTraderUnifiedAPI()
app = api_gateway.app

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
