# RapidTrader Log Rotation Configuration
# Prevents disk bloat from container logs

# Global settings
daily
rotate 7
compress
delaycompress
missingok
notifempty
create 644 rapidtrader rapidtrader

# RapidTrader application logs
/rapidtrader/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 rapidtrader rapidtrader
    postrotate
        # Send signal to application to reopen log files
        pkill -USR1 -f "rapidtrader" || true
    endscript
}

# Container-specific logs
/rapidtrader/logs/containers/*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    create 644 rapidtrader rapidtrader
    maxsize 100M
    postrotate
        # Clean up old container logs
        find /rapidtrader/logs/containers -name "*.log.*.gz" -mtime +14 -delete || true
    endscript
}

# Trading logs (keep longer for analysis)
/rapidtrader/logs/trading/*.log {
    daily
    rotate 90
    compress
    delaycompress
    missingok
    notifempty
    create 644 rapidtrader rapidtrader
    maxsize 500M
}

# Error logs (keep even longer)
/rapidtrader/logs/errors/*.log {
    daily
    rotate 180
    compress
    delaycompress
    missingok
    notifempty
    create 644 rapidtrader rapidtrader
    maxsize 1G
}

# Backtest result logs
/rapidtrader/logs/backtests/*.log {
    weekly
    rotate 52
    compress
    delaycompress
    missingok
    notifempty
    create 644 rapidtrader rapidtrader
    maxsize 2G
}
