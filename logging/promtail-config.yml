# Promtail configuration for RapidTrader log aggregation
# Collects logs from all containers and sends to Loki

server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # RapidTrader application logs
  - job_name: rapidtrader-app
    static_configs:
      - targets:
          - localhost
        labels:
          job: rapidtrader-app
          __path__: /rapidtrader/logs/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<logger>\S+) - (?P<level>\S+) - (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'
      - labels:
          level:
          logger:

  # Container-specific logs
  - job_name: rapidtrader-containers
    static_configs:
      - targets:
          - localhost
        labels:
          job: rapidtrader-containers
          __path__: /rapidtrader/logs/containers/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<container>\S+) - (?P<level>\S+) - (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'
      - labels:
          level:
          container:

  # Trading logs
  - job_name: rapidtrader-trading
    static_configs:
      - targets:
          - localhost
        labels:
          job: rapidtrader-trading
          __path__: /rapidtrader/logs/trading/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<strategy>\S+) - (?P<level>\S+) - (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'
      - labels:
          level:
          strategy:

  # Error logs
  - job_name: rapidtrader-errors
    static_configs:
      - targets:
          - localhost
        labels:
          job: rapidtrader-errors
          __path__: /rapidtrader/logs/errors/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<component>\S+) - ERROR - (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'
      - labels:
          component:
          level: error

  # Docker container logs
  - job_name: docker-containers
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
        filters:
          - name: label
            values: ["rapidtrader.managed=true"]
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        target_label: 'container_name'
      - source_labels: ['__meta_docker_container_label_rapidtrader_type']
        target_label: 'rapidtrader_type'
      - source_labels: ['__meta_docker_container_label_rapidtrader_strategy']
        target_label: 'rapidtrader_strategy'
    pipeline_stages:
      - cri: {}
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) (?P<stream>stdout|stderr) (?P<flags>\S+) (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      - labels:
          stream:

  # Backtest logs
  - job_name: rapidtrader-backtests
    static_configs:
      - targets:
          - localhost
        labels:
          job: rapidtrader-backtests
          __path__: /rapidtrader/logs/backtests/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (?P<backtest_id>\S+) - (?P<level>\S+) - (?P<message>.*)$'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05,000'
      - labels:
          level:
          backtest_id:
