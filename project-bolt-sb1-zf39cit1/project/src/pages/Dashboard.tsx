import { useEffect, useState } from 'react';
import {
  Container,
  Grid,
  Typography,
  Paper,
  Box,
  Button,
  List,
  Divider,
  Tab,
  Tabs,
  CircularProgress,
  useTheme,
  alpha,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  ShoppingCart as ShoppingCartIcon,
  AccountBalance as AccountBalanceIcon,
  ShowChart as ShowChartIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useMarketDataStore } from '../store/marketDataStore';
import { useOrdersStore } from '../store/ordersStore';
import { usePositionsStore } from '../store/positionsStore';
import { useMarketData } from '../hooks/useMarketData';
import SummaryCard from '../components/dashboard/SummaryCard';
import MarketWatchItem from '../components/dashboard/MarketWatchItem';
import OrderForm from '../components/orders/OrderForm';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

// Sample account data - would normally come from API
const accountData = {
  balance: 100000,
  margin: 75000,
  usedMargin: 25000,
  availableMargin: 50000,
};

// Sample chart data - would normally come from API
const generateChartData = () => {
  const data = [];
  const now = new Date();
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(now.getDate() - i);
    
    data.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      value: Math.round(90000 + Math.random() * 20000),
    });
  }
  
  return data;
};

const Dashboard = () => {
  const theme = useTheme();
  const { marketData, subscribeSymbol } = useMarketData();
  const { orders } = useOrdersStore();
  const { positions, updatePositionLTP } = usePositionsStore();
  const { instruments } = useMarketDataStore();
  
  const [tabValue, setTabValue] = useState(0);
  const [chartData] = useState(generateChartData);
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [orderFormData, setOrderFormData] = useState({
    symbol: '',
    side: 'BUY' as const,
  });
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // Handle buy click from market watch
  const handleBuyClick = (symbol: string) => {
    setOrderFormData({
      symbol,
      side: 'BUY',
    });
    setShowOrderForm(true);
  };
  
  // Handle sell click from market watch
  const handleSellClick = (symbol: string) => {
    setOrderFormData({
      symbol,
      side: 'SELL',
    });
    setShowOrderForm(true);
  };
  
  // Update position P&L when market data changes
  useEffect(() => {
    Object.values(marketData).forEach((data) => {
      updatePositionLTP(data.symbol, data.ltp);
    });
  }, [marketData, updatePositionLTP]);
  
  // Subscribe to symbols for positions and commonly watched instruments
  useEffect(() => {
    // Subscribe to symbols for positions
    positions.forEach((position) => {
      subscribeSymbol(position.symbol);
    });
    
    // Subscribe to some common instruments (demo)
    const commonSymbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META'];
    commonSymbols.forEach((symbol) => {
      subscribeSymbol(symbol);
    });
  }, [positions, subscribeSymbol]);
  
  // Calculate totals
  const totalPositions = positions.length;
  const openOrders = orders.filter((order) => order.status === 'OPEN').length;
  
  // Calculate total P&L
  const totalPnL = positions.reduce((sum, position) => sum + position.pnl, 0);
  const totalPnLPercent = totalPnL / accountData.balance * 100;
  
  return (
    <Container maxWidth="xl">
      <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
        Dashboard
      </Typography>
      
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard
            title="Account Balance"
            value={`$${accountData.balance.toLocaleString()}`}
            subtitle="Available: $50,000"
            icon={<AccountBalanceIcon />}
            trend="up"
            trendValue="+5.2% today"
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard
            title="Total P&L"
            value={`$${totalPnL.toLocaleString()}`}
            subtitle="Across all positions"
            icon={<TrendingUpIcon />}
            trend={totalPnL >= 0 ? 'up' : 'down'}
            trendValue={`${totalPnLPercent.toFixed(2)}%`}
            color={totalPnL >= 0 ? theme.palette.success.main : theme.palette.error.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard
            title="Active Positions"
            value={totalPositions}
            subtitle="Total positions"
            icon={<ShowChartIcon />}
            color={theme.palette.info.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <SummaryCard
            title="Open Orders"
            value={openOrders}
            subtitle="Awaiting execution"
            icon={<ShoppingCartIcon />}
            color={theme.palette.warning.main}
          />
        </Grid>
      </Grid>
      
      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Market Watch and Charts */}
        <Grid item xs={12} md={8}>
          <Paper elevation={1} sx={{ p: 0, height: '100%' }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange}
                aria-label="dashboard tabs"
                sx={{ px: 2 }}
              >
                <Tab label="Portfolio Value" />
                <Tab label="Positions P&L" />
              </Tabs>
            </Box>
            
            <Box sx={{ p: 3 }}>
              {tabValue === 0 && (
                <>
                  <Typography variant="h6\" gutterBottom>
                    Portfolio Value
                  </Typography>
                  <Box sx={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line
                          type="monotone"
                          dataKey="value"
                          stroke={theme.palette.primary.main}
                          strokeWidth={2}
                          dot={{ r: 1 }}
                          activeDot={{ r: 5 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </>
              )}
              
              {tabValue === 1 && (
                <>
                  <Typography variant="h6" gutterBottom>
                    P&L by Position
                  </Typography>
                  <Box sx={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      Position P&L chart will be displayed here
                    </Typography>
                  </Box>
                </>
              )}
            </Box>
          </Paper>
        </Grid>
        
        {/* Market Watch */}
        <Grid item xs={12} md={4}>
          <Paper elevation={1} sx={{ p: 0, height: '100%' }}>
            <Box sx={{ 
              p: 2, 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              borderBottom: 1,
              borderColor: 'divider',
            }}>
              <Typography variant="h6">Market Watch</Typography>
              <Button 
                startIcon={<AddIcon />} 
                size="small"
                onClick={() => setShowOrderForm(true)}
              >
                New Order
              </Button>
            </Box>
            
            <List sx={{ maxHeight: 400, overflow: 'auto' }}>
              {instruments.length > 0 ? (
                instruments.slice(0, 6).map((instrument) => (
                  <MarketWatchItem
                    key={instrument.symbol}
                    symbol={instrument.symbol}
                    name={instrument.fullName}
                    onBuyClick={handleBuyClick}
                    onSellClick={handleSellClick}
                  />
                ))
              ) : (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress size={24} />
                </Box>
              )}
            </List>
          </Paper>
        </Grid>
        
        {/* Recent Orders */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Recent Orders
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {orders.length > 0 ? (
              orders.slice(0, 5).map((order) => (
                <Box 
                  key={order.id} 
                  sx={{ 
                    p: 1.5, 
                    mb: 1, 
                    borderRadius: 1,
                    bgcolor: alpha(
                      order.side === 'BUY' 
                        ? theme.palette.success.main 
                        : theme.palette.error.main, 
                      0.1
                    )
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2">
                      {order.symbol} • {order.type}
                    </Typography>
                    <Typography 
                      variant="subtitle2" 
                      color={order.side === 'BUY' ? 'success.main' : 'error.main'}
                    >
                      {order.side}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                    <Typography variant="body2" color="text.secondary">
                      {order.quantity} × {order.price?.toFixed(2) || 'MARKET'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(order.createdAt).toLocaleTimeString()}
                    </Typography>
                  </Box>
                </Box>
              ))
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                No recent orders
              </Typography>
            )}
            
            <Button 
              variant="text" 
              sx={{ mt: 1 }}
              href="/orders"
              fullWidth
            >
              View All Orders
            </Button>
          </Paper>
        </Grid>
        
        {/* Active Positions */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              Active Positions
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {positions.length > 0 ? (
              positions.slice(0, 5).map((position) => (
                <Box 
                  key={position.id} 
                  sx={{ 
                    p: 1.5, 
                    mb: 1, 
                    borderRadius: 1,
                    bgcolor: alpha(
                      position.side === 'LONG' 
                        ? theme.palette.success.main 
                        : theme.palette.error.main, 
                      0.1
                    )
                  }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2">
                      {position.symbol} • {position.type}
                    </Typography>
                    <Typography 
                      variant="subtitle2" 
                      color={position.side === 'LONG' ? 'success.main' : 'error.main'}
                    >
                      {position.side}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                    <Typography variant="body2" color="text.secondary">
                      {position.quantity} @ {position.avgPrice.toFixed(2)}
                    </Typography>
                    <Typography 
                      variant="body2" 
                      color={position.pnl >= 0 ? 'success.main' : 'error.main'}
                    >
                      {position.pnl.toFixed(2)} ({position.pnlPercent.toFixed(2)}%)
                    </Typography>
                  </Box>
                </Box>
              ))
            ) : (
              <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                No active positions
              </Typography>
            )}
            
            <Button 
              variant="text" 
              sx={{ mt: 1 }}
              href="/positions"
              fullWidth
            >
              View All Positions
            </Button>
          </Paper>
        </Grid>
      </Grid>
      
      {/* Order Form Dialog */}
      <OrderForm
        open={showOrderForm}
        onClose={() => setShowOrderForm(false)}
        initialSymbol={orderFormData.symbol}
        initialSide={orderFormData.side}
      />
    </Container>
  );
};

export default Dashboard;