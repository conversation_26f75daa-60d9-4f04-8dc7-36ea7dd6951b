import { useEffect, useState } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Button,
  CircularProgress,
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { useOrdersStore } from '../store/ordersStore';
import OrdersTable from '../components/orders/OrdersTable';
import OrderForm from '../components/orders/OrderForm';
import { getOrders } from '../services/ordersService';

const Orders = () => {
  const { setOrders, setLoading, loading, error } = useOrdersStore();
  const [showOrderForm, setShowOrderForm] = useState(false);
  
  // Fetch orders on component mount
  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      try {
        const response = await getOrders();
        setOrders(response.orders);
      } catch (error) {
        console.error('Failed to fetch orders:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchOrders();
  }, [setOrders, setLoading]);
  
  return (
    <Container maxWidth="xl">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={600}>
          Orders
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => setShowOrderForm(true)}
        >
          New Order
        </Button>
      </Box>
      
      <Paper elevation={1} sx={{ p: 3 }}>
        {loading && !error ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <OrdersTable />
        )}
      </Paper>
      
      {/* Order Form Dialog */}
      <OrderForm
        open={showOrderForm}
        onClose={() => setShowOrderForm(false)}
      />
    </Container>
  );
};

export default Orders;