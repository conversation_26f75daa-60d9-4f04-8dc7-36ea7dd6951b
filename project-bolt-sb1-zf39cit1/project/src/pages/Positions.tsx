import { useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Button,
} from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';
import { usePositionsStore } from '../store/positionsStore';
import PositionsTable from '../components/positions/PositionsTable';
import { getPositions } from '../services/positionsService';

const Positions = () => {
  const { setPositions, setLoading, loading } = usePositionsStore();
  
  // Fetch positions on component mount
  useEffect(() => {
    const fetchPositions = async () => {
      setLoading(true);
      try {
        const positions = await getPositions();
        setPositions(positions);
      } catch (error) {
        console.error('Failed to fetch positions:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPositions();
  }, [setPositions, setLoading]);
  
  // Refresh positions
  const handleRefresh = () => {
    setLoading(true);
  };
  
  return (
    <Container maxWidth="xl">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight={600}>
          Positions
        </Typography>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>
      
      <Paper elevation={1} sx={{ p: 3 }}>
        <PositionsTable />
      </Paper>
    </Container>
  );
};

export default Positions;