import { useState, FormEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Link,
  Paper,
  Divider,
  InputAdornment,
  IconButton,
  Alert,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Bolt as BoltIcon,
  Google as GoogleIcon,
  Github as GithubIcon,
} from '@mui/icons-material';
import { useAuthStore } from '../store/authStore';
import { signInWithEmail, signUpWithEmail, signInWithGoogle, signInWithGithub } from '../services/firebaseService';

const Login = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { setUser } = useAuthStore();
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  
  // Form fields
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  
  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: '',
    name: '',
  });
  
  // Toggle between login and signup
  const toggleAuthMode = () => {
    setIsLogin(!isLogin);
    setError(null);
  };
  
  // Validate form
  const validateForm = (): boolean => {
    const errors = {
      email: '',
      password: '',
      name: '',
    };
    let isValid = true;
    
    // Email validation
    if (!email) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Email is invalid';
      isValid = false;
    }
    
    // Password validation
    if (!password) {
      errors.password = 'Password is required';
      isValid = false;
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters';
      isValid = false;
    }
    
    // Name validation (only for signup)
    if (!isLogin && !name) {
      errors.name = 'Name is required';
      isValid = false;
    }
    
    setFormErrors(errors);
    return isValid;
  };
  
  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      let user;
      
      if (isLogin) {
        user = await signInWithEmail(email, password);
      } else {
        user = await signUpWithEmail(email, password, name);
      }
      
      setUser(user);
      navigate('/dashboard');
    } catch (error) {
      console.error('Authentication error:', error);
      setError(
        error instanceof Error
          ? error.message
          : 'Authentication failed. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle social login
  const handleSocialLogin = async (provider: 'google' | 'github') => {
    setLoading(true);
    setError(null);

    try {
      const user = await (provider === 'google' ? signInWithGoogle() : signInWithGithub());
      setUser(user);
      navigate('/dashboard');
    } catch (error) {
      console.error(`${provider} login error:`, error);
      setError(
        error instanceof Error
          ? error.message
          : `${provider} login failed. Please try again.`
      );
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        py: 8,
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={3}
          sx={{
            p: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            borderRadius: 2,
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 4,
            }}
          >
            <BoltIcon
              sx={{
                color: 'primary.main',
                fontSize: 40,
                mr: 1,
              }}
            />
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 700,
                color: 'primary.main',
              }}
            >
              RapidTrader
            </Typography>
          </Box>
          
          <Typography variant="h5" component="h2" gutterBottom sx={{ fontWeight: 600 }}>
            {isLogin ? 'Sign In' : 'Create Account'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
            {isLogin
              ? 'Sign in to access your RapidTrader account'
              : 'Create a new account to start trading with RapidTrader'}
          </Typography>
          
          {error && (
            <Alert severity="error" sx={{ width: '100%', mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Social Login Buttons */}
          <Box sx={{ width: '100%', mb: 3 }}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<GoogleIcon />}
              onClick={() => handleSocialLogin('google')}
              disabled={loading}
              sx={{
                mb: 2,
                py: 1.5,
                color: 'text.primary',
                borderColor: 'divider',
                '&:hover': {
                  backgroundColor: 'rgba(66, 133, 244, 0.04)',
                  borderColor: '#4285F4',
                },
              }}
            >
              Continue with Google
            </Button>
            
            <Button
              fullWidth
              variant="outlined"
              startIcon={<GithubIcon />}
              onClick={() => handleSocialLogin('github')}
              disabled={loading}
              sx={{
                py: 1.5,
                color: 'text.primary',
                borderColor: 'divider',
                '&:hover': {
                  backgroundColor: 'rgba(36, 41, 46, 0.04)',
                  borderColor: '#24292E',
                },
              }}
            >
              Continue with GitHub
            </Button>
          </Box>

          <Divider sx={{ width: '100%', mb: 3 }}>
            <Typography variant="body2" color="text.secondary">
              OR
            </Typography>
          </Divider>
          
          <Box component="form" onSubmit={handleSubmit} noValidate sx={{ width: '100%' }}>
            {!isLogin && (
              <TextField
                margin="normal"
                required
                fullWidth
                label="Full Name"
                name="name"
                autoComplete="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                error={!!formErrors.name}
                helperText={formErrors.name}
                sx={{ mb: 2 }}
              />
            )}
            
            <TextField
              margin="normal"
              required
              fullWidth
              label="Email Address"
              name="email"
              autoComplete="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              error={!!formErrors.email}
              helperText={formErrors.email}
              sx={{ mb: 2 }}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              autoComplete={isLogin ? 'current-password' : 'new-password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              error={!!formErrors.password}
              helperText={formErrors.password}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 3 }}
            />
            
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={loading}
              sx={{
                py: 1.5,
                fontWeight: 600,
                textTransform: 'none',
                fontSize: '1rem',
                mb: 2,
                position: 'relative',
              }}
            >
              {loading ? (
                <CircularProgress size={24} sx={{ color: 'white' }} />
              ) : (
                isLogin ? 'Sign In' : 'Create Account'
              )}
            </Button>
            
            {isLogin && (
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Link href="#" variant="body2" color="primary.main">
                  Forgot your password?
                </Link>
              </Box>
            )}
            
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                {isLogin ? "Don't have an account?" : "Already have an account?"}
                <Link
                  href="#"
                  variant="body2"
                  color="primary.main"
                  sx={{ ml: 1, fontWeight: 500 }}
                  onClick={(e) => {
                    e.preventDefault();
                    toggleAuthMode();
                  }}
                >
                  {isLogin ? 'Sign Up' : 'Sign In'}
                </Link>
              </Typography>
            </Box>
          </Box>
        </Paper>
        
        <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 4 }}>
          &copy; {new Date().getFullYear()} RapidTrader. All rights reserved.
        </Typography>
      </Container>
    </Box>
  );
};

export default Login;