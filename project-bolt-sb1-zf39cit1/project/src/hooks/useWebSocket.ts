import { useEffect, useRef, useState, useCallback } from 'react';
import { useMarketDataStore } from '../store/marketDataStore';

interface WebSocketHookOptions {
  url: string;
  onOpen?: (event: WebSocketEventMap['open']) => void;
  onMessage?: (event: WebSocketEventMap['message']) => void;
  onError?: (event: WebSocketEventMap['error']) => void;
  onClose?: (event: WebSocketEventMap['close']) => void;
  reconnectInterval?: number;
  reconnectAttempts?: number;
  autoConnect?: boolean;
}

export function useWebSocket({
  url,
  onOpen,
  onMessage,
  onError,
  onClose,
  reconnectInterval = 5000,
  reconnectAttempts = 10,
  autoConnect = true,
}: WebSocketHookOptions) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const socketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<number | null>(null);
  const reconnectCountRef = useRef(0);
  const { setConnectionStatus: updateStoreConnectionStatus } = useMarketDataStore();
  
  // Connect to WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.readyState === WebSocket.OPEN) return;

    try {
      setConnectionStatus('connecting');
      updateStoreConnectionStatus('connecting');
      
      socketRef.current = new WebSocket(url);
      
      socketRef.current.onopen = (event) => {
        setIsConnected(true);
        setConnectionStatus('connected');
        updateStoreConnectionStatus('connected');
        reconnectCountRef.current = 0;
        onOpen?.(event);
      };
      
      socketRef.current.onmessage = (event) => {
        onMessage?.(event);
      };
      
      socketRef.current.onerror = (event) => {
        onError?.(event);
      };
      
      socketRef.current.onclose = (event) => {
        setIsConnected(false);
        setConnectionStatus('disconnected');
        updateStoreConnectionStatus('disconnected');
        onClose?.(event);
        
        // Attempt to reconnect if not closed deliberately
        if (reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current += 1;
          reconnectTimeoutRef.current = window.setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };
    } catch (error) {
      console.error('WebSocket connection error:', error);
    }
  }, [url, onOpen, onMessage, onError, onClose, reconnectAttempts, reconnectInterval, updateStoreConnectionStatus]);
  
  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionStatus('disconnected');
    updateStoreConnectionStatus('disconnected');
  }, [updateStoreConnectionStatus]);
  
  // Send message through WebSocket
  const send = useCallback((data: string | ArrayBufferLike | Blob | ArrayBufferView) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(data);
      return true;
    }
    return false;
  }, []);
  
  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);
  
  return {
    isConnected,
    connectionStatus,
    connect,
    disconnect,
    send,
  };
}