import { useEffect, useCallback } from 'react';
import { useWebSocket } from './useWebSocket';
import { useMarketDataStore, MarketData } from '../store/marketDataStore';

// Define the WebSocket message types
interface MarketDataMessage {
  type: 'marketData';
  data: MarketData;
}

interface SubscriptionMessage {
  type: 'subscribe' | 'unsubscribe';
  symbols: string[];
}

export function useMarketData() {
  const wsUrl = import.meta.env.VITE_WS_URL || 'wss://ws.rapidtrader.com/market';
  const { 
    subscribedSymbols, 
    marketData,
    updateMarketData,
    subscribeSymbol: addSymbol,
    unsubscribeSymbol: removeSymbol,
    connectionStatus,
  } = useMarketDataStore();
  
  const { send, isConnected } = useWebSocket({
    url: wsUrl,
    onMessage: (event) => {
      try {
        const message = JSON.parse(event.data) as MarketDataMessage;
        if (message.type === 'marketData') {
          updateMarketData(message.data);
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    },
  });
  
  // Subscribe to a symbol
  const subscribeSymbol = useCallback((symbol: string) => {
    addSymbol(symbol);
    
    if (isConnected) {
      const message: SubscriptionMessage = {
        type: 'subscribe',
        symbols: [symbol],
      };
      send(JSON.stringify(message));
    }
  }, [addSymbol, isConnected, send]);
  
  // Unsubscribe from a symbol
  const unsubscribeSymbol = useCallback((symbol: string) => {
    removeSymbol(symbol);
    
    if (isConnected) {
      const message: SubscriptionMessage = {
        type: 'unsubscribe',
        symbols: [symbol],
      };
      send(JSON.stringify(message));
    }
  }, [removeSymbol, isConnected, send]);
  
  // When connection is established, subscribe to all saved symbols
  useEffect(() => {
    if (isConnected && subscribedSymbols.length > 0) {
      const message: SubscriptionMessage = {
        type: 'subscribe',
        symbols: subscribedSymbols,
      };
      send(JSON.stringify(message));
    }
  }, [isConnected, subscribedSymbols, send]);
  
  return {
    marketData,
    subscribedSymbols,
    subscribeSymbol,
    unsubscribeSymbol,
    connectionStatus,
    isConnected,
  };
}