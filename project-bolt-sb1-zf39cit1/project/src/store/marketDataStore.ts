import { create } from 'zustand';

export interface Instrument {
  symbol: string;
  exchange: string;
  fullName: string;
}

export interface MarketData {
  symbol: string;
  ltp: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  close: number;
  volume: number;
  lastUpdated: Date;
}

interface MarketDataState {
  instruments: Instrument[];
  marketData: Record<string, MarketData>;
  subscribedSymbols: string[];
  isConnected: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'connecting';
  
  setInstruments: (instruments: Instrument[]) => void;
  updateMarketData: (data: MarketData) => void;
  subscribeSymbol: (symbol: string) => void;
  unsubscribeSymbol: (symbol: string) => void;
  setConnectionStatus: (status: 'connected' | 'disconnected' | 'connecting') => void;
}

export const useMarketDataStore = create<MarketDataState>((set) => ({
  instruments: [],
  marketData: {},
  subscribedSymbols: [],
  isConnected: false,
  connectionStatus: 'disconnected',
  
  setInstruments: (instruments) => set({ instruments }),
  
  updateMarketData: (data) => set((state) => ({
    marketData: {
      ...state.marketData,
      [data.symbol]: data
    }
  })),
  
  subscribeSymbol: (symbol) => set((state) => ({
    subscribedSymbols: state.subscribedSymbols.includes(symbol) 
      ? state.subscribedSymbols 
      : [...state.subscribedSymbols, symbol]
  })),
  
  unsubscribeSymbol: (symbol) => set((state) => ({
    subscribedSymbols: state.subscribedSymbols.filter(s => s !== symbol)
  })),
  
  setConnectionStatus: (status) => set({ 
    connectionStatus: status,
    isConnected: status === 'connected'
  }),
}));