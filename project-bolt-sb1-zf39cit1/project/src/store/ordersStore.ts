import { create } from 'zustand';

export type OrderType = 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
export type OrderSide = 'BUY' | 'SELL';
export type OrderStatus = 'OPEN' | 'COMPLETE' | 'CANCELLED' | 'REJECTED';

export interface Order {
  id: string;
  symbol: string;
  side: OrderSide;
  type: OrderType;
  quantity: number;
  price: number | null;
  triggerPrice: number | null;
  status: OrderStatus;
  createdAt: string;
  updatedAt: string;
  userId: string;
  exchange: string;
  product: string;
}

interface OrdersState {
  orders: Order[];
  loading: boolean;
  error: string | null;
  
  setOrders: (orders: Order[]) => void;
  addOrder: (order: Order) => void;
  updateOrder: (orderId: string, updates: Partial<Order>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useOrdersStore = create<OrdersState>((set) => ({
  orders: [],
  loading: false,
  error: null,
  
  setOrders: (orders) => set({ orders }),
  
  addOrder: (order) => set((state) => ({
    orders: [order, ...state.orders]
  })),
  
  updateOrder: (orderId, updates) => set((state) => ({
    orders: state.orders.map(order => 
      order.id === orderId 
        ? { ...order, ...updates, updatedAt: new Date().toISOString() } 
        : order
    )
  })),
  
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
}));