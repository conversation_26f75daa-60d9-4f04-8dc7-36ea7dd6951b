import { create } from 'zustand';

export type PositionType = 'INTRADAY' | 'DELIVERY';

export interface Position {
  id: string;
  symbol: string;
  exchange: string;
  quantity: number;
  avgPrice: number;
  ltp: number;
  pnl: number;
  pnlPercent: number;
  type: PositionType;
  side: 'LONG' | 'SHORT';
  openTime: string;
  userId: string;
}

interface PositionsState {
  positions: Position[];
  loading: boolean;
  error: string | null;
  
  setPositions: (positions: Position[]) => void;
  updatePosition: (positionId: string, updates: Partial<Position>) => void;
  updatePositionLTP: (symbol: string, ltp: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const usePositionsStore = create<PositionsState>((set) => ({
  positions: [],
  loading: false,
  error: null,
  
  setPositions: (positions) => set({ positions }),
  
  updatePosition: (positionId, updates) => set((state) => ({
    positions: state.positions.map(position => 
      position.id === positionId 
        ? { ...position, ...updates } 
        : position
    )
  })),
  
  updatePositionLTP: (symbol, ltp) => set((state) => ({
    positions: state.positions.map(position => {
      if (position.symbol === symbol) {
        const pnl = position.side === 'LONG' 
          ? (ltp - position.avgPrice) * position.quantity
          : (position.avgPrice - ltp) * position.quantity;
        
        const pnlPercent = position.side === 'LONG'
          ? ((ltp - position.avgPrice) / position.avgPrice) * 100
          : ((position.avgPrice - ltp) / position.avgPrice) * 100;
          
        return {
          ...position,
          ltp,
          pnl,
          pnlPercent
        };
      }
      return position;
    })
  })),
  
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
}));