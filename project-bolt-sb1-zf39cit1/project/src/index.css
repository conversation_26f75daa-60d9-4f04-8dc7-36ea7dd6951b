@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-main: #2196F3;
  --primary-light: #64B5F6;
  --primary-dark: #1976D2;
  --secondary-main: #4CAF50;
  --secondary-light: #81C784;
  --secondary-dark: #388E3C;
  --accent-main: #FF9800;
  --accent-light: #FFB74D;
  --accent-dark: #F57C00;
  --success-main: #4CAF50;
  --warning-main: #FF9800;
  --error-main: #F44336;
  --background-light: #F5F5F5;
  --background-dark: #121212;
  --text-primary-light: rgba(0, 0, 0, 0.87);
  --text-secondary-light: rgba(0, 0, 0, 0.6);
  --text-primary-dark: rgba(255, 255, 255, 0.87);
  --text-secondary-dark: rgba(255, 255, 255, 0.6);
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.slideUp {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* WebSocket status indicators */
.status-connected {
  background-color: var(--success-main);
}

.status-disconnected {
  background-color: var(--error-main);
}

.status-connecting {
  background-color: var(--warning-main);
}

/* Profit/Loss colors */
.profit {
  color: var(--success-main);
}

.loss {
  color: var(--error-main);
}

/* Transitions */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease;
}