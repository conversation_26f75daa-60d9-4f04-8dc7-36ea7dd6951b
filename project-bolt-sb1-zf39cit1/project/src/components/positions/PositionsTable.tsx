import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Button,
  Chip,
  Box,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { useState } from 'react';
import { Position, usePositionsStore } from '../../store/positionsStore';
import { closePosition } from '../../services/positionsService';

const PositionsTable = () => {
  const { positions, updatePosition, loading, error } = usePositionsStore();
  const [closingPosition, setClosingPosition] = useState<string | null>(null);
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; positionId: string }>({
    open: false,
    positionId: '',
  });
  
  // Handle position closing
  const handleClosePosition = async (positionId: string) => {
    setClosingPosition(positionId);
    try {
      await closePosition(positionId, 'MARKET');
      updatePosition(positionId, { quantity: 0 });
    } catch (error) {
      console.error('Failed to close position:', error);
    } finally {
      setClosingPosition(null);
    }
  };
  
  // Open confirmation dialog
  const openConfirmDialog = (positionId: string) => {
    setConfirmDialog({
      open: true,
      positionId,
    });
  };
  
  // Close confirmation dialog
  const closeConfirmDialog = () => {
    setConfirmDialog({
      open: false,
      positionId: '',
    });
  };
  
  // Confirm position closing
  const confirmClosePosition = () => {
    handleClosePosition(confirmDialog.positionId);
    closeConfirmDialog();
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Typography color="error" sx={{ p: 2 }}>
        {error}
      </Typography>
    );
  }
  
  return (
    <>
      <TableContainer component={Paper} elevation={1}>
        <Table sx={{ minWidth: 650 }} size="medium">
          <TableHead>
            <TableRow sx={{ backgroundColor: 'background.paper' }}>
              <TableCell>Symbol</TableCell>
              <TableCell>Type</TableCell>
              <TableCell align="right">Quantity</TableCell>
              <TableCell align="right">Avg. Price</TableCell>
              <TableCell align="right">LTP</TableCell>
              <TableCell align="right">P&L</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {positions.length > 0 ? (
              positions.map((position) => (
                <PositionRow
                  key={position.id}
                  position={position}
                  onClosePosition={openConfirmDialog}
                  isClosing={closingPosition === position.id}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No positions found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={closeConfirmDialog}
        aria-labelledby="close-position-dialog-title"
      >
        <DialogTitle id="close-position-dialog-title">
          Close Position
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to close this position? This will create a market order to exit your position immediately.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeConfirmDialog} color="inherit">
            Cancel
          </Button>
          <Button onClick={confirmClosePosition} color="error" variant="contained">
            Close Position
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

// Position row component
interface PositionRowProps {
  position: Position;
  onClosePosition: (id: string) => void;
  isClosing: boolean;
}

const PositionRow = ({ position, onClosePosition, isClosing }: PositionRowProps) => {
  // Calculate class for profit/loss styling
  const pnlClass = position.pnl > 0 ? 'profit' : position.pnl < 0 ? 'loss' : '';
  
  return (
    <TableRow
      sx={{
        '&:last-child td, &:last-child th': { border: 0 },
        backgroundColor: position.side === 'LONG' 
          ? 'rgba(76, 175, 80, 0.04)' 
          : 'rgba(244, 67, 54, 0.04)',
      }}
    >
      <TableCell component="th" scope="row">
        <Typography variant="body2" fontWeight="medium">
          {position.symbol}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {position.exchange}
        </Typography>
      </TableCell>
      <TableCell>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Chip
            label={position.side}
            size="small"
            color={position.side === 'LONG' ? 'success' : 'error'}
            sx={{ width: 'fit-content' }}
          />
          <Typography variant="caption" color="text.secondary">
            {position.type}
          </Typography>
        </Box>
      </TableCell>
      <TableCell align="right">
        <Typography variant="body2">
          {position.quantity}
        </Typography>
      </TableCell>
      <TableCell align="right">
        <Typography variant="body2">
          {position.avgPrice.toFixed(2)}
        </Typography>
      </TableCell>
      <TableCell align="right">
        <Typography variant="body2">
          {position.ltp.toFixed(2)}
        </Typography>
      </TableCell>
      <TableCell align="right">
        <Typography
          variant="body2"
          className={pnlClass}
          fontWeight="medium"
        >
          {position.pnl.toFixed(2)}
        </Typography>
        <Typography
          variant="caption"
          className={pnlClass}
          display="block"
        >
          ({position.pnlPercent.toFixed(2)}%)
        </Typography>
      </TableCell>
      <TableCell align="center">
        <Button
          variant="outlined"
          color="error"
          size="small"
          disabled={isClosing || position.quantity === 0}
          onClick={() => onClosePosition(position.id)}
          sx={{ minWidth: '100px' }}
        >
          {isClosing ? <CircularProgress size={20} /> : 'Close'}
        </Button>
      </TableCell>
    </TableRow>
  );
};

export default PositionsTable;