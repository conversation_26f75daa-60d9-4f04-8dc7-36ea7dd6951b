import { useState, useEffect, useRef } from 'react';
import {
  ListItem,
  ListItemText,
  Typography,
  Box,
  IconButton,
  Collapse,
  ButtonGroup,
  Button,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
} from '@mui/icons-material';
import { useMarketData } from '../../hooks/useMarketData';
import { MarketData } from '../../store/marketDataStore';

interface MarketWatchItemProps {
  symbol: string;
  name: string;
  onBuyClick: (symbol: string) => void;
  onSellClick: (symbol: string) => void;
}

const MarketWatchItem = ({ symbol, name, onBuyClick, onSellClick }: MarketWatchItemProps) => {
  const [expanded, setExpanded] = useState(false);
  const [favorite, setFavorite] = useState(false);
  const [highlight, setHighlight] = useState<'none' | 'up' | 'down'>('none');
  const { marketData, subscribeSymbol, unsubscribeSymbol } = useMarketData();
  const prevPrice = useRef<number | null>(null);
  const data: MarketData | undefined = marketData[symbol];

  // Subscribe to symbol data when component mounts
  useEffect(() => {
    subscribeSymbol(symbol);
    return () => {
      unsubscribeSymbol(symbol);
    };
  }, [symbol, subscribeSymbol, unsubscribeSymbol]);

  // Highlight price changes
  useEffect(() => {
    if (data && prevPrice.current !== null) {
      if (data.ltp > prevPrice.current) {
        setHighlight('up');
      } else if (data.ltp < prevPrice.current) {
        setHighlight('down');
      }
      
      const timer = setTimeout(() => {
        setHighlight('none');
      }, 1000);
      
      return () => clearTimeout(timer);
    }
    
    if (data) {
      prevPrice.current = data.ltp;
    }
  }, [data]);

  if (!data) {
    return (
      <ListItem divider>
        <ListItemText
          primary={symbol}
          secondary={name}
        />
        <Typography variant="body2" color="text.secondary">
          Loading...
        </Typography>
      </ListItem>
    );
  }

  return (
    <>
      <ListItem
        divider
        secondaryAction={
          <IconButton edge="end\" onClick={() => setExpanded(!expanded)}>
            {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        }
        sx={{
          transition: 'background-color 0.2s',
          '&:hover': {
            backgroundColor: 'action.hover',
          },
        }}
      >
        <IconButton 
          size="small" 
          onClick={() => setFavorite(!favorite)}
          sx={{ mr: 1 }}
        >
          {favorite ? 
            <FavoriteIcon fontSize="small\" color="error" /> : 
            <FavoriteBorderIcon fontSize="small" />
          }
        </IconButton>
        
        <ListItemText
          primary={
            <Typography variant="subtitle2">
              {symbol}
            </Typography>
          }
          secondary={name}
        />
        
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', mr: 2 }}>
          <Typography 
            variant="subtitle1" 
            sx={{ 
              fontWeight: 'medium',
              color: highlight === 'up' 
                ? 'success.main' 
                : highlight === 'down' 
                  ? 'error.main' 
                  : 'text.primary',
              transition: 'color 0.5s ease-in-out',
            }}
          >
            {data.ltp.toFixed(2)}
          </Typography>
          
          <Typography 
            variant="body2" 
            color={data.change >= 0 ? 'success.main' : 'error.main'}
          >
            {data.change > 0 ? '+' : ''}{data.change.toFixed(2)} ({data.changePercent.toFixed(2)}%)
          </Typography>
        </Box>
      </ListItem>
      
      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Box>
              <Typography variant="caption" color="text.secondary">High</Typography>
              <Typography variant="body2">{data.high.toFixed(2)}</Typography>
            </Box>
            <Box>
              <Typography variant="caption" color="text.secondary">Low</Typography>
              <Typography variant="body2">{data.low.toFixed(2)}</Typography>
            </Box>
            <Box>
              <Typography variant="caption" color="text.secondary">Open</Typography>
              <Typography variant="body2">{data.open.toFixed(2)}</Typography>
            </Box>
            <Box>
              <Typography variant="caption" color="text.secondary">Close</Typography>
              <Typography variant="body2">{data.close.toFixed(2)}</Typography>
            </Box>
          </Box>
          
          <ButtonGroup variant="contained" size="small" fullWidth>
            <Button 
              color="success" 
              onClick={() => onBuyClick(symbol)}
              sx={{ fontWeight: 'bold' }}
            >
              BUY
            </Button>
            <Button 
              color="error" 
              onClick={() => onSellClick(symbol)}
              sx={{ fontWeight: 'bold' }}
            >
              SELL
            </Button>
          </ButtonGroup>
        </Box>
      </Collapse>
    </>
  );
};

export default MarketWatchItem;