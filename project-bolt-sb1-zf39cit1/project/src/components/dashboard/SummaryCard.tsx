import { 
  Card, 
  CardContent, 
  Typography, 
  Box, 
  Divider,
  useTheme
} from '@mui/material';
import { ReactNode } from 'react';

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: string;
}

const SummaryCard = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  trendValue,
  color,
}: SummaryCardProps) => {
  const theme = useTheme();

  // Define colors based on trend
  const trendColor = trend === 'up' 
    ? theme.palette.success.main 
    : trend === 'down' 
      ? theme.palette.error.main 
      : theme.palette.info.main;

  return (
    <Card 
      elevation={1}
      sx={{
        height: '100%',
        borderTop: color ? `4px solid ${color}` : 'none',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography 
            variant="subtitle1" 
            color="text.secondary"
            sx={{ fontWeight: 500 }}
          >
            {title}
          </Typography>
          <Box 
            sx={{ 
              color: color || 'primary.main',
              backgroundColor: color ? alpha(color, 0.1) : 'rgba(33, 150, 243, 0.1)',
              p: 1,
              borderRadius: '50%'
            }}
          >
            {icon}
          </Box>
        </Box>
        
        <Typography 
          variant="h4" 
          component="div"
          sx={{ 
            fontWeight: 700,
            mb: 1
          }}
        >
          {value}
        </Typography>
        
        {subtitle && (
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ mb: 2 }}
          >
            {subtitle}
          </Typography>
        )}
        
        {trend && trendValue && (
          <>
            <Divider sx={{ my: 1.5 }} />
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Box
                component="span"
                sx={{ 
                  color: trendColor,
                  display: 'flex',
                  alignItems: 'center',
                  mr: 1
                }}
              >
                {trend === 'up' ? '↑' : trend === 'down' ? '↓' : '→'}
              </Box>
              <Typography 
                variant="body2" 
                component="span"
                sx={{ 
                  color: trendColor,
                  fontWeight: 500
                }}
              >
                {trendValue}
              </Typography>
            </Box>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default SummaryCard;