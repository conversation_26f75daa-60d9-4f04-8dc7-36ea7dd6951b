import { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  Typography,
  Box,
  Divider,
  CircularProgress,
} from '@mui/material';
import { useOrdersStore, OrderType, OrderSide } from '../../store/ordersStore';
import { createOrder, CreateOrderRequest } from '../../services/ordersService';
import { useMarketDataStore } from '../../store/marketDataStore';

interface OrderFormProps {
  open: boolean;
  onClose: () => void;
  initialSymbol?: string;
  initialSide?: OrderSide;
}

const OrderForm = ({ open, onClose, initialSymbol = '', initialSide = 'BUY' }: OrderFormProps) => {
  const { instruments } = useMarketDataStore();
  const { addOrder } = useOrdersStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Order form state
  const [orderData, setOrderData] = useState<CreateOrderRequest>({
    symbol: initialSymbol,
    side: initialSide,
    type: 'LIMIT',
    quantity: 1,
    exchange: 'NSE',
    product: 'INTRADAY',
    price: 0,
  });
  
  // Form validation errors
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  
  // Handle input changes
  const handleChange = (field: keyof CreateOrderRequest, value: any) => {
    setOrderData({
      ...orderData,
      [field]: value,
    });
    
    // Clear error for the field being changed
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: '',
      });
    }
  };
  
  // Validate form data
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!orderData.symbol) {
      errors.symbol = 'Symbol is required';
    }
    
    if (orderData.quantity <= 0) {
      errors.quantity = 'Quantity must be greater than 0';
    }
    
    if (orderData.type === 'LIMIT' && (!orderData.price || orderData.price <= 0)) {
      errors.price = 'Price must be greater than 0 for LIMIT orders';
    }
    
    if (orderData.type === 'SL' && (!orderData.triggerPrice || orderData.triggerPrice <= 0)) {
      errors.triggerPrice = 'Trigger price must be greater than 0 for SL orders';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const order = await createOrder(orderData);
      addOrder(order);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to place order');
    } finally {
      setLoading(false);
    }
  };
  
  // Reset form when dialog closes
  const handleDialogClose = () => {
    setOrderData({
      symbol: '',
      side: 'BUY',
      type: 'LIMIT',
      quantity: 1,
      exchange: 'NSE',
      product: 'INTRADAY',
      price: 0,
    });
    setFormErrors({});
    setError(null);
    onClose();
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={handleDialogClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{ bgcolor: 'background.paper', pb: 1 }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {orderData.side === 'BUY' ? 'Buy Order' : 'Sell Order'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Place a new order in the market
        </Typography>
      </DialogTitle>
      
      <Divider />
      
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth error={!!formErrors.symbol}>
                <InputLabel>Symbol</InputLabel>
                <Select
                  value={orderData.symbol}
                  label="Symbol"
                  onChange={(e) => handleChange('symbol', e.target.value)}
                >
                  {instruments.map((instrument) => (
                    <MenuItem key={instrument.symbol} value={instrument.symbol}>
                      {instrument.symbol} - {instrument.fullName}
                    </MenuItem>
                  ))}
                </Select>
                {formErrors.symbol && <FormHelperText>{formErrors.symbol}</FormHelperText>}
              </FormControl>
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Side</InputLabel>
                <Select
                  value={orderData.side}
                  label="Side"
                  onChange={(e) => handleChange('side', e.target.value)}
                >
                  <MenuItem value="BUY">BUY</MenuItem>
                  <MenuItem value="SELL">SELL</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Order Type</InputLabel>
                <Select
                  value={orderData.type}
                  label="Order Type"
                  onChange={(e) => handleChange('type', e.target.value as OrderType)}
                >
                  <MenuItem value="MARKET">MARKET</MenuItem>
                  <MenuItem value="LIMIT">LIMIT</MenuItem>
                  <MenuItem value="SL">STOP LOSS</MenuItem>
                  <MenuItem value="SL-M">STOP LOSS MARKET</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Quantity"
                type="number"
                value={orderData.quantity}
                onChange={(e) => handleChange('quantity', parseInt(e.target.value, 10))}
                error={!!formErrors.quantity}
                helperText={formErrors.quantity}
              />
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Product</InputLabel>
                <Select
                  value={orderData.product}
                  label="Product"
                  onChange={(e) => handleChange('product', e.target.value)}
                >
                  <MenuItem value="INTRADAY">INTRADAY</MenuItem>
                  <MenuItem value="DELIVERY">DELIVERY</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {(orderData.type === 'LIMIT' || orderData.type === 'SL') && (
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Price"
                  type="number"
                  value={orderData.price}
                  onChange={(e) => handleChange('price', parseFloat(e.target.value))}
                  error={!!formErrors.price}
                  helperText={formErrors.price}
                />
              </Grid>
            )}
            
            {(orderData.type === 'SL' || orderData.type === 'SL-M') && (
              <Grid item xs={6}>
                <TextField
                  fullWidth
                  label="Trigger Price"
                  type="number"
                  value={orderData.triggerPrice || ''}
                  onChange={(e) => handleChange('triggerPrice', parseFloat(e.target.value))}
                  error={!!formErrors.triggerPrice}
                  helperText={formErrors.triggerPrice}
                />
              </Grid>
            )}
          </Grid>
        </Box>
        
        {error && (
          <Typography color="error" variant="body2" sx={{ mt: 2 }}>
            {error}
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleDialogClose} color="inherit">
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          color={orderData.side === 'BUY' ? 'success' : 'error'}
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Placing Order...' : `Place ${orderData.side} Order`}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderForm;