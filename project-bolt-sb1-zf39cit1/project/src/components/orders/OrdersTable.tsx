import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Box,
  TablePagination,
  TextField,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import {
  Cancel as CancelIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { Order, useOrdersStore, OrderStatus } from '../../store/ordersStore';
import { cancelOrder } from '../../services/ordersService';

// Helper function to get status chip color
const getStatusColor = (status: OrderStatus) => {
  switch (status) {
    case 'COMPLETE':
      return 'success';
    case 'OPEN':
      return 'primary';
    case 'CANCELLED':
      return 'warning';
    case 'REJECTED':
      return 'error';
    default:
      return 'default';
  }
};

const OrdersTable = () => {
  const { orders, updateOrder, loading, setLoading, error } = useOrdersStore();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [search, setSearch] = useState('');
  const [cancelling, setCancelling] = useState<string | null>(null);
  
  // Filter orders based on search
  const filteredOrders = orders.filter(
    (order) =>
      order.symbol.toLowerCase().includes(search.toLowerCase()) ||
      order.id.toLowerCase().includes(search.toLowerCase())
  );
  
  // Handle order cancellation
  const handleCancelOrder = async (orderId: string) => {
    setCancelling(orderId);
    try {
      await cancelOrder(orderId);
      updateOrder(orderId, { status: 'CANCELLED' });
    } catch (error) {
      console.error('Failed to cancel order:', error);
    } finally {
      setCancelling(null);
    }
  };
  
  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  
  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between' }}>
        <TextField
          placeholder="Search orders..."
          variant="outlined"
          size="small"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
          }}
          sx={{ width: 300 }}
        />
        
        <Tooltip title="Refresh orders">
          <IconButton
            color="primary"
            onClick={() => setLoading(true)}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>
      
      {error && (
        <Typography color="error" sx={{ mb: 2 }}>
          {error}
        </Typography>
      )}
      
      <TableContainer component={Paper} elevation={1}>
        <Table sx={{ minWidth: 650 }} size="medium">
          <TableHead>
            <TableRow sx={{ backgroundColor: 'background.paper' }}>
              <TableCell>Order ID</TableCell>
              <TableCell>Symbol</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Side</TableCell>
              <TableCell align="right">Quantity</TableCell>
              <TableCell align="right">Price</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Time</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredOrders
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((order) => (
                <TableRow 
                  key={order.id}
                  sx={{ 
                    '&:last-child td, &:last-child th': { border: 0 },
                    backgroundColor: order.side === 'BUY' 
                      ? 'rgba(76, 175, 80, 0.04)' 
                      : 'rgba(244, 67, 54, 0.04)',
                  }}
                >
                  <TableCell component="th" scope="row">
                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                      {order.id.substring(0, 8)}...
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {order.symbol}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {order.exchange}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {order.type}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {order.product}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography 
                      variant="body2" 
                      color={order.side === 'BUY' ? 'success.main' : 'error.main'}
                      fontWeight="medium"
                    >
                      {order.side}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {order.quantity}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {order.price 
                        ? order.price.toFixed(2) 
                        : order.type === 'MARKET' 
                          ? 'MARKET' 
                          : '-'
                      }
                    </Typography>
                    {order.triggerPrice && (
                      <Typography variant="caption" color="text.secondary">
                        Trigger: {order.triggerPrice.toFixed(2)}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={order.status}
                      size="small"
                      color={getStatusColor(order.status)}
                      sx={{ fontWeight: 500 }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="caption" color="text.secondary">
                      {new Date(order.createdAt).toLocaleTimeString()}
                    </Typography>
                    <Typography variant="caption" display="block" color="text.secondary">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    {order.status === 'OPEN' && (
                      <Tooltip title="Cancel order">
                        <IconButton
                          size="small"
                          color="warning"
                          onClick={() => handleCancelOrder(order.id)}
                          disabled={cancelling === order.id}
                        >
                          {cancelling === order.id ? (
                            <CircularProgress size={20} />
                          ) : (
                            <CancelIcon fontSize="small" />
                          )}
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            {filteredOrders.length === 0 && (
              <TableRow>
                <TableCell colSpan={9} align="center" sx={{ py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    No orders found
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={filteredOrders.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Box>
  );
};

export default OrdersTable;