import { Box, Tooltip, Typography } from '@mui/material';
import { useMarketDataStore } from '../../store/marketDataStore';

const ConnectionStatus = () => {
  const { connectionStatus } = useMarketDataStore();

  // Define status properties based on connection state
  const statusProps = {
    connected: {
      color: 'success.main',
      label: 'Connected',
      tooltip: 'WebSocket connection is active',
    },
    disconnected: {
      color: 'error.main',
      label: 'Disconnected',
      tooltip: 'WebSocket connection is down',
    },
    connecting: {
      color: 'warning.main',
      label: 'Connecting',
      tooltip: 'Establishing WebSocket connection',
    },
  };

  const currentStatus = statusProps[connectionStatus];

  return (
    <Tooltip title={currentStatus.tooltip}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mx: 2,
        }}
      >
        <Box
          sx={{
            width: 10,
            height: 10,
            borderRadius: '50%',
            bgcolor: currentStatus.color,
            mr: 1,
            animation: connectionStatus === 'connecting' ? 'pulse 1.5s infinite' : 'none',
            '@keyframes pulse': {
              '0%': { opacity: 0.4 },
              '50%': { opacity: 1 },
              '100%': { opacity: 0.4 },
            },
          }}
        />
        <Typography
          variant="body2"
          component="span"
          sx={{
            fontSize: '0.75rem',
            color: 'text.secondary',
          }}
        >
          {currentStatus.label}
        </Typography>
      </Box>
    </Tooltip>
  );
};

export default ConnectionStatus;