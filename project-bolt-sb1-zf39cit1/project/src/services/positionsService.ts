import { apiClient } from './apiClient';
import { Position } from '../store/positionsStore';

// API endpoints
const POSITIONS_ENDPOINT = '/positions';

// Interface for position filters
export interface PositionFilters {
  symbol?: string;
  exchange?: string;
  type?: string;
}

// Get all positions with optional filters
export const getPositions = async (filters?: PositionFilters): Promise<Position[]> => {
  return apiClient.get(POSITIONS_ENDPOINT, { params: filters });
};

// Get a specific position by ID
export const getPositionById = async (positionId: string): Promise<Position> => {
  return apiClient.get(`${POSITIONS_ENDPOINT}/${positionId}`);
};

// Close a position (create an opposite order)
export const closePosition = async (positionId: string, exitPrice?: 'MARKET' | number): Promise<{ success: boolean; message: string; order?: any }> => {
  return apiClient.post(`${POSITIONS_ENDPOINT}/${positionId}/close`, { exitPrice });
};

// Convert position type (e.g., from INTRADAY to DELIVERY)
export const convertPosition = async (positionId: string, newType: 'INTRADAY' | 'DELIVERY'): Promise<Position> => {
  return apiClient.put(`${POSITIONS_ENDPOINT}/${positionId}/convert`, { type: newType });
};