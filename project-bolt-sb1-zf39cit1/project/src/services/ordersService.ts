import { apiClient } from './apiClient';
import { Order } from '../store/ordersStore';

// API endpoints
const ORDERS_ENDPOINT = '/orders';

// Interface for order creation
export interface CreateOrderRequest {
  symbol: string;
  side: 'BUY' | 'SELL';
  type: 'MARKET' | 'LIMIT' | 'SL' | 'SL-M';
  quantity: number;
  price?: number;
  triggerPrice?: number;
  exchange: string;
  product: string;
}

// Interface for order filters
export interface OrderFilters {
  status?: string;
  symbol?: string;
  side?: string;
  fromDate?: string;
  toDate?: string;
  page?: number;
  limit?: number;
}

// Get all orders with optional filters
export const getOrders = async (filters?: OrderFilters): Promise<{ orders: Order[], total: number }> => {
  return apiClient.get(ORDERS_ENDPOINT, { params: filters });
};

// Get a specific order by ID
export const getOrderById = async (orderId: string): Promise<Order> => {
  return apiClient.get(`${ORDERS_ENDPOINT}/${orderId}`);
};

// Create a new order
export const createOrder = async (orderData: CreateOrderRequest): Promise<Order> => {
  return apiClient.post(ORDERS_ENDPOINT, orderData);
};

// Cancel an order
export const cancelOrder = async (orderId: string): Promise<{ success: boolean; message: string }> => {
  return apiClient.delete(`${ORDERS_ENDPOINT}/${orderId}`);
};

// Modify an existing order
export const modifyOrder = async (orderId: string, updates: Partial<CreateOrderRequest>): Promise<Order> => {
  return apiClient.put(`${ORDERS_ENDPOINT}/${orderId}`, updates);
};