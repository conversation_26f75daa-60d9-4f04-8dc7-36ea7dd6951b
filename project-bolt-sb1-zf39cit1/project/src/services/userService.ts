import { apiClient } from './apiClient';

// API endpoints
const USER_ENDPOINT = '/user';
const PROFILE_ENDPOINT = `${USER_ENDPOINT}/profile`;
const ACCOUNT_ENDPOINT = `${USER_ENDPOINT}/account`;

// User profile interface
export interface UserProfile {
  id: string;
  email: string;
  name: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  profilePictureUrl?: string;
}

// Account details interface
export interface AccountDetails {
  id: string;
  balance: number;
  margin: number;
  usedMargin: number;
  availableMargin: number;
  currency: string;
  accountType: string;
  status: string;
}

// Get user profile
export const getUserProfile = async (): Promise<UserProfile> => {
  return apiClient.get(PROFILE_ENDPOINT);
};

// Update user profile
export const updateUserProfile = async (profileData: Partial<UserProfile>): Promise<UserProfile> => {
  return apiClient.put(PROFILE_ENDPOINT, profileData);
};

// Get account details
export const getAccountDetails = async (): Promise<AccountDetails> => {
  return apiClient.get(ACCOUNT_ENDPOINT);
};

// Change password
export const changePassword = async (currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
  return apiClient.post(`${USER_ENDPOINT}/change-password`, {
    currentPassword,
    newPassword,
  });
};