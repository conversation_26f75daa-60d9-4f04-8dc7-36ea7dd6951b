# 🔧 Developer Integration Guide - Broker Symbol Management

## 🚀 Quick Start

### **1. Basic Setup**
```python
from data.symbol_manager import BrokerSymbolManager

# Initialize the manager
manager = BrokerSymbolManager()
```

### **2. Register Your First Broker**
```python
# Example: Register Fyers
fyers_config = {
    "client_id": "your_fyers_client_id",
    "access_token": "your_access_token",
    "dry_run": True
}

success = manager.register_broker_config("fyers", fyers_config)
# ✅ Automatically downloads symbols and creates mappings
```

### **3. Use in Trading**
```python
# Get broker-specific symbol
symbol = manager.get_broker_symbol_mapping("RELIANCE", "fyers")
# Returns: "NSE:RELIANCE-EQ"

# Use with your broker wrapper
from broker.fyers_wrapper import FyersBroker
broker = FyersBroker()
broker.place_order(symbol=symbol, quantity=10, side="BUY")
```

---

## 🏗️ Integration Patterns

### **Pattern 1: Strategy Integration**
```python
class TradingStrategy:
    def __init__(self, broker_name):
        self.broker_name = broker_name
        self.symbol_manager = BrokerSymbolManager()
        
    def get_tradable_symbol(self, rapidtrader_symbol):
        return self.symbol_manager.get_broker_symbol_mapping(
            rapidtrader_symbol, self.broker_name
        )
    
    def execute_trade(self, symbol, quantity, side):
        broker_symbol = self.get_tradable_symbol(symbol)
        if broker_symbol:
            # Execute with broker-specific symbol
            return self.broker.place_order(
                symbol=broker_symbol, 
                quantity=quantity, 
                side=side
            )
```

### **Pattern 2: Multi-Broker Portfolio**
```python
class MultibrokerPortfolio:
    def __init__(self):
        self.symbol_manager = BrokerSymbolManager()
        self.brokers = {}
    
    def add_broker(self, name, broker_instance, config):
        self.brokers[name] = broker_instance
        # Register and download symbols
        self.symbol_manager.register_broker_config(name, config)
    
    def place_order_across_brokers(self, symbol, quantity, side):
        results = {}
        for broker_name, broker in self.brokers.items():
            broker_symbol = self.symbol_manager.get_broker_symbol_mapping(
                symbol, broker_name
            )
            if broker_symbol:
                results[broker_name] = broker.place_order(
                    symbol=broker_symbol, quantity=quantity, side=side
                )
        return results
```

### **Pattern 3: Symbol Validation**
```python
class SymbolValidator:
    def __init__(self):
        self.symbol_manager = BrokerSymbolManager()
    
    def validate_symbol(self, symbol, broker_name=None):
        if broker_name:
            # Check specific broker
            mapping = self.symbol_manager.get_broker_symbol_mapping(
                symbol, broker_name
            )
            return mapping is not None
        else:
            # Check across all brokers
            results = self.symbol_manager.search_broker_symbols(symbol, limit=1)
            return len(results) > 0
    
    def get_available_brokers(self, symbol):
        brokers = []
        for broker in self.symbol_manager.broker_configs.keys():
            if self.validate_symbol(symbol, broker):
                brokers.append(broker)
        return brokers
```

---

## 🔌 API Gateway Integration

### **Extend Broker Interface**
```python
# In api_gateway/broker_interface.py
class EnhancedBrokerInterface(BrokerInterface):
    def __init__(self, config):
        super().__init__(config)
        self.symbol_manager = BrokerSymbolManager()
        
        # Register broker and download symbols
        self.symbol_manager.register_broker_config(
            self.broker_name, config
        )
    
    def place_order(self, order_data):
        # Convert RapidTrader symbol to broker symbol
        rt_symbol = order_data.get('symbol')
        broker_symbol = self.symbol_manager.get_broker_symbol_mapping(
            rt_symbol, self.broker_name
        )
        
        if broker_symbol:
            order_data['symbol'] = broker_symbol
            return super().place_order(order_data)
        else:
            return {
                "status": "error",
                "message": f"Symbol {rt_symbol} not found for {self.broker_name}"
            }
```

### **Add Symbol Endpoints**
```python
# In api_gateway/main.py
@app.get("/symbols/search")
async def search_symbols(query: str, broker: str = None):
    manager = BrokerSymbolManager()
    results = manager.search_broker_symbols(query, broker, limit=50)
    return {"symbols": results}

@app.get("/symbols/mapping/{symbol}")
async def get_symbol_mapping(symbol: str, broker: str):
    manager = BrokerSymbolManager()
    mapping = manager.get_broker_symbol_mapping(symbol, broker)
    if mapping:
        return {"symbol": symbol, "broker_symbol": mapping, "broker": broker}
    else:
        raise HTTPException(404, "Symbol mapping not found")

@app.get("/brokers/status")
async def get_broker_status():
    manager = BrokerSymbolManager()
    return manager.get_broker_status()
```

---

## 🔄 Automatic Updates

### **Schedule Daily Updates**
```python
# In your main application
import schedule
import time
from data.symbol_manager import BrokerSymbolManager

def daily_symbol_update():
    manager = BrokerSymbolManager()
    
    # Update all registered brokers
    for broker_name in manager.broker_configs.keys():
        success = manager.update_broker_symbols(broker_name)
        print(f"Updated {broker_name}: {'✅' if success else '❌'}")
    
    # Create new master contract
    manager.create_broker_master_contract()

# Schedule at 6 AM daily
schedule.every().day.at("06:00").do(daily_symbol_update)

# Run scheduler
while True:
    schedule.run_pending()
    time.sleep(60)
```

### **Config-Triggered Updates**
```python
# When adding new broker configuration
def add_broker_config(broker_name, config):
    # Add to your config system
    save_broker_config(broker_name, config)
    
    # Automatically register and download symbols
    manager = BrokerSymbolManager()
    success = manager.register_broker_config(broker_name, config)
    
    if success:
        print(f"✅ {broker_name} configured and symbols downloaded")
    else:
        print(f"❌ {broker_name} configuration failed")
    
    return success
```

---

## 🧪 Testing Integration

### **Unit Tests**
```python
import unittest
from data.symbol_manager import BrokerSymbolManager

class TestSymbolManager(unittest.TestCase):
    def setUp(self):
        self.manager = BrokerSymbolManager()
    
    def test_broker_registration(self):
        config = {"client_id": "test", "dry_run": True}
        success = self.manager.register_broker_config("test_broker", config)
        self.assertTrue(success)
    
    def test_symbol_mapping(self):
        # Assuming broker is registered
        mapping = self.manager.get_broker_symbol_mapping("RELIANCE", "fyers")
        self.assertIsNotNone(mapping)
    
    def test_symbol_search(self):
        results = self.manager.search_broker_symbols("RELIANCE")
        self.assertGreater(len(results), 0)
```

### **Integration Tests**
```python
def test_end_to_end_workflow():
    manager = BrokerSymbolManager()
    
    # 1. Register broker
    config = {"client_id": "test", "dry_run": True}
    assert manager.register_broker_config("fyers", config)
    
    # 2. Search symbols
    results = manager.search_broker_symbols("RELIANCE")
    assert len(results) > 0
    
    # 3. Get mapping
    mapping = manager.get_broker_symbol_mapping("RELIANCE", "fyers")
    assert mapping is not None
    
    # 4. Create master contract
    assert manager.create_broker_master_contract()
    
    print("✅ End-to-end workflow successful")
```

---

## 🚨 Error Handling

### **Robust Error Handling**
```python
class SafeSymbolManager:
    def __init__(self):
        self.manager = BrokerSymbolManager()
    
    def safe_get_mapping(self, symbol, broker):
        try:
            mapping = self.manager.get_broker_symbol_mapping(symbol, broker)
            if mapping:
                return {"success": True, "symbol": mapping}
            else:
                return {"success": False, "error": "Symbol not found"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def safe_register_broker(self, broker_name, config):
        try:
            success = self.manager.register_broker_config(broker_name, config)
            return {"success": success}
        except Exception as e:
            return {"success": False, "error": str(e)}
```

---

## 📚 Best Practices

1. **Always validate symbols** before trading
2. **Handle broker-specific formats** appropriately
3. **Use fallback mechanisms** for symbol resolution
4. **Monitor symbol update status** regularly
5. **Cache frequently used mappings** for performance
6. **Log all symbol operations** for debugging
7. **Test with multiple brokers** before production

---

## 🎯 Ready to Integrate!

The broker symbol management system is designed for easy integration with any trading system. Follow these patterns and your application will have robust, scalable symbol management across all Indian brokers! 🚀
