#!/usr/bin/env python3
"""
Fyers Broker Usage Example

This example demonstrates how to use the Fyers broker with credentials
stored in the .env file for both trading operations and real-time data streaming.

Prerequisites:
1. Set up your Fyers credentials in .env file
2. Generate access token using: python scripts/fyers_auth.py --generate-token
3. Ensure fyers-apiv3 package is installed: pip install fyers-apiv3
"""

import os
import sys
import time
from datetime import datetime

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from broker import FyersBroker


def main():
    """Main example function."""
    print("Fyers Broker Usage Example")
    print("=" * 40)
    
    # 1. Initialize Fyers Broker
    # Credentials are automatically loaded from .env file
    print("\n1. Initializing Fyers Broker...")
    broker = FyersBroker()
    
    if not broker.client_id or not broker.access_token:
        print("❌ Error: Fyers credentials not found in .env file")
        print("Please run: python scripts/fyers_auth.py --generate-token")
        return
    
    print(f"✅ Broker initialized successfully")
    print(f"   Client ID: {broker.client_id}")
    print(f"   Dry Run Mode: {broker.is_dry_run()}")
    print(f"   Live Data Enabled: {broker.is_live_data_enabled()}")
    
    # 2. Get User Profile
    print("\n2. Getting User Profile...")
    profile = broker.get_profile()
    
    if profile.get('s') == 'ok':
        data = profile.get('data', {})
        print(f"✅ Profile retrieved successfully")
        print(f"   Name: {data.get('name', 'N/A')}")
        print(f"   User ID: {data.get('fy_id', 'N/A')}")
        print(f"   Email: {data.get('email_id', 'N/A')}")
    else:
        print(f"❌ Failed to get profile: {profile.get('message', 'Unknown error')}")
    
    # 3. Get Account Information
    print("\n3. Getting Account Information...")
    
    # Get funds
    funds = broker.get_funds()
    if funds.get('s') == 'ok':
        print("✅ Funds information retrieved")
        # Handle different response formats
        data = funds.get('fund_limit', funds.get('data', {}))
        if isinstance(data, list) and data:
            data = data[0]
        print(f"   Available Cash: ₹{data.get('availablecash', 'N/A')}")
    
    # Get positions
    positions = broker.get_positions()
    if positions.get('s') == 'ok':
        pos_data = positions.get('netPositions', [])
        print(f"✅ Positions retrieved: {len(pos_data)} positions")
    
    # 4. Place a Dry Run Order
    print("\n4. Placing a Dry Run Order...")
    
    order_response = broker.place_order(
        symbol="NSE:SBIN-EQ",
        qty=1,
        side=1,  # BUY
        type=2,  # MARKET
        productType="CNC"
    )
    
    if order_response.get('s') == 'ok' or 'DRY' in str(order_response.get('id', '')):
        print("✅ Dry run order placed successfully")
        print(f"   Order ID: {order_response.get('id', 'N/A')}")
        print(f"   Message: {order_response.get('message', 'N/A')}")
    else:
        print(f"❌ Failed to place order: {order_response.get('message', 'Unknown error')}")
    
    # 5. Enable Real-time Data Streaming
    print("\n5. Setting up Real-time Data Streaming...")
    
    if broker.enable_websocket():
        print("✅ WebSocket enabled successfully")
        
        # Define callback functions for real-time data
        def on_quote_update(symbol, data):
            ltp = data.get('ltp', 'N/A')
            change = data.get('ch', 'N/A')
            volume = data.get('vol_traded_today', 'N/A')
            print(f"📈 {symbol}: LTP=₹{ltp}, Change={change}, Volume={volume}")
        
        def on_depth_update(symbol, data):
            bid = data.get('bid', [{}])[0].get('price', 'N/A') if data.get('bid') else 'N/A'
            ask = data.get('ask', [{}])[0].get('price', 'N/A') if data.get('ask') else 'N/A'
            print(f"📊 {symbol}: Bid=₹{bid}, Ask=₹{ask}")
        
        def on_order_update(data):
            order_id = data.get('id', 'N/A')
            status = data.get('status', 'N/A')
            print(f"📋 Order Update: {order_id} - Status: {status}")
        
        # Add callbacks
        broker.add_quote_callback(on_quote_update)
        broker.add_depth_callback(on_depth_update)
        broker.add_order_callback(on_order_update)
        
        # Subscribe to symbols
        symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ"]
        
        print(f"\n6. Subscribing to live data for: {symbols}")
        
        # Subscribe to live quotes
        if broker.subscribe_live_quotes(symbols):
            print("✅ Subscribed to live quotes")
        
        # Subscribe to market depth for one symbol
        if broker.subscribe_market_depth(symbols[:1]):
            print("✅ Subscribed to market depth")
        
        # Show subscribed symbols
        subscribed = broker.get_subscribed_symbols()
        print(f"   Subscribed symbols: {subscribed}")
        
        # 7. Monitor Live Data
        print("\n7. Monitoring live data for 20 seconds...")
        print("   (You should see real-time updates below)")
        
        start_time = time.time()
        while time.time() - start_time < 20:
            time.sleep(1)
            
            # Show statistics every 5 seconds
            elapsed = int(time.time() - start_time)
            if elapsed % 5 == 0:
                stats = broker.get_websocket_statistics()
                print(f"   [{elapsed}s] Stats: {stats.get('quotes_processed', 0)} quotes, "
                      f"{stats.get('depths_processed', 0)} depths processed")
        
        print("\n✅ Live data monitoring completed")
        
        # 8. Access Cached Live Data
        print("\n8. Accessing cached live data...")
        
        for symbol in symbols[:2]:
            # Get latest quote from cache
            quote = broker.get_live_quote(symbol)
            if quote:
                print(f"   {symbol}: Latest LTP = ₹{quote.get('ltp', 'N/A')}")
            
            # Get latest depth from cache
            depth = broker.get_live_depth(symbol)
            if depth:
                bid = depth.get('bid', [{}])[0].get('price', 'N/A') if depth.get('bid') else 'N/A'
                ask = depth.get('ask', [{}])[0].get('price', 'N/A') if depth.get('ask') else 'N/A'
                print(f"   {symbol}: Bid=₹{bid}, Ask=₹{ask}")
        
        # 9. WebSocket Statistics
        print("\n9. WebSocket Statistics:")
        stats = broker.get_websocket_statistics()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # Cleanup
        broker.disable_websocket()
        print("\n✅ WebSocket disabled")
        
    else:
        print("❌ Failed to enable WebSocket")
    
    print("\n" + "=" * 40)
    print("Example completed successfully! 🎉")
    print("\nKey Features Demonstrated:")
    print("✅ Automatic credential loading from .env file")
    print("✅ User profile and account information access")
    print("✅ Dry run order placement")
    print("✅ Real-time data streaming via WebSocket")
    print("✅ Live quote and market depth subscriptions")
    print("✅ Event-driven callbacks for market data")
    print("✅ Cached data access for quick retrieval")
    
    print("\nNext Steps:")
    print("1. Disable dry run mode in .env to place real orders")
    print("2. Add more symbols to your watchlist")
    print("3. Implement trading strategies using live data")
    print("4. Set up alerts based on price movements")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExample interrupted by user")
    except Exception as e:
        print(f"\nError running example: {e}")
        import traceback
        traceback.print_exc()
