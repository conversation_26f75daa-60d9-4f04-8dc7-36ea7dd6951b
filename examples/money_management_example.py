#!/usr/bin/env python3
"""
Money Management Module Usage Example

This example demonstrates how to use the standalone money management module
in your own trading systems or external applications.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from money_management import MoneyManager, get_money_manager
from money_management.interfaces import SimpleBalanceProvider, BrokerAdapter


def example_1_basic_usage():
    """Example 1: Basic usage with simple balance provider"""
    print("=== Example 1: Basic Usage ===")

    # Create a simple balance provider
    balance_provider = SimpleBalanceProvider(
        available_balance=100000.0,
        total_balance=120000.0,
        used_margin=20000.0
    )

    # Initialize money manager
    money_manager = MoneyManager(broker=balance_provider)

    # Add strategy allocations
    money_manager.add_strategy_allocation("momentum_strategy", 30.0)
    money_manager.add_strategy_allocation("swing_strategy", 25.0)
    money_manager.add_strategy_allocation("scalping_strategy", 20.0)

    # Get allocation summary
    summary = money_manager.get_allocation_summary()
    print(f"Total Allocated: ₹{summary['total_allocated_amount']:,.2f}")
    print(f"Unallocated: ₹{summary['unallocated_amount']:,.2f}")

    # Calculate position size
    position_info = money_manager.get_position_size("momentum_strategy", "RELIANCE", 2500.0)
    print(f"Recommended quantity for RELIANCE: {position_info['quantity']}")
    print(f"Position value: ₹{position_info['position_value']:,.2f}")
    print()


def example_2_custom_broker():
    """Example 2: Using with a custom broker class"""
    print("=== Example 2: Custom Broker Integration ===")

    # Example custom broker class
    class MyCustomBroker:
        def __init__(self):
            self.balance = 150000.0
            self.used_margin = 30000.0

        def get_fund_limit(self):
            return {
                "totalBalance": self.balance + self.used_margin,
                "availableBalance": self.balance,
                "utilizedAmount": self.used_margin,
                "withdrawableBalance": self.balance - 5000,
                "blockedPayoutAmount": 5000.0
            }

        def get_holdings(self):
            return [
                {"symbol": "RELIANCE", "quantity": 10, "avgCostPrice": 2400, "currentPrice": 2500},
                {"symbol": "TCS", "quantity": 5, "avgCostPrice": 3200, "currentPrice": 3300}
            ]

        def get_positions(self):
            return [
                {"symbol": "INFY", "quantity": 8, "avgPrice": 1800, "unrealizedPnl": 400}
            ]

        def is_dry_run(self):
            return True

    # Initialize with custom broker
    custom_broker = MyCustomBroker()
    money_manager = MoneyManager(broker=custom_broker)

    # The money manager automatically adapts the broker
    balance_info = money_manager.get_available_balance()
    print(f"Available Balance: ₹{balance_info['available_balance']:,.2f}")

    # Add allocations and calculate positions
    success = money_manager.add_strategy_allocation("custom_strategy", 40.0)
    if success:
        position_info = money_manager.get_position_size("custom_strategy", "HDFC", 1500.0)
        if "error" not in position_info:
            print(f"Position size for HDFC: {position_info['quantity']} shares")
        else:
            print(f"Error calculating position: {position_info['error']}")
    else:
        print("Failed to add strategy allocation")
    print()


def example_3_risk_management():
    """Example 3: Risk management and validation"""
    print("=== Example 3: Risk Management ===")

    # Initialize money manager with force_new to avoid conflicts
    money_manager = get_money_manager(
        broker=SimpleBalanceProvider(200000.0, 250000.0, 50000.0),
        force_new=True
    )

    # Configure risk settings
    money_manager.update_global_settings({
        "max_risk_per_trade": 1.5,
        "max_total_risk": 10.0,
        "stop_loss_global": 15.0
    })

    # Add strategy allocation
    money_manager.add_strategy_allocation("conservative_strategy", 30.0)

    # Create some test positions
    test_positions = [
        {
            "symbol": "RELIANCE",
            "strategy": "conservative_strategy",
            "position_value": 25000.0,
            "risk_amount": 1250.0
        },
        {
            "symbol": "TCS",
            "strategy": "conservative_strategy",
            "position_value": 20000.0,
            "risk_amount": 1000.0
        }
    ]

    # Check risk limits
    risk_check = money_manager.check_risk_limits(test_positions)
    print(f"Within risk limits: {risk_check['within_limits']}")
    if risk_check.get('violations'):
        print("Risk violations:")
        for violation in risk_check['violations']:
            print(f"  - {violation}")

    print(f"Total portfolio risk: {risk_check['portfolio_risk']['portfolio_risk_percentage']:.2f}%")
    print()


def example_4_multiple_sizing_methods():
    """Example 4: Different position sizing methods"""
    print("=== Example 4: Position Sizing Methods ===")

    money_manager = MoneyManager(broker=SimpleBalanceProvider(100000.0), config_path="examples/test_config_4.json")
    money_manager.add_strategy_allocation("test_strategy", 50.0)

    symbol = "INFY"
    price = 1800.0

    # Test different sizing methods
    methods = ["percentage", "risk_based", "equal_weight", "kelly"]

    for method in methods:
        position_info = money_manager.get_position_size(
            "test_strategy", symbol, price, sizing_method=method
        )

        if "error" not in position_info:
            print(f"{method.title()} sizing: {position_info['quantity']} shares "
                  f"(₹{position_info['position_value']:,.2f})")
        else:
            print(f"{method.title()} sizing: Error - {position_info['error']}")

    print()


def example_5_configuration_management():
    """Example 5: Configuration management"""
    print("=== Example 5: Configuration Management ===")

    # Initialize with custom config path
    money_manager = MoneyManager(
        broker=SimpleBalanceProvider(75000.0),
        config_path="examples/custom_money_config.json"
    )

    # Update configuration
    money_manager.config_manager.set_setting("global_settings.total_capital_usage", 85.0)
    money_manager.config_manager.set_setting("risk_management.single_stock_limit", 15.0)

    # Get specific settings
    capital_usage = money_manager.config_manager.get_setting("global_settings.total_capital_usage")
    print(f"Capital usage setting: {capital_usage}%")

    # Export configuration
    money_manager.config_manager.export_config("examples/exported_config.json")
    print("Configuration exported to examples/exported_config.json")
    print()


def example_6_integration_with_external_system():
    """Example 6: Integration with external trading system"""
    print("=== Example 6: External System Integration ===")

    # Simulate an external trading system
    class ExternalTradingSystem:
        def __init__(self):
            self.money_manager = MoneyManager(
                broker=SimpleBalanceProvider(300000.0, 350000.0, 50000.0),
                config_path="examples/external_system_config.json"
            )
            self.setup_strategies()

        def setup_strategies(self):
            """Setup strategy allocations"""
            strategies = {
                "trend_following": 35.0,
                "mean_reversion": 25.0,
                "momentum": 20.0,
                "arbitrage": 15.0
            }

            for strategy, allocation in strategies.items():
                self.money_manager.add_strategy_allocation(strategy, allocation)

        def calculate_trade_size(self, strategy: str, symbol: str, price: float):
            """Calculate trade size for a signal"""
            position_info = self.money_manager.get_position_size(strategy, symbol, price)

            if "error" not in position_info:
                return {
                    "symbol": symbol,
                    "quantity": position_info["quantity"],
                    "value": position_info["position_value"],
                    "risk": position_info["risk_amount"],
                    "stop_loss": position_info.get("stop_loss_price", price * 0.95)
                }
            else:
                return {"error": position_info["error"]}

        def get_portfolio_status(self):
            """Get current portfolio status"""
            return self.money_manager.get_allocation_summary()

    # Use the external system
    trading_system = ExternalTradingSystem()

    # Calculate trade sizes for different signals
    signals = [
        ("trend_following", "RELIANCE", 2600.0),
        ("mean_reversion", "HDFC", 1650.0),
        ("momentum", "INFY", 1850.0)
    ]

    print("Trade sizing for signals:")
    for strategy, symbol, price in signals:
        trade_info = trading_system.calculate_trade_size(strategy, symbol, price)
        if "error" not in trade_info:
            print(f"  {symbol}: {trade_info['quantity']} shares @ ₹{price} "
                  f"(Risk: ₹{trade_info['risk']:.2f})")
        else:
            print(f"  {symbol}: Error - {trade_info['error']}")

    # Get portfolio status
    status = trading_system.get_portfolio_status()
    print(f"\nPortfolio Status:")
    print(f"  Total Capital: ₹{status['total_capital']:,.2f}")
    print(f"  Allocated: ₹{status['total_allocated_amount']:,.2f} ({status['total_allocated_percentage']:.1f}%)")
    print(f"  Available: ₹{status['unallocated_amount']:,.2f}")


def main():
    """Run all examples"""
    print("Money Management Module Examples")
    print("=" * 50)
    print()

    try:
        example_1_basic_usage()
        example_2_custom_broker()
        example_3_risk_management()
        example_4_multiple_sizing_methods()
        example_5_configuration_management()
        example_6_integration_with_external_system()

        print("\n" + "=" * 50)
        print("All examples completed successfully!")

    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
