# Multi-stage Alpine-based Dockerfile for RapidTrader
# Optimized for production with minimal size and security

# Build stage
FROM python:3.11-alpine AS builder

LABEL maintainer="RapidTrader <<EMAIL>>"

# Install build dependencies
RUN apk add --no-cache \
    build-base \
    libffi-dev \
    openssl-dev \
    musl-dev \
    linux-headers \
    git \
    curl \
    && rm -rf /var/cache/apk/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt /tmp/
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r /tmp/requirements.txt

# Production stage
FROM python:3.11-alpine AS production

# Set up environment
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    RAPIDTRADER_ENV=production

# Install runtime dependencies only
RUN apk add --no-cache \
    libffi \
    openssl \
    ca-certificates \
    tzdata \
    curl \
    && rm -rf /var/cache/apk/*

# Create rapidtrader user with specific UID/GID for security
RUN addgroup -g 1000 rapidtrader \
    && adduser -D -u 1000 -G rapidtrader -s /bin/sh rapidtrader

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Create directories and set permissions
RUN mkdir -p /rapidtrader /rapidtrader/userdata /rapidtrader/logs \
    && chown -R rapidtrader:rapidtrader /rapidtrader

WORKDIR /rapidtrader

# Copy application code with proper ownership
COPY --chown=rapidtrader:rapidtrader . /rapidtrader/

# Make scripts executable
RUN chmod +x /rapidtrader/rapidtrader \
    && mkdir -p /rapidtrader/scripts \
    && chmod +x /rapidtrader/scripts/*.sh 2>/dev/null || true

# Switch to rapidtrader user
USER rapidtrader

# Create symbolic link for easy access
RUN mkdir -p /home/<USER>/.local/bin \
    && ln -sf /rapidtrader/rapidtrader /home/<USER>/.local/bin/rapidtrader

# Set up volumes for data persistence
VOLUME ["/rapidtrader/userdata", "/rapidtrader/logs"]

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# Set entrypoint
ENTRYPOINT ["/rapidtrader/scripts/entrypoint.sh"]

# Default command
CMD ["help"]
