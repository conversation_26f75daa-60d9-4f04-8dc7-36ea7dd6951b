FROM python:3.11-slim-bullseye

LABEL maintainer="RapidTrader <<EMAIL>>"

# Set up environment
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies including TA-Lib
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
        libatlas-base-dev \
        libffi-dev \
        libssl-dev \
        wget \
        ca-certificates \
        unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Note: TA-Lib installation removed - using pandas_ta instead for technical analysis

# Create rapidtrader user
RUN useradd -m -u 1000 -s /bin/bash rapidtrader

# Create directories and set permissions
RUN mkdir -p /rapidtrader /rapidtrader/userdata \
    && chown -R rapidtrader:rapidtrader /rapidtrader

WORKDIR /rapidtrader

# Copy requirements first to leverage Docker cache
COPY --chown=rapidtrader:rapidtrader requirements.txt /rapidtrader/

# Install Python dependencies
RUN pip install --upgrade pip \
    && pip install -r requirements.txt

# Copy application code
COPY --chown=rapidtrader:rapidtrader . /rapidtrader/

# Make scripts executable
RUN chmod +x /rapidtrader/rapidtrader \
    && mkdir -p /rapidtrader/scripts \
    && chmod +x /rapidtrader/scripts/*.sh 2>/dev/null || true

# Switch to rapidtrader user
USER rapidtrader

# Create symbolic link for easy access
RUN mkdir -p /home/<USER>/.local/bin \
    && ln -sf /rapidtrader/rapidtrader /home/<USER>/.local/bin/rapidtrader \
    && export PATH="/home/<USER>/.local/bin:$PATH"

# Set up volumes
VOLUME ["/rapidtrader/userdata"]

# Set entrypoint
ENTRYPOINT ["/rapidtrader/scripts/entrypoint.sh"]

# Default command
CMD ["help"]