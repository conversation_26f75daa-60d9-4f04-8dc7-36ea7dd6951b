

services:
  # Base service with shared configuration
  rapidtrader-base: &rapidtrader-base
    build:
      context: .
      dockerfile: Dockerfile
    image: rapidtrader:latest
    restart: unless-stopped
    volumes:
      - ./userdata:/rapidtrader/userdata
    env_file:
      - .env
    networks:
      - rapidtrader-network

  # Backtesting service (auto-stops when complete)
  backtest:
    <<: *rapidtrader-base
    container_name: rapidtrader-backtest
    command: backtest
    profiles:
      - backtest
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/backtest-config.json
    restart: "no"  # Don't restart when backtest completes

  # Enhanced Backtesting service with dynamic naming and auto data download
  enhanced-backtest:
    <<: *rapidtrader-base
    container_name: ${BACKTEST_CONTAINER_NAME:-rapidtrader-enhanced-backtest}
    command: >
      sh -c "
      echo '🚀 Starting Enhanced Backtest';
      echo 'Strategy: ${STRATEGY:-DefaultStrategy}';
      echo 'Config: ${CONFIG:-backtest-config}';
      echo 'Symbols: ${SYMBOLS:-R<PERSON><PERSON>NC<PERSON>,TCS}';
      echo 'Timeframe: ${TIMEFRAME:-1d}';
      echo '📊 Downloading/updating data...';
      python -m core.rapidtrader data download --symbols ${SYMBOLS:-REL<PERSON>NCE,TCS} --timeframe ${TIMEFRAME:-1d} --update || echo 'Data download completed with warnings';
      echo '🏃 Running backtest...';
      python -m core.rapidtrader backtest run -c ${CONFIG:-backtest-config} -s ${STRATEGY:-DefaultStrategy} ${TIMERANGE:+--timerange} ${TIMERANGE} ${TIMEFRAME:+-t} ${TIMEFRAME};
      echo '💾 Storing results...';
      echo '✅ Enhanced backtest completed - container will auto-stop';
      "
    profiles:
      - enhanced-backtest
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/${CONFIG:-backtest-config}.json
      - STRATEGY=${STRATEGY:-DefaultStrategy}
      - CONFIG=${CONFIG:-backtest-config}
      - SYMBOLS=${SYMBOLS:-RELIANCE,TCS}
      - TIMEFRAME=${TIMEFRAME:-1d}
      - TIMERANGE=${TIMERANGE:-}
    restart: "no"  # Don't restart when backtest completes

  # Dry-run service (broker-based)
  dryrun:
    <<: *rapidtrader-base
    container_name: rapidtrader-dryrun
    command: dryrun
    profiles:
      - dryrun
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dryrun-config.json

  # Paper trading service (independent)
  paper-trade:
    <<: *rapidtrader-base
    container_name: rapidtrader-paper-trade
    command: paper-trade
    profiles:
      - paper-trade
      - paper
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dry_run_config.json
    ports:
      - "8081:8081"  # Optional: for future web interface

  # Live trading service
  live:
    <<: *rapidtrader-base
    container_name: rapidtrader-live
    command: live
    profiles:
      - live
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/live-config.json

  # Optimization service (auto-stops when complete)
  optimize:
    <<: *rapidtrader-base
    container_name: rapidtrader-optimize
    command: optimize
    profiles:
      - optimize
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/optimize-config.json
    restart: "no"  # Don't restart when optimization completes

# Data download service removed as requested

  # Interactive shell for development
  shell:
    <<: *rapidtrader-base
    container_name: rapidtrader-shell
    command: shell
    profiles:
      - shell
    stdin_open: true
    tty: true

  # Web interface (RapidUI)
  web:
    <<: *rapidtrader-base
    container_name: rapidtrader-web
    entrypoint: ["python", "/rapidtrader/ui_module/rapidui.py", "--host", "0.0.0.0", "--port", "8080"]
    command: []
    profiles:
      - web
      - ui
    ports:
      - "8080:8080"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=false

  # Fyers trading service (dry-run)
  fyers-dryrun:
    <<: *rapidtrader-base
    container_name: rapidtrader-fyers-dryrun
    command: trade dryrun -c fyers-config.json
    profiles:
      - fyers-dryrun
      - fyers
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/configs/fyers-config.json

  # Fyers trading service (live)
  fyers-live:
    <<: *rapidtrader-base
    container_name: rapidtrader-fyers-live
    command: trade start -c fyers-live-config.json
    profiles:
      - fyers-live
      - fyers
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/configs/fyers-live-config.json

  # Combined web + trading service
  fyers-web-trading:
    <<: *rapidtrader-base
    container_name: rapidtrader-fyers-web-trading
    entrypoint: ["sh", "-c", "python /rapidtrader/ui_module/rapidui.py --host 0.0.0.0 --port 8080"]
    command: []
    profiles:
      - fyers-web-trading
      - fyers-complete
    ports:
      - "8080:8080"
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/configs/fyers-config.json
      - FLASK_ENV=production
      - FLASK_DEBUG=false

  # Market Hours Manager - automatically pauses/resumes containers based on market hours
  market-manager:
    <<: *rapidtrader-base
    container_name: rapidtrader-market-manager
    command: python scripts/docker_market_manager.py --exchange NSE --containers rapidtrader-fyers-dryrun rapidtrader-fyers-live rapidtrader-dhan-dryrun rapidtrader-dhan-live
    profiles:
      - market-manager
      - auto-trading
    volumes:
      - ./userdata:/rapidtrader/userdata
      - /var/run/docker.sock:/var/run/docker.sock  # Access to Docker daemon
    environment:
      - EXCHANGE=NSE
    restart: unless-stopped
    depends_on:
      - fyers-dryrun
      - fyers-live

networks:
  rapidtrader-network:
    driver: bridge