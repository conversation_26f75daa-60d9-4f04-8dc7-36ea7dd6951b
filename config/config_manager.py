"""
Configuration Manager for RapidTrader

This module handles loading, validating, and managing configuration for the RapidTrader engine.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import yaml
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger("config_manager")

# Load environment variables from .env file
load_dotenv()

class ConfigManager:
    """
    Configuration manager for RapidTrader.
    
    This class handles loading, validating, and managing configuration for the RapidTrader engine.
    """
    
    DEFAULT_CONFIG_PATH = "config/config.yml"
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to the configuration file (default: config/config.yml)
        """
        self.config_path = config_path or self.DEFAULT_CONFIG_PATH
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from a YAML file.
        
        Returns:
            Configuration as a dictionary
        """
        try:
            with open(self.config_path, "r") as f:
                config = yaml.safe_load(f)
            return config or {}
        except FileNotFoundError:
            logger.warning(f"Configuration file not found: {self.config_path}")
            return {}
        except yaml.YAMLError as e:
            logger.error(f"Error parsing configuration file: {e}")
            return {}
            
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.
        
        Args:
            key: Configuration key (can be nested using dot notation, e.g., 'broker.dhan.client_id')
            default: Default value to return if the key is not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split(".")
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
        
    def set(self, key: str, value: Any) -> None:
        """
        Set a configuration value.
        
        Args:
            key: Configuration key (can be nested using dot notation, e.g., 'broker.dhan.client_id')
            value: Value to set
        """
        keys = key.split(".")
        config = self.config
        
        for i, k in enumerate(keys[:-1]):
            if k not in config:
                config[k] = {}
            config = config[k]
                
        config[keys[-1]] = value
        
    def save(self, config_path: Optional[str] = None) -> None:
        """
        Save the configuration to a YAML file.
        
        Args:
            config_path: Path to save the configuration file (default: self.config_path)
        """
        config_path = config_path or self.config_path
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            with open(config_path, "w") as f:
                yaml.dump(self.config, f, default_flow_style=False)
                
            logger.info(f"Configuration saved to {config_path}")
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            
    def get_env(self, key: str, default: Any = None) -> Any:
        """
        Get a value from environment variables.
        
        Args:
            key: Environment variable name
            default: Default value to return if the environment variable is not set
            
        Returns:
            Environment variable value or default
        """
        return os.environ.get(key, default)
        
    def validate(self) -> bool:
        """
        Validate the configuration.
        
        Returns:
            True if the configuration is valid, False otherwise
        """
        # TODO: Implement configuration validation
        return True
