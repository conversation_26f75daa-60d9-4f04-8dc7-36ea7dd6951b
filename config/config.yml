# RapidTrader Configuration

# Trading mode: live, dryrun, backtest
mode: dryrun

# Exchange configuration
exchange:
  name: dhan  # Exchange name (dhan, zerodha, etc.)
  credentials:
    # Credentials are loaded from .env file
    # You can override them here if needed
    client_id: null
    access_token: null

# Trading parameters
trading:
  # Default trading parameters
  default_timeframe: 1d
  max_open_trades: 3
  stake_amount: 1000
  stake_currency: INR

# Backtesting parameters
backtest:
  timerange: null  # Format: YYYYMMDD-YYYYMMDD
  timeframe: 1d
  data_dir: userdata/historical_data

# Strategy parameters
strategy:
  name: ta_sma_crossover
  params:
    short_window: 20
    long_window: 50

# Data parameters
data:
  # Default data parameters
  default_timeframe: 1d
  default_exchange: dhan
  data_dir: userdata/historical_data

# Logging parameters
logging:
  level: INFO
  file: userdata/logs/rapidtrader.log