#!/usr/bin/env python3
"""
RapidTrader API Integration Demo

This script demonstrates the complete API integration including:
- API Gateway startup and configuration
- Frontend-API communication
- Broker integration and symbol mapping
- Real-time data streaming
- Container management
- Authentication and security
"""

import os
import sys
import json
import time
import asyncio
import logging
import requests
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("api_demo")

class RapidTraderAPIDemo:
    """Comprehensive API Integration Demo"""
    
    def __init__(self):
        """Initialize the demo"""
        self.api_base_url = "http://localhost:8000"
        self.api_key = "rt_demo_key_12345"
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
        
        logger.info("RapidTrader API Demo initialized")
    
    def test_api_health(self) -> bool:
        """Test API Gateway health"""
        try:
            logger.info("Testing API Gateway health...")
            response = self.session.get(f"{self.api_base_url}/health")
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info(f"API Gateway is healthy: {health_data}")
                return True
            else:
                logger.error(f"API Gateway health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing API health: {e}")
            return False
    
    def test_authentication(self) -> bool:
        """Test API authentication"""
        try:
            logger.info("Testing API authentication...")
            
            # Test with valid API key
            response = self.session.get(f"{self.api_base_url}/api/v1/profile")
            
            if response.status_code == 200:
                logger.info("Authentication successful")
                return True
            elif response.status_code == 401:
                logger.warning("Authentication failed - API key may be invalid")
                return False
            else:
                logger.error(f"Unexpected response: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing authentication: {e}")
            return False
    
    def test_broker_management(self) -> bool:
        """Test broker management endpoints"""
        try:
            logger.info("Testing broker management...")
            
            # List brokers
            response = self.session.get(f"{self.api_base_url}/brokers")
            
            if response.status_code == 200:
                brokers = response.json()
                logger.info(f"Available brokers: {brokers}")
                
                # Test adding a demo broker
                demo_broker_config = {
                    "broker_name": "fyers",
                    "credentials": {
                        "client_id": "demo_client",
                        "access_token": "demo_token"
                    },
                    "dry_run": True
                }
                
                response = self.session.post(
                    f"{self.api_base_url}/brokers/demo_fyers",
                    json=demo_broker_config
                )
                
                if response.status_code in [200, 201]:
                    logger.info("Demo broker added successfully")
                    return True
                else:
                    logger.warning(f"Failed to add demo broker: {response.status_code}")
                    return False
            else:
                logger.error(f"Failed to list brokers: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing broker management: {e}")
            return False
    
    def test_symbol_operations(self) -> bool:
        """Test symbol-related operations"""
        try:
            logger.info("Testing symbol operations...")
            
            # Test symbol search
            response = self.session.get(
                f"{self.api_base_url}/api/v1/symbols/search",
                params={"query": "RELIANCE", "limit": 5}
            )
            
            if response.status_code == 200:
                symbols = response.json()
                logger.info(f"Symbol search results: {len(symbols.get('symbols', []))} found")
                
                # Test symbol info
                if symbols.get('symbols'):
                    symbol = symbols['symbols'][0]['symbol']
                    response = self.session.get(
                        f"{self.api_base_url}/api/v1/symbols/{symbol}"
                    )
                    
                    if response.status_code == 200:
                        symbol_info = response.json()
                        logger.info(f"Symbol info retrieved for {symbol}")
                        return True
                    else:
                        logger.warning(f"Failed to get symbol info: {response.status_code}")
                        return False
                else:
                    logger.warning("No symbols found in search")
                    return False
            else:
                logger.error(f"Symbol search failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing symbol operations: {e}")
            return False
    
    def test_trading_operations(self) -> bool:
        """Test trading operations (dry run)"""
        try:
            logger.info("Testing trading operations...")
            
            # Test order placement (dry run)
            order_data = {
                "symbol": "RELIANCE-EQ",
                "quantity": 1,
                "side": "BUY",
                "order_type": "MARKET",
                "product": "MIS"
            }
            
            response = self.session.post(
                f"{self.api_base_url}/api/v1/placeorder",
                json=order_data,
                params={"broker_id": "demo_fyers"}
            )
            
            if response.status_code in [200, 201]:
                order_response = response.json()
                logger.info(f"Order placed successfully: {order_response}")
                
                # Test order book
                response = self.session.get(
                    f"{self.api_base_url}/api/v1/orderbook",
                    params={"broker_id": "demo_fyers"}
                )
                
                if response.status_code == 200:
                    orderbook = response.json()
                    logger.info(f"Order book retrieved: {len(orderbook.get('orders', []))} orders")
                    return True
                else:
                    logger.warning(f"Failed to get order book: {response.status_code}")
                    return False
            else:
                logger.warning(f"Order placement failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing trading operations: {e}")
            return False
    
    def test_container_management(self) -> bool:
        """Test container management"""
        try:
            logger.info("Testing container management...")
            
            # List containers
            response = self.session.get(f"{self.api_base_url}/containers")
            
            if response.status_code == 200:
                containers = response.json()
                logger.info(f"Active containers: {len(containers.get('containers', []))}")
                
                # Test container creation (dry run)
                container_config = {
                    "action": "start",
                    "container_type": "backtest",
                    "strategy": "TestStrategy",
                    "symbols": ["RELIANCE", "TCS"],
                    "config": {
                        "timeframe": "1d",
                        "start_date": "2024-01-01",
                        "end_date": "2024-06-30"
                    }
                }
                
                response = self.session.post(
                    f"{self.api_base_url}/containers/manage",
                    json=container_config
                )
                
                if response.status_code in [200, 201]:
                    container_response = response.json()
                    logger.info(f"Container operation successful: {container_response}")
                    return True
                else:
                    logger.warning(f"Container operation failed: {response.status_code}")
                    return False
            else:
                logger.error(f"Failed to list containers: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing container management: {e}")
            return False
    
    def test_data_operations(self) -> bool:
        """Test data-related operations"""
        try:
            logger.info("Testing data operations...")
            
            # Test market quotes
            response = self.session.get(
                f"{self.api_base_url}/api/v1/quotes",
                params={
                    "symbols": "RELIANCE,TCS,INFY",
                    "broker_id": "demo_fyers"
                }
            )
            
            if response.status_code == 200:
                quotes = response.json()
                logger.info(f"Market quotes retrieved: {len(quotes.get('quotes', []))} quotes")
                
                # Test historical data
                response = self.session.get(
                    f"{self.api_base_url}/api/v1/historical",
                    params={
                        "symbol": "RELIANCE",
                        "timeframe": "1d",
                        "days": 30,
                        "broker_id": "demo_fyers"
                    }
                )
                
                if response.status_code == 200:
                    historical_data = response.json()
                    logger.info(f"Historical data retrieved: {len(historical_data.get('data', []))} records")
                    return True
                else:
                    logger.warning(f"Failed to get historical data: {response.status_code}")
                    return False
            else:
                logger.warning(f"Failed to get quotes: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error testing data operations: {e}")
            return False
    
    def run_comprehensive_demo(self) -> Dict[str, bool]:
        """Run comprehensive API integration demo"""
        logger.info("🚀 Starting RapidTrader API Integration Demo")
        
        results = {}
        
        # Test sequence
        tests = [
            ("API Health", self.test_api_health),
            ("Authentication", self.test_authentication),
            ("Broker Management", self.test_broker_management),
            ("Symbol Operations", self.test_symbol_operations),
            ("Trading Operations", self.test_trading_operations),
            ("Container Management", self.test_container_management),
            ("Data Operations", self.test_data_operations),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Testing: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = test_func()
                results[test_name] = result
                
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.warning(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                logger.error(f"💥 {test_name}: ERROR - {e}")
                results[test_name] = False
            
            time.sleep(1)  # Brief pause between tests
        
        # Summary
        logger.info(f"\n{'='*50}")
        logger.info("DEMO SUMMARY")
        logger.info(f"{'='*50}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if passed == total:
            logger.info("🎉 All API integration tests passed!")
        else:
            logger.warning("⚠️ Some API integration tests failed")
        
        return results


def main():
    """Main function"""
    demo = RapidTraderAPIDemo()
    
    # Check if API Gateway is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            logger.error("API Gateway is not running. Please start it first:")
            logger.error("./scripts/rapidtrader-api.sh start")
            return
    except requests.exceptions.RequestException:
        logger.error("API Gateway is not accessible. Please start it first:")
        logger.error("./scripts/rapidtrader-api.sh start")
        return
    
    # Run demo
    results = demo.run_comprehensive_demo()
    
    # Exit with appropriate code
    if all(results.values()):
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    main()
