# Docker Compose Override for Development
# This file provides development-specific configurations
# It will be automatically loaded by docker-compose

services:
  # Development overrides for all services
  rapidtrader-base: &dev-base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
    volumes:
      - ./userdata:/rapidtrader/userdata
      - ./logs:/rapidtrader/logs
      - ./.env:/rapidtrader/.env:ro
    environment:
      - PYTHONPATH=/rapidtrader
      - LOG_LEVEL=DEBUG
    restart: "no"  # Don't restart in development

  # Paper trading with development settings
  paper-trade:
    <<: *dev-base
    ports:
      - "8081:8081"
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dry_run_config.json
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1

  # Dry run with development settings
  dryrun:
    <<: *dev-base
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dryrun-config.json
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1

  # Backtest with development settings
  backtest:
    <<: *dev-base
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/backtest-config.json
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1

  # Live trading with development settings
  live:
    <<: *dev-base
    ports:
      - "8080:8080"
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/live-config.json
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1

  # Optimization with development settings
  optimize:
    <<: *dev-base
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/optimize-config.json
      - LOG_LEVEL=INFO
      - PYTHONUNBUFFERED=1

  # Shell with development settings
  shell:
    <<: *dev-base
    environment:
      - LOG_LEVEL=DEBUG
      - PYTHONUNBUFFERED=1
