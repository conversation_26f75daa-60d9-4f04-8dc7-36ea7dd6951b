# RapidTrader Backtesting Engine

RapidTrader includes a fast, accurate, and simple backtesting engine inspired by FreqTrade's approach. The engine uses vectorized pandas operations for maximum performance while ensuring realistic trade execution.

## Features

### 🚀 **Fast Performance**
- **Vectorized Operations**: Uses pandas vectorized calculations instead of loops
- **Efficient Data Processing**: Processes entire datasets at once
- **Optimized Memory Usage**: Minimal memory footprint during backtesting

### 🎯 **Accurate Results**
- **Realistic Trade Execution**: Simulates real-world order execution
- **Fee and Slippage Modeling**: Includes trading fees and slippage
- **No Lookahead Bias**: Prevents using future data in signals
- **Proper OHLC Handling**: Uses realistic entry/exit prices

### 📊 **Comprehensive Metrics**
- **Performance Metrics**: Total return, win rate, profit factor
- **Risk Analysis**: Maximum drawdown, risk-adjusted returns
- **Trade Statistics**: Average trade duration, best/worst trades
- **Detailed Reports**: JSON results with full trade history

## Quick Start

### 1. Create Sample Data
```bash
# Generate sample data for testing
python create_sample_data.py
```

### 2. Run Your First Backtest
```bash
# Run backtest with default configuration
python core/rapidtrader.py backtest run -c backtest-config
```

### 3. View Results
```bash
# Display backtest results
python core/rapidtrader.py backtest results
```

## Configuration

### Backtest Configuration File
Create a configuration file in `userdata/configs/backtest-config.json`:

```json
{
    "max_open_trades": 3,
    "stake_currency": "INR",
    "stake_amount": 1000,
    "dry_run_wallet": 100000,
    "timeframe": "1d",
    "exchange": {
        "name": "NSE",
        "pair_whitelist": [
            "RELIANCE",
            "TCS",
            "HDFCBANK"
        ]
    },
    "strategy": "SampleStrategy",
    "strategy_path": "userdata/strategies/",
    "backtest": {
        "start_date": "2024-01-01",
        "end_date": "2024-05-01",
        "results_file": "userdata/results/backtest_results.json"
    }
}
```

## Creating Strategies

### Strategy Structure
All strategies must inherit from `BaseStrategy` and implement required methods:

```python
from userdata.strategies.base_strategy import BaseStrategy
import pandas as pd
import numpy as np

class MyStrategy(BaseStrategy):
    def __init__(self, config=None):
        super().__init__(config)
        self.name = "MyStrategy"
    
    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators"""
        dataframe['sma_20'] = dataframe['close'].rolling(20).mean()
        dataframe['rsi'] = self._calculate_rsi(dataframe['close'])
        return dataframe
    
    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Generate buy signals"""
        dataframe['buy_signal'] = np.where(
            (dataframe['close'] > dataframe['sma_20']) &
            (dataframe['rsi'] < 30),
            1, 0
        )
        return dataframe
    
    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Generate sell signals"""
        dataframe['sell_signal'] = np.where(
            dataframe['rsi'] > 70,
            1, 0
        )
        return dataframe
```

### Required Signal Columns
Your strategy must generate these columns:
- `buy_signal`: 1 for buy, 0 for no action
- `sell_signal`: 1 for sell, 0 for no action

## Command Line Usage

### Basic Commands
```bash
# Run backtest with config file
rapidtrader backtest run -c <config-name>

# Run with specific strategy
rapidtrader backtest run -c <config> -s <strategy-name>

# Run with custom timerange
rapidtrader backtest run -c <config> --timerange ********-20240301

# Run with different timeframe
rapidtrader backtest run -c <config> -t 1h

# Show results
rapidtrader backtest results

# Show results for specific strategy
rapidtrader backtest results -s <strategy-name>
```

### Advanced Options
```bash
# Multiple strategies
rapidtrader backtest run -c <config> --strategy-list "Strategy1,Strategy2"

# Custom date range
rapidtrader backtest run -c <config> --timerange ********-********
```

## Data Requirements

### Data Format
Historical data should be in CSV format with these columns:
- `date`: Timestamp (YYYY-MM-DD HH:MM:SS)
- `open`: Opening price
- `high`: Highest price
- `low`: Lowest price
- `close`: Closing price
- `volume`: Trading volume

### Data Location
Place data files in `userdata/historical_data/`:
```
userdata/historical_data/
├── RELIANCE_1d.csv
├── TCS_1d.csv
└── HDFCBANK_1d.csv
```

## Results Analysis

### Metrics Provided
- **Total Trades**: Number of completed trades
- **Win Rate**: Percentage of profitable trades
- **Total Return**: Overall percentage return
- **Profit Factor**: Ratio of gross profit to gross loss
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Average Trade**: Mean profit/loss per trade
- **Best/Worst Trade**: Highest and lowest single trade results

### Results File
Results are saved in JSON format at `userdata/results/backtest_results.json`:

```json
{
    "timestamp": "2024-01-15T10:30:00",
    "results": {
        "SampleStrategy": {
            "total_trades": 15,
            "win_rate": 66.67,
            "total_return_pct": 12.45,
            "max_drawdown_pct": -5.23,
            "profit_factor": 1.85
        }
    }
}
```

## Performance Tips

### 1. Optimize Data Loading
- Use appropriate date ranges
- Limit the number of symbols
- Use higher timeframes when possible

### 2. Strategy Optimization
- Avoid complex calculations in signal generation
- Use vectorized operations instead of loops
- Cache expensive calculations

### 3. Memory Management
- Process symbols individually for large datasets
- Use appropriate data types
- Clear unused DataFrames

## Testing

### Run Complete Test Suite
```bash
# Test all backtesting functionality
python test_backtest.py
```

### Manual Testing
```bash
# Create sample data
python create_sample_data.py

# Run backtest
python core/rapidtrader.py backtest run -c backtest-config

# Check results
python core/rapidtrader.py backtest results
```

## Comparison with FreqTrade

| Feature | RapidTrader | FreqTrade |
|---------|-------------|-----------|
| **Performance** | Vectorized pandas | Vectorized pandas |
| **Accuracy** | Realistic execution | Realistic execution |
| **Simplicity** | Simple config | Complex config |
| **Indian Markets** | ✅ Native support | ❌ Limited |
| **Broker Integration** | ✅ DhanHQ, etc. | ❌ Crypto focused |
| **Strategy Format** | Python classes | Python classes |

## Troubleshooting

### Common Issues

1. **No data found**
   - Check data file format and location
   - Verify date ranges in configuration
   - Ensure symbol names match file names

2. **Strategy not loading**
   - Check strategy file syntax
   - Verify class name and methods
   - Ensure proper imports

3. **No trades generated**
   - Check signal generation logic
   - Verify data has required columns
   - Review strategy parameters

### Debug Mode
Enable detailed logging:
```bash
rapidtrader backtest run -c <config> --verbose
```

## Next Steps

1. **Create Your Strategy**: Develop custom trading strategies
2. **Optimize Parameters**: Use different timeframes and parameters
3. **Paper Trading**: Test strategies in dry-run mode
4. **Live Trading**: Deploy successful strategies with real money

For more information, see the main [README.md](README.md) file.
