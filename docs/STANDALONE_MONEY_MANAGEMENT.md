# Standalone Money Management Module

## Overview

The RapidTrader Money Management System has been refactored into a **completely standalone, modular system** that can be used by any trading application or external system. This design provides maximum flexibility and reusability.

## 🏗️ **Architecture**

### Module Structure
```
money_management/
├── __init__.py              # Main module exports
├── core.py                  # Core MoneyManager class
├── interfaces.py            # Broker and provider interfaces
├── config_manager.py        # Configuration management
├── risk_manager.py          # Risk calculations and validation
├── allocation_engine.py     # Capital allocation strategies
└── position_sizer.py        # Position sizing algorithms
```

### Key Components

#### **1. Core MoneyManager (`core.py`)**
- Main orchestrator class
- Integrates all components
- Provides unified interface
- Handles broker integration

#### **2. Interfaces (`interfaces.py`)**
- `BrokerInterface`: Standard broker interface
- `BalanceProvider`: Simple balance interface
- `BrokerAdapter`: Adapter for existing brokers
- `SimpleBalanceProvider`: Mock/testing provider

#### **3. Config<PERSON>anager (`config_manager.py`)**
- JSON-based configuration
- Dot-notation setting access
- Import/export capabilities
- Validation and defaults

#### **4. RiskManager (`risk_manager.py`)**
- Portfolio risk calculations
- Risk limit validation
- Correlation analysis
- Risk recommendations

#### **5. AllocationEngine (`allocation_engine.py`)**
- Multiple allocation strategies
- Rebalancing logic
- Strategy management
- Capital distribution

#### **6. PositionSizer (`position_sizer.py`)**
- Multiple sizing methods
- Risk-based calculations
- Kelly Criterion support
- Volatility adjustments

## 🔌 **Integration Methods**

### Method 1: Direct Import
```python
from money_management import MoneyManager
from money_management.interfaces import SimpleBalanceProvider

# Create balance provider
balance_provider = SimpleBalanceProvider(100000.0, 120000.0, 20000.0)

# Initialize money manager
money_manager = MoneyManager(broker=balance_provider)

# Use the system
money_manager.add_strategy_allocation("my_strategy", 30.0)
position_info = money_manager.get_position_size("my_strategy", "SYMBOL", 100.0)
```

### Method 2: Global Instance
```python
from money_management import get_money_manager

# Get global instance (singleton pattern)
money_manager = get_money_manager(broker=my_broker)

# Use across your application
summary = money_manager.get_allocation_summary()
```

### Method 3: Custom Broker Integration
```python
from money_management import MoneyManager

class MyCustomBroker:
    def get_fund_limit(self):
        return {"availableBalance": 150000.0, "totalBalance": 200000.0}
    
    def get_holdings(self):
        return []
    
    def get_positions(self):
        return []
    
    def is_dry_run(self):
        return True

# Automatic adaptation
money_manager = MoneyManager(broker=MyCustomBroker())
```

### Method 4: Adapter Pattern
```python
from money_management.interfaces import BrokerAdapter

# Adapt existing broker with method mapping
adapter = BrokerAdapter(
    broker=existing_broker,
    method_mapping={
        'get_fund_limit': 'get_account_balance',
        'get_holdings': 'get_portfolio',
        'get_positions': 'get_open_positions'
    }
)

money_manager = MoneyManager(broker=adapter)
```

## 🎯 **Key Benefits**

### **1. Complete Independence**
- No dependencies on RapidTrader core
- Can be used in any Python application
- Standalone configuration and data management

### **2. Flexible Integration**
- Works with any broker through interfaces
- Automatic broker adaptation
- Multiple integration patterns

### **3. Modular Design**
- Each component can be used independently
- Easy to extend and customize
- Clean separation of concerns

### **4. Production Ready**
- Comprehensive error handling
- Extensive logging
- Configuration validation
- Risk management safeguards

### **5. Backward Compatibility**
- RapidTrader CLI still works
- Existing code continues to function
- Deprecation warnings for migration

## 📊 **Features**

### **Capital Allocation**
- Percentage-based allocation
- Fixed amount allocation
- Dynamic allocation (performance-based)
- Risk-adjusted allocation

### **Position Sizing**
- Percentage sizing
- Risk-based sizing
- Equal weight sizing
- Kelly Criterion
- Volatility-based sizing

### **Risk Management**
- Per-trade risk limits
- Portfolio risk monitoring
- Concentration limits
- Correlation analysis
- Global stop-loss protection

### **Configuration**
- JSON-based configuration
- Runtime setting updates
- Import/export capabilities
- Multiple configuration profiles

## 🚀 **Usage Examples**

### Basic Trading System
```python
from money_management import MoneyManager
from money_management.interfaces import SimpleBalanceProvider

class SimpleTradingSystem:
    def __init__(self, capital: float):
        balance_provider = SimpleBalanceProvider(capital)
        self.money_manager = MoneyManager(broker=balance_provider)
        
        # Setup strategies
        self.money_manager.add_strategy_allocation("trend_following", 40.0)
        self.money_manager.add_strategy_allocation("mean_reversion", 30.0)
        self.money_manager.add_strategy_allocation("momentum", 20.0)
    
    def calculate_trade_size(self, strategy: str, symbol: str, price: float):
        return self.money_manager.get_position_size(strategy, symbol, price)
    
    def check_risk(self, new_positions: list):
        return self.money_manager.check_risk_limits(new_positions)

# Usage
trading_system = SimpleTradingSystem(100000.0)
trade_info = trading_system.calculate_trade_size("trend_following", "AAPL", 150.0)
```

### Advanced Integration
```python
from money_management import MoneyManager

class AdvancedTradingPlatform:
    def __init__(self, broker):
        self.money_manager = MoneyManager(
            broker=broker,
            config_path="configs/money_management.json"
        )
        
        # Configure risk parameters
        self.money_manager.update_global_settings({
            "max_risk_per_trade": 1.5,
            "max_total_risk": 10.0,
            "emergency_reserve": 15.0
        })
    
    def add_strategy(self, name: str, allocation: float):
        return self.money_manager.add_strategy_allocation(name, allocation)
    
    def get_portfolio_status(self):
        return self.money_manager.get_allocation_summary()
    
    def rebalance_portfolio(self):
        return self.money_manager.rebalance_allocations()

# Multi-strategy setup
platform = AdvancedTradingPlatform(my_broker)
platform.add_strategy("scalping", 15.0)
platform.add_strategy("swing_trading", 35.0)
platform.add_strategy("position_trading", 25.0)
```

## 🔧 **Configuration**

### Default Configuration Location
- **RapidTrader**: `userdata/config/money_management.json`
- **Standalone**: `money_management_config.json` (current directory)
- **Custom**: Specify path in constructor

### Configuration Structure
```json
{
    "global_settings": {
        "total_capital_usage": 90.0,
        "emergency_reserve": 10.0,
        "max_risk_per_trade": 2.0,
        "max_total_risk": 15.0,
        "stop_loss_global": 20.0
    },
    "allocation_model": {
        "type": "percentage",
        "strategies": {},
        "default_allocation": 10.0
    },
    "risk_management": {
        "position_sizing": "percentage",
        "max_positions_per_strategy": 5,
        "single_stock_limit": 10.0
    }
}
```

## 📈 **Performance**

### Optimizations
- **Caching**: Balance data cached to reduce API calls
- **Lazy Loading**: Components initialized on demand
- **Efficient Calculations**: Optimized algorithms for large portfolios
- **Memory Management**: Minimal memory footprint

### Scalability
- **Multiple Instances**: Support for multiple money managers
- **Concurrent Access**: Thread-safe operations
- **Large Portfolios**: Handles hundreds of strategies/positions
- **Real-time Updates**: Fast calculation updates

## 🛡️ **Safety Features**

### Built-in Protections
- **Allocation Validation**: Prevents over-allocation
- **Risk Limits**: Automatic risk limit enforcement
- **Configuration Validation**: Ensures valid settings
- **Error Recovery**: Graceful error handling
- **Logging**: Comprehensive audit trail

### Emergency Controls
- **Global Stop Loss**: Emergency trading halt
- **Emergency Reserve**: Always maintains cash buffer
- **Risk Circuit Breakers**: Automatic position reduction
- **Manual Overrides**: Emergency manual controls

## 🔄 **Migration Guide**

### From Old System
```python
# Old way (deprecated)
from core.money_manager import MoneyManager, get_money_manager

# New way
from money_management import MoneyManager, get_money_manager
```

### Backward Compatibility
- All existing RapidTrader CLI commands work unchanged
- Existing configuration files are automatically migrated
- Deprecation warnings guide migration
- Full compatibility for 6 months

## 📚 **Documentation**

- **API Reference**: Complete method documentation
- **Examples**: `examples/money_management_example.py`
- **Integration Guide**: This document
- **CLI Reference**: `docs/MONEY_MANAGEMENT.md`

The standalone money management module provides enterprise-grade money management capabilities that can be integrated into any trading system, making it a truly universal solution for capital allocation and risk management.
