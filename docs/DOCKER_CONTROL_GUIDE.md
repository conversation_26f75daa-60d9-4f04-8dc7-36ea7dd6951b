# RapidTrader Docker Control & Testing Guide

This guide provides complete control over RapidTrader Docker containers and comprehensive testing of all trading modes.

## 🎯 **Complete Container Control**

### **Enhanced Management Script**

The `scripts/docker-run.sh` script provides full control over all RapidTrader modes:

```bash
# Build the Docker image
./scripts/docker-run.sh build

# Test different trading modes
./scripts/docker-run.sh paper-trade --duration 3600 --capital 50000
./scripts/docker-run.sh dryrun -c userdata/config/dryrun-config.json
./scripts/docker-run.sh backtest -c userdata/config/backtest-config.json
./scripts/docker-run.sh optimize -c userdata/config/optimize-config.json
./scripts/docker-run.sh live -c userdata/config/live-config.json

# Container management
./scripts/docker-run.sh status          # Show all container status
./scripts/docker-run.sh monitor         # Real-time monitoring
./scripts/docker-run.sh stop <name>     # Stop specific container
./scripts/docker-run.sh stop-all        # Stop all containers
./scripts/docker-run.sh restart <name>  # Restart container
./scripts/docker-run.sh clean           # Remove stopped containers
./scripts/docker-run.sh logs <name>     # View container logs
```

## 🧪 **Comprehensive Testing Suite**

### **Automated Testing Script**

Use `scripts/docker-control.sh` for comprehensive testing:

```bash
# Test all modes
./scripts/docker-control.sh all

# Test specific modes
./scripts/docker-control.sh paper      # Paper trading
./scripts/docker-control.sh dryrun     # Dry run
./scripts/docker-control.sh backtest   # Backtesting (auto-stop)
./scripts/docker-control.sh optimize   # Optimization (auto-stop)
./scripts/docker-control.sh live       # Live trading demo
```

### **Docker Compose Testing**

Use the test compose file for structured testing:

```bash
# Test paper trading
docker-compose -f docker-compose.test.yml --profile test-paper up

# Test dry run
docker-compose -f docker-compose.test.yml --profile test-dryrun up

# Test backtesting (auto-stops)
docker-compose -f docker-compose.test.yml --profile test-backtest up

# Test optimization (auto-stops)
docker-compose -f docker-compose.test.yml --profile test-optimize up

# Test all modes
docker-compose -f docker-compose.test.yml --profile test-all up
```

## 🎮 **Mode-Specific Control**

### **1. Paper Trading (Independent)**

**Features:**
- Uses yfinance delayed data (15-20 min delay)
- No broker API required
- Completely safe simulation
- Auto-stops after specified duration

**Control Commands:**
```bash
# Start paper trading for 1 hour
./scripts/docker-run.sh paper-trade --duration 3600 --capital 100000

# Monitor paper trading
./scripts/docker-run.sh logs rapidtrader-paper-trade

# Stop paper trading
./scripts/docker-run.sh stop rapidtrader-paper-trade
```

**Docker Compose:**
```bash
# Start paper trading
docker-compose --profile paper-trade up

# Stop paper trading
docker-compose --profile paper-trade down
```

### **2. Dry Run (Broker-based)**

**Features:**
- Uses real broker API but no real trades
- Requires API credentials
- Real market data simulation
- Continuous operation until stopped

**Control Commands:**
```bash
# Start dry run
./scripts/docker-run.sh dryrun

# Monitor dry run
./scripts/docker-run.sh logs rapidtrader-dryrun

# Stop dry run
./scripts/docker-run.sh stop rapidtrader-dryrun
```

### **3. Backtesting (Auto-Stop)**

**Features:**
- Processes historical data
- **Automatically stops when complete**
- Generates performance reports
- No manual intervention needed

**Control Commands:**
```bash
# Start backtesting (will auto-stop)
./scripts/docker-run.sh backtest

# Monitor progress
./scripts/docker-run.sh logs rapidtrader-backtest

# Check if completed
./scripts/docker-run.sh status
```

**Auto-Stop Behavior:**
- Container runs until backtesting completes
- Automatically exits with results
- No restart policy (restart: "no")
- Results saved to userdata/results/

### **4. Optimization/Hyperopt (Auto-Stop)**

**Features:**
- Parameter optimization
- **Automatically stops when complete**
- Can run for hours/days
- Saves optimal parameters

**Control Commands:**
```bash
# Start optimization (will auto-stop)
./scripts/docker-run.sh optimize

# Monitor progress
./scripts/docker-run.sh logs rapidtrader-optimize

# Check completion status
./scripts/docker-run.sh status
```

**Auto-Stop Behavior:**
- Runs until optimization epochs complete
- Automatically exits with best parameters
- Results saved to userdata/results/
- No manual stopping required

### **5. Live Trading**

**Features:**
- Real trading with real money
- Continuous operation
- Web interface on port 8080
- Requires careful monitoring

**Control Commands:**
```bash
# Start live trading (use with caution!)
./scripts/docker-run.sh live

# Monitor live trading
./scripts/docker-run.sh logs rapidtrader-live

# Stop live trading
./scripts/docker-run.sh stop rapidtrader-live
```

## 📊 **Real-time Monitoring**

### **Container Monitor**
```bash
# Real-time container monitoring
./scripts/docker-run.sh monitor

# Shows:
# - Running containers
# - Resource usage (CPU, Memory, Network)
# - Status updates every 5 seconds
# - Press Ctrl+C to exit
```

### **Individual Container Logs**
```bash
# View live logs
./scripts/docker-run.sh logs rapidtrader-paper-trade
./scripts/docker-run.sh logs rapidtrader-backtest
./scripts/docker-run.sh logs rapidtrader-optimize

# Follow logs in real-time
docker logs -f rapidtrader-backtest
```

### **Container Status**
```bash
# Show all container status
./scripts/docker-run.sh status

# Output shows:
# - Running containers
# - Stopped containers
# - Port mappings
# - Resource usage
```

## 🔄 **Container Lifecycle Management**

### **Automatic Stopping (Backtesting & Optimization)**

**Configuration in docker-compose.yml:**
```yaml
backtest:
  restart: "no"  # Don't restart when complete
  
optimize:
  restart: "no"  # Don't restart when complete
```

**Behavior:**
- Containers run until task completion
- Automatically exit with success/failure code
- No manual intervention required
- Results preserved in userdata/

### **Manual Control (Paper Trading & Live Trading)**

**Long-running Services:**
- Paper trading: Runs for specified duration
- Dry run: Runs until manually stopped
- Live trading: Runs until manually stopped

**Control:**
```bash
# Stop specific container
./scripts/docker-run.sh stop rapidtrader-paper-trade

# Stop all containers
./scripts/docker-run.sh stop-all

# Restart container
./scripts/docker-run.sh restart rapidtrader-dryrun
```

## 🛠 **Advanced Control Features**

### **Resource Management**
```bash
# Monitor resource usage
docker stats --filter "name=rapidtrader-*"

# Set resource limits (in docker-compose.yml)
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
```

### **Network Management**
```bash
# View container networks
docker network ls | grep rapidtrader

# Inspect network
docker network inspect rapidtrader-network
```

### **Volume Management**
```bash
# Check volume usage
docker system df

# Backup userdata
tar -czf userdata-backup.tar.gz userdata/
```

## 🎯 **Testing Scenarios**

### **Quick Test (5 minutes)**
```bash
# Test paper trading for 5 minutes
./scripts/docker-run.sh paper-trade --duration 300 --capital 10000
```

### **Full Test Suite (30 minutes)**
```bash
# Run comprehensive tests
./scripts/docker-control.sh all
```

### **Production Simulation**
```bash
# Start multiple modes simultaneously
docker-compose --profile paper-trade up -d
docker-compose --profile dryrun up -d
./scripts/docker-run.sh monitor
```

## 🚀 **Best Practices**

1. **Always test with paper trading first**
2. **Monitor resource usage during optimization**
3. **Use auto-stopping for backtesting and optimization**
4. **Keep logs for debugging**
5. **Backup userdata regularly**
6. **Use profiles for organized testing**
7. **Monitor container health**

## 🎉 **Summary**

The Docker implementation provides:

✅ **Complete control** over all trading modes
✅ **Automatic stopping** for backtesting and optimization
✅ **Real-time monitoring** and logging
✅ **Easy testing** of all modes
✅ **Production-ready** container management
✅ **FreqTrade-like** workflow and commands

You now have full control over RapidTrader in Docker with automatic lifecycle management!
