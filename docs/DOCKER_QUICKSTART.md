# RapidTrader Docker Quick Start Guide

This guide will help you get started with RapidTrader using Docker containers, similar to FreqTrade's approach.

## Prerequisites

- Docker installed and running
- Docker Compose installed
- Git (to clone the repository)

## Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/rapidtrader.git
cd rapidtrader

# Copy environment file and configure
cp .env.example .env
# Edit .env with your API credentials
```

### 2. Build the Docker Image

```bash
# Using the management script
./scripts/docker-run.sh build

# Or using docker-compose
docker-compose build
```

### 3. Run Paper Trading (Recommended for beginners)

```bash
# Run paper trading for 1 hour with default settings
./scripts/docker-run.sh paper-trade --duration 3600

# Run with custom capital
./scripts/docker-run.sh paper-trade --capital 50000 --duration 1800

# Run indefinitely (stop with Ctrl+C)
./scripts/docker-run.sh paper-trade
```

### 4. Monitor Your Trading

```bash
# Check container status
./scripts/docker-run.sh status

# View logs
./scripts/docker-run.sh logs rapidtrader-paper-trade

# Stop containers
./scripts/docker-run.sh stop
```

## Available Commands

### Paper Trading (Independent)
- **Purpose**: Test strategies with yfinance delayed data
- **No broker required**: Uses simulated market data
- **Safe**: No real money involved

```bash
# Basic paper trading
docker-compose run --rm paper-trade

# With custom duration and capital
./scripts/docker-run.sh paper-trade --duration 7200 --capital 100000

# With custom config
docker-compose run --rm paper-trade -c userdata/config/my_paper_config.json
```

### Dry Run (Broker-based)
- **Purpose**: Test with real broker API but no real trades
- **Requires**: Broker API credentials
- **Safe**: No real money involved

```bash
# Basic dry run
docker-compose run --rm dryrun

# With custom config
./scripts/docker-run.sh dryrun -c userdata/config/dryrun-config.json
```

### Backtesting
- **Purpose**: Test strategies on historical data
- **Fast**: Processes historical data quickly
- **Analysis**: Provides detailed performance metrics

```bash
# Basic backtesting
docker-compose run --rm backtest

# With custom parameters
./scripts/docker-run.sh backtest -c userdata/config/backtest-config.json
```

### Live Trading
- **Purpose**: Real trading with real money
- **Requires**: Broker API credentials and sufficient balance
- **Risk**: Real money involved

```bash
# Live trading (use with caution!)
docker-compose run --rm live

# With custom config
./scripts/docker-run.sh live -c userdata/config/live-config.json
```

### Strategy Optimization
- **Purpose**: Find optimal strategy parameters
- **Resource intensive**: Uses multiple CPU cores
- **Time consuming**: Can take hours to complete

```bash
# Basic optimization
docker-compose run --rm optimize

# With custom config
./scripts/docker-run.sh optimize -c userdata/config/optimize-config.json
```

## Configuration Files

All configuration files are located in `userdata/config/`:

- `dry_run_config.json` - Independent paper trading configuration
- `dryrun-config.json` - Broker-based dry run configuration  
- `backtest-config.json` - Backtesting configuration
- `live-config.json` - Live trading configuration
- `optimize-config.json` - Optimization configuration

## Data Persistence

All data is stored in the `userdata/` directory:

```
userdata/
├── config/           # Configuration files
├── strategies/       # Trading strategies
├── historical_data/  # Downloaded market data
├── logs/            # Log files
└── results/         # Backtest and optimization results
```

## Docker Compose Profiles

Use profiles to run specific services:

```bash
# Paper trading
docker-compose --profile paper-trade up

# Dry run
docker-compose --profile dryrun up

# Backtesting
docker-compose --profile backtest up

# Live trading
docker-compose --profile live up

# Optimization
docker-compose --profile optimize up
```

## Troubleshooting

### Container Won't Start
```bash
# Check Docker is running
docker info

# Check logs
./scripts/docker-run.sh logs <container-name>

# Rebuild image
./scripts/docker-run.sh build
```

### Configuration Issues
```bash
# Validate config file
python -m json.tool userdata/config/your-config.json

# Use shell to debug
./scripts/docker-run.sh shell
```

### Permission Issues
```bash
# Fix userdata permissions
sudo chown -R $USER:$USER userdata/
```

## Best Practices

1. **Start with Paper Trading**: Always test strategies with paper trading first
2. **Use Version Control**: Keep your strategies and configs in git
3. **Monitor Logs**: Regularly check container logs for issues
4. **Backup Data**: Backup your userdata directory regularly
5. **Test Configurations**: Validate configs before live trading

## Next Steps

1. Customize your strategies in `userdata/strategies/`
2. Modify configurations in `userdata/config/`
3. Run backtests to validate performance
4. Use paper trading to test in real-time
5. Only move to live trading after thorough testing

For more detailed information, see the main README.md and other documentation files.
