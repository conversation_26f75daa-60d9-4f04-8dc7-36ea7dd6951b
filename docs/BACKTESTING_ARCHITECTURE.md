# 🏗️ RapidTrader Backtesting Architecture

## 📊 System Overview

RapidTrader's backtesting engine is a sophisticated, multi-layered system designed for high-performance strategy testing with automatic data management and realistic trade simulation.

---

## 🎯 Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           🎯 RapidTrader CLI Interface                          │
│                         python core/rapidtrader.py backtest                    │
└─────────────────────────┬───────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        📊 Backtest Orchestrator                                │
│                           (core/backtest.py)                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │ Config Loading  │  │ Strategy Loading│  │ Results Display │                │
│  │ & Validation    │  │ & Validation    │  │ & Persistence   │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────┬───────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                      🚀 Engine Selection Logic                                 │
│                                                                                 │
│  ┌─────────────────────────────────┐    ┌─────────────────────────────────┐    │
│  │        📊 Standard Engine       │    │       🚀 Optimized Engine       │    │
│  │    (BacktestEngine)             │    │  (OptimizedBacktestEngine)      │    │
│  │                                 │    │                                 │    │
│  │ • Row-by-row processing         │    │ • Vectorized operations         │    │
│  │ • DataFrame copying             │    │ • In-place modifications        │    │
│  │ • Standard pandas ops           │    │ • Numpy acceleration            │    │
│  │ • Sequential symbol processing  │    │ • Batch symbol processing       │    │
│  └─────────────────────────────────┘    └─────────────────────────────────┘    │
└─────────────────────────┬───────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        🔧 Core Engine Components                               │
│                                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │
│  │   📥 Data       │  │   💹 Trade      │  │   📈 Results    │                │
│  │   Manager       │  │   Simulator     │  │   Calculator    │                │
│  │                 │  │                 │  │                 │                │
│  │ • Auto download │  │ • Realistic     │  │ • Comprehensive │                │
│  │ • Format conv.  │  │   execution     │  │   metrics       │                │
│  │ • Multi-source  │  │ • Fee modeling  │  │ • Risk analysis │                │
│  │ • Caching       │  │ • Slippage sim. │  │ • Performance   │                │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘                │
└─────────────────────────┬───────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────────────────────┐
│                       🎯 Strategy Processing Pipeline                          │
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   📊 Data       │───▶│   🔧 Strategy   │───▶│   💹 Trade      │             │
│  │   Loading       │    │   Analysis      │    │   Simulation    │             │
│  │                 │    │                 │    │                 │             │
│  │ • Load OHLCV    │    │ • Indicators    │    │ • Signal proc.  │             │
│  │ • Validate      │    │ • Buy signals   │    │ • Order exec.   │             │
│  │ • Optimize      │    │ • Sell signals  │    │ • P&L calc.     │             │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘             │
└─────────────────────────────────────────────────────────────────────────────────┘
```

---

## 🔄 Detailed Component Flow

### 1. **CLI Entry Point**
```
User Command: python core/rapidtrader.py backtest run -c config --optimized
                                    │
                                    ▼
                            Parse Arguments
                                    │
                                    ▼
                            Load Configuration
                                    │
                                    ▼
                            Validate Parameters
                                    │
                                    ▼
                            Call run_backtest()
```

### 2. **Engine Initialization**
```
run_backtest() Function
        │
        ▼
Check --optimized Flag
        │
        ├─── Yes ──▶ OptimizedBacktestEngine()
        │                    │
        │                    ▼
        │            Initialize with:
        │            • Vectorized operations
        │            • Memory optimization
        │            • Batch processing
        │
        └─── No ───▶ BacktestEngine()
                             │
                             ▼
                     Initialize with:
                     • Standard operations
                     • Row-by-row processing
                     • Sequential execution
```

### 3. **Data Management Flow**
```
DataManager.ensure_data_available()
                │
                ▼
        Check Each Symbol
                │
                ├─── Data Exists ──▶ Validate Date Coverage
                │                            │
                │                            ├─── Complete ──▶ ✅ Ready
                │                            └─── Incomplete ──▶ Download More
                │
                └─── Missing ──▶ Add to Download Queue
                                        │
                                        ▼
                                Download Process
                                        │
                                        ├─── Try YFinanceFetcher
                                        │           │
                                        │           ├─── Success ──▶ Convert Format
                                        │           └─── Fail ──▶ Try download_data
                                        │
                                        └─── Try download_data Module
                                                    │
                                                    ├─── Success ──▶ Convert Format
                                                    └─── Fail ──▶ Log Error
```

### 4. **Strategy Analysis Pipeline**
```
Strategy.analyze(dataframe)
            │
            ▼
    populate_indicators()
            │
            ├─── Standard ──▶ pandas.rolling().mean()
            └─── Optimized ──▶ numpy vectorized + caching
            │
            ▼
    populate_buy_signals()
            │
            ├─── Standard ──▶ Boolean conditions + pandas logic
            └─── Optimized ──▶ numpy boolean arrays + vectorized
            │
            ▼
    populate_sell_signals()
            │
            ├─── Standard ──▶ Boolean conditions + pandas logic
            └─── Optimized ──▶ numpy boolean arrays + vectorized
            │
            ▼
    Return DataFrame with buy_signal & sell_signal columns
```

### 5. **Trade Simulation Process**
```
Trade Simulation
        │
        ├─── Standard Engine ──▶ _simulate_trades()
        │                              │
        │                              ▼
        │                      for timestamp, row in data.iterrows():
        │                              │
        │                              ├─── Buy Signal ──▶ Check Conditions ──▶ Execute Trade
        │                              └─── Sell Signal ──▶ Check Conditions ──▶ Close Trade
        │
        └─── Optimized Engine ──▶ _simulate_trades_vectorized()
                                         │
                                         ▼
                                 Convert to numpy arrays
                                         │
                                         ▼
                                 Find signal indices with np.where()
                                         │
                                         ▼
                                 Process signals in batch
                                         │
                                         ▼
                                 Fast trade execution methods
```

### 6. **Trade Execution Logic**
```
TradeSimulator.open_trade()
            │
            ▼
    Check Conditions:
    ├─── Max open trades limit
    ├─── Sufficient balance
    ├─── Valid price/quantity
    └─── Apply slippage & fees
            │
            ├─── All Pass ──▶ Execute Trade
            │                      │
            │                      ├─── Create trade record
            │                      ├─── Update balance
            │                      ├─── Track fees
            │                      └─── Store open position
            │
            └─── Any Fail ──▶ Return None (skip trade)

TradeSimulator.close_trade()
            │
            ▼
    Calculate Exit:
    ├─── Apply exit slippage
    ├─── Calculate exit fees
    ├─── Compute P&L
    └─── Calculate duration
            │
            ▼
    Update Records:
    ├─── Complete trade record
    ├─── Update balance
    ├─── Track total fees
    └─── Remove from open positions
```

---

## 🎯 Key Decision Points & Conditions

### **1. Engine Selection**
```python
if optimized_flag:
    engine = OptimizedBacktestEngine(config)
    # Uses vectorized operations, memory optimization
else:
    engine = BacktestEngine(config)
    # Uses standard pandas operations
```

### **2. Data Availability**
```python
for symbol in symbols:
    if not data_exists(symbol, timeframe, date_range):
        download_queue.append(symbol)

if download_queue:
    download_data(download_queue)
```

### **3. Trade Execution**
```python
if buy_signal and no_open_position:
    if can_open_trade() and sufficient_balance():
        execute_entry()

if sell_signal and has_open_position:
    execute_exit()
```

### **4. Error Handling**
```python
try:
    result = process_symbol(symbol)
except CriticalError:
    stop_execution()
except NonCriticalError:
    log_warning()
    continue_processing()
```

---

## 📊 Performance Characteristics

### **Standard Engine**
- **Processing**: Row-by-row iteration
- **Memory**: Higher usage due to DataFrame copying
- **Speed**: Moderate (8+ seconds for 5 symbols)
- **Compatibility**: Maximum compatibility
- **Use Case**: Development, debugging, complex strategies

### **Optimized Engine**
- **Processing**: Vectorized numpy operations
- **Memory**: Optimized with float32 data types
- **Speed**: High performance (2-3 seconds for 5 symbols)
- **Compatibility**: Modern pandas/numpy required
- **Use Case**: Production, high-frequency testing

---

## 🔧 Configuration Flow

### **Config Loading Hierarchy**
```
1. Load base config from JSON file
2. Apply CLI parameter overrides
3. Validate required fields
4. Set defaults for missing values
5. Initialize components with config
```

### **Strategy Loading Process**
```
1. Parse strategy name from config
2. Construct file path (userdata/strategies/)
3. Import strategy module dynamically
4. Instantiate strategy class with config
5. Validate strategy interface (analyze method)
```

---

## 📈 Results Generation Pipeline

### **Metrics Calculation**
```
BacktestResults.calculate_metrics()
            │
            ▼
    Basic Statistics:
    ├─── Total trades count
    ├─── Win/loss ratio
    ├─── Win rate percentage
    └─── Trade duration analysis
            │
            ▼
    Financial Metrics:
    ├─── Total P&L
    ├─── Average P&L per trade
    ├─── Best/worst trades
    └─── Total fees paid
            │
            ▼
    Risk Analysis:
    ├─── Maximum drawdown
    ├─── Sharpe ratio
    ├─── Profit factor
    └─── Return percentage
            │
            ▼
    Compile Results Dictionary
```

### **Output Generation**
```
Results Processing
        │
        ├─── Console Display (Rich tables)
        ├─── JSON File Export
        └─── Log File Recording
```

---

## 🎯 Summary

RapidTrader's backtesting architecture provides:

1. **Dual-Engine Design**: Standard and optimized engines for different use cases
2. **Automatic Data Management**: Seamless data downloading and format conversion
3. **Realistic Trade Simulation**: Fees, slippage, and position management
4. **Comprehensive Results**: Professional-grade performance metrics
5. **Error Resilience**: Robust error handling and recovery
6. **Performance Optimization**: Vectorized operations and memory efficiency
7. **Extensible Design**: Easy to add new strategies and features

This architecture enables RapidTrader to deliver FreqTrade-level accuracy with superior ease of use and Indian market integration.
