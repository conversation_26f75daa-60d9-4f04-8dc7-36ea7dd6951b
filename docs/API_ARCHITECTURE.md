# RapidTrader API Architecture v2.0

## 🚀 **OpenAlgo-Inspired Unified API Gateway**

RapidTrader v2.0 introduces a completely redesigned architecture inspired by [OpenAlgo](https://github.com/marketcalls/openalgo), providing a unified API interface for all trading operations with enhanced security, modularity, and scalability.

## 🏗️ **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────┐
│                    RapidTrader v2.0                        │
├─────────────────────────────────────────────────────────────┤
│  🌐 API Gateway (FastAPI)                                  │
│  ├── 🔐 Authentication & Authorization                     │
│  ├── 🏦 Unified Broker Interface                          │
│  ├── 📊 Container Management                               │
│  ├── 📈 Real-time WebSocket                               │
│  └── 📋 Rate Limiting & Security                          │
├─────────────────────────────────────────────────────────────┤
│  🔌 Broker Adapters                                        │
│  ├── Fyers API v3                                          │
│  ├── DhanHQ API                                            │
│  └── [Extensible for more brokers]                         │
├─────────────────────────────────────────────────────────────┤
│  🐳 Trading Containers                                     │
│  ├── Backtesting Containers                                │
│  ├── Dry Run Containers                                    │
│  ├── Live Trading Containers                               │
│  └── Strategy Optimization Containers                      │
└─────────────────────────────────────────────────────────────┘
```

## 🔑 **Key Features**

### **1. API Key Authentication**
- **Secure API Keys**: `rt_` prefixed keys with bcrypt hashing
- **Permission-based Access**: Read/Write permissions
- **Expiration Support**: Time-based key expiration
- **Usage Tracking**: Monitor API key usage and statistics

### **2. Unified Broker Interface**
- **Broker Abstraction**: Single API for all brokers
- **Standardized Responses**: Consistent data formats
- **Error Handling**: Unified error responses
- **Rate Limiting**: Broker-specific rate limits

### **3. Container Orchestration**
- **Dynamic Containers**: Start/stop trading containers via API
- **Resource Management**: Automatic resource allocation
- **Log Aggregation**: Centralized logging from all containers
- **Health Monitoring**: Real-time container health checks

### **4. Real-time Updates**
- **WebSocket Support**: Live updates for orders, positions, P&L
- **Event Streaming**: Real-time market data and trade events
- **Notifications**: Instant alerts for important events

## 🚀 **Quick Start**

### **1. Setup Environment**
```bash
# Initial setup
./scripts/rapidtrader-api.sh setup

# Start API Gateway
./scripts/rapidtrader-api.sh start
```

### **2. Create API Key**
```bash
# Create your first API key
./scripts/rapidtrader-api.sh create-key "my-trading-bot"

# Save the returned API key securely
export RAPIDTRADER_API_KEY="rt_your_api_key_here"
```

### **3. Configure Broker**
```bash
# Add Fyers broker configuration
curl -X POST "http://localhost:8000/brokers" \
  -H "Authorization: Bearer $RAPIDTRADER_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "broker_name": "fyers",
    "credentials": {
      "client_id": "your_client_id",
      "access_token": "your_access_token"
    },
    "dry_run": true,
    "live_data": true
  }'
```

### **4. Start Trading**
```bash
# Place an order
curl -X POST "http://localhost:8000/api/v1/placeorder?broker_name=fyers_20241201_120000" \
  -H "Authorization: Bearer $RAPIDTRADER_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "NSE:SBIN-EQ",
    "quantity": 10,
    "side": "BUY",
    "order_type": "MARKET",
    "product_type": "MIS"
  }'
```

## 📚 **API Documentation**

### **Authentication Endpoints**

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/auth/api-keys` | Create new API key |
| GET | `/auth/api-keys` | List all API keys |
| DELETE | `/auth/api-keys/{key_id}` | Revoke API key |

### **Broker Management**

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/brokers` | Add broker configuration |
| GET | `/brokers` | List configured brokers |
| DELETE | `/brokers/{broker_name}` | Remove broker |

### **Trading Operations (OpenAlgo Compatible)**

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/placeorder` | Place trading order |
| GET | `/api/v1/orderbook` | Get order book |
| GET | `/api/v1/positions` | Get positions |
| GET | `/api/v1/tradebook` | Get trade history |
| GET | `/api/v1/funds` | Get account funds |

### **Container Management**

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/containers` | List all containers |
| POST | `/containers/manage` | Start/stop containers |
| GET | `/logs/aggregated` | Get aggregated logs |
| GET | `/pnl/aggregated` | Get aggregated P&L |

## 🔒 **Security Features**

### **1. API Key Security**
- **Secure Generation**: Cryptographically secure random keys
- **Hash Storage**: Keys stored as SHA-256 hashes
- **Permission Control**: Granular read/write permissions
- **Expiration**: Automatic key expiration

### **2. Rate Limiting**
- **Endpoint-specific Limits**: Different limits for different operations
- **Per-key Tracking**: Individual rate limits per API key
- **Sliding Window**: Time-based rate limiting

### **3. Input Validation**
- **Pydantic Models**: Automatic request validation
- **Type Safety**: Strong typing for all inputs
- **Sanitization**: Input sanitization and validation

## 🐳 **Docker Simplification**

### **Before (Multiple Docker Files)**
```
├── Dockerfile
├── Dockerfile.alpine
├── docker-compose.yml
├── docker-compose.override.yml
├── docker-compose.production.yml
├── docker-compose.test.yml
└── api_gateway/Dockerfile
```

### **After (Simplified)**
```
├── docker-compose.api.yml  # Single compose file
└── api_gateway/Dockerfile  # API Gateway only
```

### **Benefits**
- **Single Entry Point**: One API Gateway manages everything
- **Simplified Deployment**: One command to start everything
- **Better Resource Management**: Dynamic container allocation
- **Easier Maintenance**: Centralized configuration

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Required
RAPIDTRADER_SECRET_KEY=your-secret-key-here

# Optional
RAPIDTRADER_ENV=production
API_REQUEST_TIMEOUT=30
API_MAX_RETRIES=3
RATE_LIMIT_REQUESTS_PER_MINUTE=60
```

### **Broker Configuration**
```json
{
  "broker_name": "fyers",
  "credentials": {
    "client_id": "your_client_id",
    "access_token": "your_access_token",
    "refresh_token": "your_refresh_token"
  },
  "dry_run": true,
  "live_data": true
}
```

## 📊 **Monitoring & Observability**

### **Health Checks**
```bash
# API Gateway health
curl http://localhost:8000/health

# Container status
./scripts/rapidtrader-api.sh status
```

### **Logging**
- **Centralized Logs**: All container logs aggregated
- **Structured Logging**: JSON-formatted logs
- **Log Levels**: DEBUG, INFO, WARNING, ERROR
- **Real-time Streaming**: WebSocket log streaming

### **Metrics**
- **API Usage**: Request counts and response times
- **Container Metrics**: CPU, memory, disk usage
- **Trading Metrics**: Orders, trades, P&L tracking

## 🔄 **Migration from v1.0**

### **1. Backup Current Setup**
```bash
# Backup configurations
cp -r userdata userdata.backup
cp .env .env.backup
```

### **2. Setup v2.0**
```bash
# Setup new architecture
./scripts/rapidtrader-api.sh setup

# Start API Gateway
./scripts/rapidtrader-api.sh start
```

### **3. Migrate Configurations**
```bash
# Create API key
./scripts/rapidtrader-api.sh create-key "migration"

# Add broker configurations via API
# (Use the API endpoints to add your brokers)
```

## 🤝 **OpenAlgo Compatibility**

RapidTrader v2.0 is designed to be compatible with OpenAlgo's API structure:

- **Same Endpoint Patterns**: `/api/v1/placeorder`, `/api/v1/orderbook`
- **Compatible Request/Response**: Similar JSON structures
- **API Key Authentication**: Bearer token authentication
- **Rate Limiting**: Similar rate limiting approach

This means existing OpenAlgo-compatible tools and scripts can work with RapidTrader v2.0 with minimal modifications.

## 🎯 **Next Steps**

1. **Test the new API Gateway**: `./scripts/rapidtrader-api.sh start`
2. **Create your first API key**: `./scripts/rapidtrader-api.sh create-key "test"`
3. **Add a broker configuration**: Use the `/brokers` endpoint
4. **Start trading**: Use the unified trading endpoints
5. **Monitor via WebSocket**: Connect to `/ws` for real-time updates

The new architecture provides a much more scalable, secure, and maintainable foundation for your trading operations! 🚀
