# Fyers WebSocket Real-time Data Guide

## Overview

The Fyers WebSocket integration provides real-time market data streaming capabilities for the RapidTrader platform. This guide covers how to use the WebSocket functionality to receive live quotes, market depth, and order updates.

## Features

### ✅ **Implemented Features**
- **Real-time Quotes**: Live price updates for subscribed symbols
- **Market Depth**: Level 2 market data with bid/ask information
- **Order Updates**: Real-time notifications for order status changes
- **Automatic Reconnection**: Handles connection drops gracefully
- **Data Caching**: Stores recent quotes and depth data
- **Event-driven Architecture**: Callback-based system for handling data
- **Symbol Management**: Easy subscription/unsubscription of symbols
- **Connection Monitoring**: Statistics and health monitoring

### 🔧 **Architecture**

The WebSocket implementation consists of three main components:

1. **`FyersWebSocketClient`** - Low-level WebSocket client
2. **`FyersWebSocketManager`** - High-level data manager with caching
3. **`FyersBroker`** - Integrated WebSocket methods in main broker class

## Quick Start

### 1. Setup Credentials in .env File

First, add your Fyers credentials to the `.env` file:

```bash
# Fyers API Credentials
FYERS_CLIENT_ID=KQL8MIGYUG-100
FYERS_SECRET_KEY=SBRPJJQ9JB
FYERS_REDIRECT_URI=https://127.0.0.1/
FYERS_FY_ID=FAA68054
FYERS_TOTP_KEY=VLGBENEEANGPPW653DRCAEPD6BE4MAEJ
FYERS_PIN=0099

# Fyers API Tokens (generated automatically)
FYERS_AUTH_CODE=your_auth_code_here
FYERS_ACCESS_TOKEN=your_access_token_here
FYERS_REFRESH_TOKEN=your_refresh_token_here
```

### 2. Generate Access Token

```bash
# Generate access token from auth code
python scripts/fyers_auth.py --generate-token

# Check token validity
python scripts/fyers_auth.py --check-token
```

### 3. Enable WebSocket

```python
from broker import FyersBroker

# Initialize broker (credentials loaded from .env automatically)
broker = FyersBroker()

# Enable WebSocket
if broker.enable_websocket():
    print("WebSocket enabled successfully!")
else:
    print("Failed to enable WebSocket")
```

### 4. Subscribe to Live Quotes

```python
# Subscribe to live quotes
symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ"]
success = broker.subscribe_live_quotes(symbols)

if success:
    print(f"Subscribed to {len(symbols)} symbols")
```

### 5. Set up Callbacks

```python
def on_quote_update(symbol, quote_data):
    print(f"Quote Update: {symbol}")
    print(f"  LTP: {quote_data.get('ltp')}")
    print(f"  Volume: {quote_data.get('vol_traded_today')}")
    print(f"  Change: {quote_data.get('ch')}")

def on_depth_update(symbol, depth_data):
    print(f"Depth Update: {symbol}")
    print(f"  Best Bid: {depth_data.get('bid')}")
    print(f"  Best Ask: {depth_data.get('ask')}")

# Add callbacks
broker.add_quote_callback(on_quote_update)
broker.add_depth_callback(on_depth_update)
```

### 6. Access Live Data

```python
# Get latest quote from cache
quote = broker.get_live_quote("NSE:SBIN-EQ")
if quote:
    print(f"Current LTP: {quote.get('ltp')}")

# Get latest market depth
depth = broker.get_live_depth("NSE:SBIN-EQ")
if depth:
    print(f"Bid: {depth.get('bid')}, Ask: {depth.get('ask')}")
```

## Complete Example

```python
#!/usr/bin/env python3
"""
Complete example of Fyers WebSocket usage.
"""

import time
from broker import FyersBroker

def main():
    # Initialize broker
    broker = FyersBroker(
        client_id="your_client_id",
        access_token="your_access_token"
    )

    # Enable WebSocket
    if not broker.enable_websocket():
        print("Failed to enable WebSocket")
        return

    # Define callback functions
    def on_quote(symbol, data):
        ltp = data.get('ltp', 'N/A')
        change = data.get('ch', 'N/A')
        print(f"📈 {symbol}: LTP={ltp}, Change={change}")

    def on_depth(symbol, data):
        bid = data.get('bid', 'N/A')
        ask = data.get('ask', 'N/A')
        print(f"📊 {symbol}: Bid={bid}, Ask={ask}")

    def on_order(data):
        order_id = data.get('id', 'N/A')
        status = data.get('status', 'N/A')
        print(f"📋 Order {order_id}: Status={status}")

    # Add callbacks
    broker.add_quote_callback(on_quote)
    broker.add_depth_callback(on_depth)
    broker.add_order_callback(on_order)

    # Subscribe to symbols
    symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ"]
    broker.subscribe_live_quotes(symbols)
    broker.subscribe_market_depth(symbols[:1])  # Depth for one symbol

    print("Streaming live data... Press Ctrl+C to stop")

    try:
        # Monitor for 60 seconds
        for i in range(60):
            time.sleep(1)

            # Print statistics every 10 seconds
            if i % 10 == 0:
                stats = broker.get_websocket_statistics()
                print(f"Stats: {stats['quotes_processed']} quotes, "
                      f"{stats['depths_processed']} depths processed")

    except KeyboardInterrupt:
        print("Stopping...")

    finally:
        # Cleanup
        broker.disable_websocket()
        print("WebSocket disabled")

if __name__ == "__main__":
    main()
```

## API Reference

### Broker WebSocket Methods

#### Connection Management

```python
# Enable WebSocket
broker.enable_websocket() -> bool

# Disable WebSocket
broker.disable_websocket() -> None

# Check connection status
broker.is_websocket_connected() -> bool
```

#### Subscription Management

```python
# Subscribe to live quotes
broker.subscribe_live_quotes(symbols: List[str]) -> bool

# Subscribe to market depth
broker.subscribe_market_depth(symbols: List[str]) -> bool

# Unsubscribe from quotes
broker.unsubscribe_live_quotes(symbols: List[str]) -> bool

# Unsubscribe from depth
broker.unsubscribe_market_depth(symbols: List[str]) -> bool

# Get subscribed symbols
broker.get_subscribed_symbols() -> Dict[str, List[str]]
```

#### Data Access

```python
# Get latest quote
broker.get_live_quote(symbol: str) -> Optional[Dict[str, Any]]

# Get latest depth
broker.get_live_depth(symbol: str) -> Optional[Dict[str, Any]]

# Get statistics
broker.get_websocket_statistics() -> Dict[str, Any]
```

#### Callback Management

```python
# Add quote callback
broker.add_quote_callback(callback: Callable[[str, Dict], None])

# Add depth callback
broker.add_depth_callback(callback: Callable[[str, Dict], None])

# Add order callback
broker.add_order_callback(callback: Callable[[Dict], None])
```

### Data Formats

#### Quote Data Format
```python
{
    "symbol": "NSE:SBIN-EQ",
    "ltp": 542.50,                    # Last traded price
    "open_price": 540.00,             # Opening price
    "high_price": 545.00,             # Day high
    "low_price": 538.00,              # Day low
    "prev_close_price": 541.00,       # Previous close
    "ch": 1.50,                       # Change
    "chp": 0.28,                      # Change percentage
    "vol_traded_today": 1250000,      # Volume traded
    "oi": 0,                          # Open interest (for F&O)
    "timestamp": 1640995200           # Unix timestamp
}
```

#### Depth Data Format
```python
{
    "symbol": "NSE:SBIN-EQ",
    "bid": [
        {"price": 542.45, "size": 100},
        {"price": 542.40, "size": 200},
        # ... up to 5 levels
    ],
    "ask": [
        {"price": 542.50, "size": 150},
        {"price": 542.55, "size": 300},
        # ... up to 5 levels
    ],
    "timestamp": 1640995200
}
```

#### Order Update Format
```python
{
    "id": "order_id_123",
    "symbol": "NSE:SBIN-EQ",
    "status": "COMPLETE",
    "qty": 100,
    "filled_qty": 100,
    "price": 542.50,
    "side": 1,                        # 1=Buy, -1=Sell
    "product_type": "CNC",
    "timestamp": 1640995200
}
```

## Error Handling

### Common Issues and Solutions

#### 1. Authentication Errors
```python
# Error: "Please provide valid token"
# Solution: Ensure valid access token
broker = FyersBroker(
    client_id="valid_client_id",
    access_token="valid_access_token"  # Must be valid and not expired
)
```

#### 2. Subscription Failures
```python
# Error: Symbol subscription failed
# Solution: Check symbol format and connection
if broker.is_websocket_connected():
    # Use correct Fyers symbol format
    symbols = ["NSE:SBIN-EQ"]  # Not just "SBIN"
    broker.subscribe_live_quotes(symbols)
```

#### 3. Connection Issues
```python
# Error: WebSocket connection failed
# Solution: Check network and credentials
try:
    if broker.enable_websocket():
        print("Connected successfully")
    else:
        print("Connection failed - check credentials")
except Exception as e:
    print(f"Connection error: {e}")
```

## Performance Considerations

### 1. Symbol Limits
- **Quotes**: Up to 50 symbols recommended
- **Depth**: Up to 10 symbols recommended
- **Memory**: Each symbol uses ~1KB cache per minute

### 2. Callback Performance
```python
# Good: Fast callback
def fast_callback(symbol, data):
    # Quick processing only
    self.latest_price = data.get('ltp')

# Avoid: Slow operations in callbacks
def slow_callback(symbol, data):
    # Don't do this in callbacks
    time.sleep(1)  # Blocks other data
    database.save(data)  # Use separate thread
```

### 3. Data Management
```python
# Clear cache periodically to manage memory
broker.ws_manager.clear_cache()  # Clear all
broker.ws_manager.clear_cache("NSE:SBIN-EQ")  # Clear specific symbol
```

## Testing

### Test with Dummy Credentials
```python
# The test script works with dummy credentials for testing structure
python test_fyers_websocket.py

# Expected output:
# ✓ WebSocket connection successful (structure test)
# ✗ Subscription failed (expected with dummy credentials)
```

### Test with Real Credentials
```python
# Update credentials in test script for real testing
broker = FyersBroker(
    client_id="your_real_client_id",
    access_token="your_real_access_token"
)
```

## Troubleshooting

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed logging
broker.enable_websocket()
```

### Connection Status
```python
# Check connection details
print(f"Connected: {broker.is_websocket_connected()}")
print(f"Subscribed: {broker.get_subscribed_symbols()}")
print(f"Stats: {broker.get_websocket_statistics()}")
```

### Manual Reconnection
```python
# Force reconnection
broker.disable_websocket()
time.sleep(2)
broker.enable_websocket()
```

## Next Steps

1. **Implement with Real Credentials**: Replace dummy credentials with real Fyers API credentials
2. **Add More Symbols**: Populate the symbol mapping file with more instruments
3. **Integrate with Strategies**: Use live data in trading strategies
4. **Add Alerts**: Set up price alerts based on live data
5. **Historical Integration**: Combine with historical data for complete analysis

## Support

For issues or questions:
1. Check the test output for connection status
2. Verify Fyers API credentials are valid
3. Ensure symbols are in correct Fyers format
4. Check network connectivity
5. Review logs for detailed error messages
