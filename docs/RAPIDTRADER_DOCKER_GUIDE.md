# RapidTrader Docker Guide

## 🚀 Complete Docker Setup for RapidTrader with Fyers Integration

This guide shows you how to use RapidTrader with <PERSON><PERSON>, similar to FreqTrade, for both dry-run and live trading with Fyers broker integration.

## 📋 Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Fyers trading account with API access
- Basic understanding of trading concepts

## 🔧 Quick Setup

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd rapidtrader

# Make scripts executable
chmod +x scripts/rapidtrader-docker.sh

# Setup Fyers credentials
./scripts/rapidtrader-docker.sh fyers-setup
```

### 2. Configure Credentials

Edit the `.env` file with your Fyers credentials:

```bash
# Fyers API Credentials
FYERS_CLIENT_ID=your_client_id_here
FYERS_SECRET_KEY=your_secret_key_here
FYERS_REDIRECT_URI=https://127.0.0.1/
FYERS_FY_ID=your_fy_id_here
FYERS_TOTP_KEY=your_totp_key_here
FYERS_PIN=your_pin_here

# Trading Configuration
DRY_RUN_ENABLED=true
LIVE_DATA_ENABLED=true
```

### 3. Generate Access Token

```bash
./scripts/rapidtrader-docker.sh fyers-setup
```

## 🎯 Usage Examples

### FreqTrade vs RapidTrader Commands

| FreqTrade | RapidTrader | Description |
|-----------|-------------|-------------|
| `freqtrade trade` | `./scripts/rapidtrader-docker.sh fyers-dryrun` | Start dry-run trading |
| `freqtrade trade --live` | `./scripts/rapidtrader-docker.sh fyers-live` | Start live trading |
| `freqtrade backtesting` | `./scripts/rapidtrader-docker.sh backtest` | Run backtesting |
| `freqtrade webserver` | `./scripts/rapidtrader-docker.sh web` | Start web interface |
| `freqtrade plot-dataframe` | `./scripts/rapidtrader-docker.sh web` | View charts (included in web) |

### Basic Commands

#### Start Dry-Run Trading
```bash
# Start Fyers dry-run trading
./scripts/rapidtrader-docker.sh fyers-dryrun

# Start with web interface
./scripts/rapidtrader-docker.sh fyers-web-trading
```

#### Start Live Trading
```bash
# Start Fyers live trading (be careful!)
./scripts/rapidtrader-docker.sh fyers-live
```

#### Web Interface Only
```bash
# Start web dashboard (like FreqUI)
./scripts/rapidtrader-docker.sh web

# Access at: http://localhost:8080
```

#### Testing and Monitoring
```bash
# Test Fyers connection
./scripts/rapidtrader-docker.sh fyers-test

# Show Fyers profile
./scripts/rapidtrader-docker.sh fyers-profile

# Test WebSocket connection
./scripts/rapidtrader-docker.sh fyers-websocket

# View logs
./scripts/rapidtrader-docker.sh logs -f

# Check status
./scripts/rapidtrader-docker.sh status
```

#### Management Commands
```bash
# Stop all services
./scripts/rapidtrader-docker.sh stop

# Build images
./scripts/rapidtrader-docker.sh build

# Clean up
./scripts/rapidtrader-docker.sh clean

# Interactive shell
./scripts/rapidtrader-docker.sh shell
```

## 🌐 Web Interface (RapidUI)

RapidTrader includes a comprehensive web interface similar to FreqTrade's FreqUI:

### Features
- **Real-time Dashboard**: Live trading status and portfolio monitoring
- **Market Data**: Real-time price feeds via WebSocket
- **Trading Controls**: Start/stop trading, switch modes
- **Portfolio View**: Account balance, positions, P&L
- **Performance Charts**: Visual performance tracking
- **Activity Logs**: Real-time trading activity

### Access
```bash
# Start web interface
./scripts/rapidtrader-docker.sh web

# Open browser to: http://localhost:8080
```

### Screenshots
The web interface provides:
- Trading status with real-time updates
- Portfolio summary with live balance
- Market data with live price feeds
- Performance charts
- Trading controls (start/stop/mode switching)

## 📊 Configuration Files

### Fyers Dry-Run Configuration
Location: `userdata/configs/fyers-config.json`

Key settings:
```json
{
    "trading": {
        "dry_run": true,
        "stake_amount": 1000,
        "max_open_trades": 3
    },
    "broker": {
        "name": "fyers",
        "websocket_enabled": true
    }
}
```

### Fyers Live Configuration
Location: `userdata/configs/fyers-live-config.json`

Key differences:
```json
{
    "trading": {
        "dry_run": false,
        "stake_amount": 1000
    },
    "protections": [
        {
            "method": "StoplossGuard",
            "trade_limit": 2
        }
    ]
}
```

## 🔄 Docker Services

### Available Services

| Service | Description | Command |
|---------|-------------|---------|
| `web` | Web interface only | `--profile web` |
| `fyers-dryrun` | Fyers dry-run trading | `--profile fyers-dryrun` |
| `fyers-live` | Fyers live trading | `--profile fyers-live` |
| `fyers-web-trading` | Combined web + trading | `--profile fyers-web-trading` |
| `backtest` | Backtesting service | `--profile backtest` |
| `shell` | Interactive shell | `--profile shell` |

### Direct Docker Compose Usage

```bash
# Start specific service
docker-compose --profile fyers-dryrun up

# Start with build
docker-compose --profile fyers-dryrun up --build

# Start in background
docker-compose --profile fyers-dryrun up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🧪 Testing

### Complete Functionality Test
```bash
# Run comprehensive test
python test_rapidtrader_complete.py --broker fyers --test-websocket

# Test specific components
python test_rapidtrader_complete.py --test-cli --test-docker
```

### Individual Tests
```bash
# Test Fyers integration
python test_fyers_real_credentials.py

# Test WebSocket functionality
python test_fyers_websocket.py

# Test CLI functionality
./rapidtrader profile test --broker fyers
```

## 🛡️ Security and Risk Management

### Dry-Run Mode (Recommended for Testing)
- No real money at risk
- Full functionality testing
- Real market data
- Simulated order execution

### Live Trading Precautions
- Start with small amounts
- Use stop-loss protection
- Monitor regularly
- Test strategies thoroughly in dry-run first

### Configuration Security
- Keep `.env` file secure
- Use environment variables in production
- Regularly rotate API keys
- Monitor API usage

## 🔧 Troubleshooting

### Common Issues

#### Connection Problems
```bash
# Check credentials
./scripts/rapidtrader-docker.sh fyers-test

# Regenerate token
./scripts/rapidtrader-docker.sh fyers-setup
```

#### Docker Issues
```bash
# Rebuild images
./scripts/rapidtrader-docker.sh build

# Clean and restart
./scripts/rapidtrader-docker.sh clean
docker system prune -f
```

#### WebSocket Issues
```bash
# Test WebSocket connection
./scripts/rapidtrader-docker.sh fyers-websocket

# Check logs
./scripts/rapidtrader-docker.sh logs -f
```

### Log Analysis
```bash
# View all logs
./scripts/rapidtrader-docker.sh logs

# Follow logs in real-time
./scripts/rapidtrader-docker.sh logs -f

# Container-specific logs
docker-compose logs rapidtrader-fyers-dryrun
```

## 📈 Production Deployment

### Environment Setup
```bash
# Production environment variables
export DRY_RUN_ENABLED=false
export LOG_LEVEL=INFO
export FLASK_ENV=production

# Start production services
./scripts/rapidtrader-docker.sh fyers-live -d
```

### Monitoring
- Use the web interface for real-time monitoring
- Set up log aggregation
- Monitor API rate limits
- Track performance metrics

### Backup and Recovery
- Regular configuration backups
- Database backups (SQLite)
- Log archival
- Strategy version control

## 🆚 FreqTrade Comparison

### Similarities
- Docker-based deployment
- Web interface for monitoring
- Configuration-driven setup
- Dry-run and live modes
- Backtesting capabilities
- Strategy framework

### RapidTrader Advantages
- **Indian Market Focus**: Native support for Indian brokers
- **Real-time WebSocket**: Live market data streaming
- **Integrated Broker APIs**: Direct broker integration
- **Comprehensive Testing**: Built-in test suite
- **Modern UI**: React-like web interface

### Migration from FreqTrade
1. Export FreqTrade strategies
2. Adapt to RapidTrader strategy format
3. Configure Indian broker credentials
4. Test in dry-run mode
5. Deploy with Docker

## 📚 Additional Resources

- [Fyers API Documentation](https://myapi.fyers.in/docs/)
- [RapidTrader Strategy Guide](STRATEGY_GUIDE.md)
- [WebSocket Integration Guide](FYERS_WEBSOCKET_GUIDE.md)
- [Money Management Guide](MONEY_MANAGEMENT.md)

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test suite
3. Check logs for errors
4. Review configuration files
5. Test individual components

---

**Ready to start trading with RapidTrader? 🚀**

```bash
# Quick start command
./scripts/rapidtrader-docker.sh fyers-web-trading
```

Open http://localhost:8080 and start trading!
