# RapidTrader Money Management System

## Overview

The RapidTrader Money Management System is a **standalone, modular** money management solution that can be used by any trading system or external application. It provides comprehensive capital allocation, risk management, and position sizing capabilities with a clean, extensible interface.

### 🎯 **Standalone Module Design**

The money management system is now a completely separate module (`money_management/`) that can:
- **Work with any broker** through standardized interfaces
- **Integrate with external systems** easily
- **Operate independently** of RapidTrader core
- **Scale to multiple applications** simultaneously

## Key Features

### 🏦 **Real-time Balance Integration**
- Fetches live balance from DhanHQ broker
- Caches data to minimize API calls
- Works in both live and dry-run modes
- Automatic balance refresh every 5 minutes

### 💰 **Percentage-based Allocation**
- Allocate capital across unlimited strategies
- Automatic validation (total ≤ 100%)
- Dynamic reallocation capabilities
- Emergency reserve management

### 🛡️ **Risk Management**
- Per-trade risk limits (configurable %)
- Total portfolio risk monitoring
- Global stop-loss protection
- Position concentration limits
- Sector diversification controls

### 📊 **Position Sizing**
- Automatic position size calculation
- Risk-based sizing algorithms
- Entry price optimization
- Stop-loss integration

## Usage Methods

### 1. **Standalone Python Module**

```python
from money_management import MoneyManager, get_money_manager
from money_management.interfaces import SimpleBalanceProvider

# Basic usage
balance_provider = SimpleBalanceProvider(100000.0, 120000.0, 20000.0)
money_manager = MoneyManager(broker=balance_provider)

# Add strategy allocations
money_manager.add_strategy_allocation("momentum_strategy", 30.0)
money_manager.add_strategy_allocation("swing_strategy", 25.0)

# Calculate position sizes
position_info = money_manager.get_position_size("momentum_strategy", "RELIANCE", 2500.0)
print(f"Recommended quantity: {position_info['quantity']}")
```

### 2. **RapidTrader CLI Commands**

```bash
# Check money management status
rapidtrader money status

# Allocate 30% to a strategy
rapidtrader money allocate -s "momentum_strategy" -p 30

# Remove strategy allocation
rapidtrader money remove -s "old_strategy"

# Calculate position size
rapidtrader money position-size -s "swing_strategy" --symbol "RELIANCE" -p 2500

# Configure risk settings
rapidtrader money configure --max-risk-per-trade 3.0 --emergency-reserve 15.0

# Check rebalancing needs
rapidtrader money rebalance
```

### 3. **External System Integration**

```python
# Custom broker integration
class MyBroker:
    def get_fund_limit(self):
        return {"availableBalance": 150000.0, "totalBalance": 200000.0}

    def get_holdings(self):
        return [{"symbol": "RELIANCE", "quantity": 10, "avgCostPrice": 2400}]

    def get_positions(self):
        return []

    def is_dry_run(self):
        return True

# Use with money manager
money_manager = MoneyManager(broker=MyBroker())
```

### Advanced Configuration

```bash
# Set comprehensive risk parameters
rapidtrader money configure \
  --total-capital-usage 85.0 \
  --emergency-reserve 15.0 \
  --max-risk-per-trade 2.5 \
  --max-total-risk 12.0 \
  --stop-loss-global 18.0 \
  --auto-rebalance

# Calculate position with custom risk
rapidtrader money position-size \
  -s "scalping_strategy" \
  --symbol "INFY" \
  -p 1800 \
  --risk-percentage 1.5
```

## Configuration Files

### Main Configuration
**Location**: `userdata/config/money_management.json`

```json
{
    "global_settings": {
        "total_capital_usage": 90.0,
        "emergency_reserve": 10.0,
        "max_risk_per_trade": 2.0,
        "max_total_risk": 15.0,
        "stop_loss_global": 20.0
    },
    "allocation_model": {
        "strategies": {
            "momentum_strategy": 30.0,
            "swing_strategy": 25.0,
            "scalping_strategy": 20.0
        }
    }
}
```

### Template Configuration
**Location**: `userdata/config/money_management_template.json`

Contains example configurations for:
- Conservative profile (70% usage, 1% risk)
- Moderate profile (85% usage, 2% risk)
- Aggressive profile (95% usage, 3% risk)

## Risk Profiles

### Conservative (Low Risk)
```bash
rapidtrader money configure \
  --total-capital-usage 70.0 \
  --emergency-reserve 30.0 \
  --max-risk-per-trade 1.0 \
  --max-total-risk 8.0 \
  --stop-loss-global 15.0
```

### Moderate (Balanced Risk)
```bash
rapidtrader money configure \
  --total-capital-usage 85.0 \
  --emergency-reserve 15.0 \
  --max-risk-per-trade 2.0 \
  --max-total-risk 12.0 \
  --stop-loss-global 20.0
```

### Aggressive (High Risk)
```bash
rapidtrader money configure \
  --total-capital-usage 95.0 \
  --emergency-reserve 5.0 \
  --max-risk-per-trade 3.0 \
  --max-total-risk 20.0 \
  --stop-loss-global 25.0
```

## Example Workflow

### 1. Initial Setup
```bash
# Check current balance and status
rapidtrader money status

# Configure risk profile (moderate)
rapidtrader money configure \
  --total-capital-usage 85.0 \
  --emergency-reserve 15.0 \
  --max-risk-per-trade 2.0
```

### 2. Strategy Allocation
```bash
# Allocate to different strategies
rapidtrader money allocate -s "momentum_strategy" -p 35
rapidtrader money allocate -s "swing_strategy" -p 30
rapidtrader money allocate -s "scalping_strategy" -p 20
rapidtrader money allocate -s "long_term_strategy" -p 15

# Check updated allocations
rapidtrader money status
```

### 3. Position Sizing
```bash
# Calculate position for momentum strategy
rapidtrader money position-size \
  -s "momentum_strategy" \
  --symbol "RELIANCE" \
  -p 2500

# Output shows:
# - Recommended quantity: 5 shares
# - Position value: ₹12,500
# - Risk amount: ₹1,050
# - Allocation used: 8.7%
```

### 4. Ongoing Management
```bash
# Regular rebalancing check
rapidtrader money rebalance

# Adjust allocations as needed
rapidtrader money allocate -s "momentum_strategy" -p 40
rapidtrader money remove -s "old_strategy"
```

## Integration with Strategies

The money management system integrates seamlessly with RapidTrader strategies:

1. **Strategy Files**: Place in `userdata/strategies/`
2. **Configuration**: Use `rapidtrader money allocate` to assign capital
3. **Position Sizing**: Automatic calculation based on allocation
4. **Risk Management**: Built-in limits prevent over-exposure

## Safety Features

### Automatic Protections
- **Total allocation validation**: Cannot exceed 100%
- **Emergency reserve**: Always maintains cash buffer
- **Global stop-loss**: Halts trading at loss threshold
- **Position limits**: Maximum positions per strategy
- **Concentration limits**: Prevents over-exposure to single stocks

### Manual Overrides
- Custom risk percentages per trade
- Manual rebalancing controls
- Strategy-specific position limits
- Emergency allocation removal

## Monitoring and Alerts

The system provides comprehensive monitoring:

- **Real-time balance tracking**
- **Allocation drift detection**
- **Risk limit violations**
- **Rebalancing recommendations**
- **Performance attribution**

## Best Practices

1. **Start Conservative**: Begin with lower risk settings
2. **Regular Monitoring**: Check status daily
3. **Gradual Scaling**: Increase allocations as confidence grows
4. **Diversification**: Spread across multiple strategies
5. **Emergency Planning**: Always maintain cash reserves

## Troubleshooting

### Common Issues

**Balance not updating**: Check broker connection and API credentials
**Allocation errors**: Verify total doesn't exceed 100%
**Position sizing issues**: Ensure strategy exists and has allocation
**Configuration problems**: Check JSON syntax in config files

### Support Commands
```bash
# Check system status
rapidtrader profile show

# Verify broker connection
rapidtrader profile test

# View detailed logs
tail -f userdata/logs/engine.log
```

## Advanced Features

### Custom Position Sizing
- Volatility-based sizing
- Correlation monitoring
- Sector concentration limits
- Market regime detection

### Portfolio Analytics
- Risk attribution analysis
- Performance tracking
- Drawdown monitoring
- Rebalancing optimization

The RapidTrader Money Management System ensures disciplined, risk-controlled trading while maximizing capital efficiency across your strategy portfolio.
