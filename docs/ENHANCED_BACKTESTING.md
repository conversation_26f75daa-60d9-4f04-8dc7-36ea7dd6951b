# Enhanced Backtesting System for RapidTrader

The Enhanced Backtesting System provides automated Docker container deployment with intelligent data management, dynamic container naming, and automatic result storage.

## 🚀 Features

### ✅ **Automatic Container Deployment**
- Deploys dedicated Docker containers for each backtest
- Dynamic container naming: `backtest-{symbol}-{config}-{strategy}-{timestamp}`
- Auto-stop containers after completion
- Isolated execution environment

### ✅ **Intelligent Data Management**
- Automatic data downloading if data doesn't exist
- Updates existing data to latest available
- Supports multiple symbols and timeframes
- Handles data validation and error recovery

### ✅ **Result Management**
- Automatic result storage with metadata
- Timestamped result files
- JSON format for easy analysis
- Results stored in `userdata/results/backtests/`

### ✅ **Container Naming Convention**
```
backtest-{symbols}-{config}-{strategy}-{timestamp}

Examples:
- backtest-reliance-tcs-fyers-config-aggressivema-20241127-143022
- backtest-infy-plus2-backtest-config-defaultstrategy-20241127-143045
```

## 🎯 Usage Methods

### Method 1: CLI Command
```bash
# Basic enhanced backtest
python -m core.rapidtrader backtest enhanced -s MyStrategy -c my-config

# With specific symbols and timeframe
python -m core.rapidtrader backtest enhanced \
  -s AggressiveMA \
  -c fyers-config \
  -y RELIANCE,TCS,INFY \
  -t 1h

# With time range
python -m core.rapidtrader backtest enhanced \
  -s MyStrategy \
  -c backtest-config \
  -r 20240101-20240630
```

### Method 2: Shell Script (Recommended)
```bash
# Basic usage
./scripts/run-enhanced-backtest.sh --strategy MyStrategy --config my-config

# Full example
./scripts/run-enhanced-backtest.sh \
  --strategy AggressiveMA \
  --config fyers-config \
  --symbols RELIANCE,TCS,INFY \
  --timeframe 1h \
  --timerange 20240101-20240331
```

### Method 3: Docker Compose
```bash
# Set environment variables
export STRATEGY="MyStrategy"
export CONFIG="my-config"
export SYMBOLS="RELIANCE,TCS"
export TIMEFRAME="1h"
export BACKTEST_CONTAINER_NAME="backtest-custom-name"

# Run enhanced backtest
docker-compose --profile enhanced-backtest up
```

### Method 4: Python Script
```bash
# Using the enhanced backtest runner
python scripts/enhanced_backtest_runner.py \
  --strategy MyStrategy \
  --config my-config \
  --symbols RELIANCE TCS INFY \
  --timeframe 1h
```

## 📋 Parameters

### Required Parameters
- `--strategy` / `-s`: Strategy name to use
- `--config` / `-c`: Configuration file name (without .json)

### Optional Parameters
- `--symbols` / `-y`: Comma-separated symbols (default: from config)
- `--timeframe` / `-t`: Data timeframe (default: 1d)
- `--timerange` / `-r`: Time range YYYYMMDD-YYYYMMDD
- `--no-update`: Skip automatic data update

## 🔄 Workflow

### 1. **Container Deployment**
```
🚀 Starting Enhanced Backtest
📦 Generating container name: backtest-reliance-tcs-fyers-config-aggressivema-20241127-143022
🐳 Deploying Docker container...
```

### 2. **Data Management**
```
📊 Downloading/updating data...
  📈 Checking data for RELIANCE...
    ✅ Data ready for RELIANCE
  📈 Checking data for TCS...
    ✅ Data ready for TCS
📊 Data preparation complete
```

### 3. **Backtest Execution**
```
🏃 Running backtest...
📈 Loading strategy: AggressiveMA
📊 Processing 2 symbols from 2024-01-01 to 2024-12-31
⚡ Backtest completed in 45.2 seconds
```

### 4. **Result Storage**
```
💾 Storing results...
📁 Results saved to: userdata/results/backtests/backtest_AggressiveMA_RELIANCE-TCS_20241127_143022.json
✅ Enhanced backtest completed - container will auto-stop
```

## 📊 Result Format

Results are stored in JSON format with metadata:

```json
{
  "metadata": {
    "container_name": "backtest-reliance-tcs-fyers-config-aggressivema-20241127-143022",
    "strategy": "AggressiveMA",
    "symbols": ["RELIANCE", "TCS"],
    "timestamp": "20241127_143022",
    "backtest_date": "2024-11-27T14:30:22.123456"
  },
  "results": {
    "total_return_pct": 15.67,
    "total_trades": 45,
    "win_rate": 62.22,
    "total_profit_loss": 7835.50,
    "max_drawdown_pct": -8.45,
    "sharpe_ratio": 1.23,
    "...": "..."
  }
}
```

## 🐳 Container Management

### Container Lifecycle
1. **Creation**: Dynamic name generation based on parameters
2. **Execution**: Isolated environment with mounted userdata
3. **Monitoring**: Real-time log streaming
4. **Completion**: Automatic result storage
5. **Cleanup**: Auto-stop and remove container

### Container Features
- **Isolation**: Each backtest runs in its own container
- **Resource Management**: Controlled CPU and memory usage
- **Data Persistence**: Results stored in mounted volumes
- **Network Access**: For data downloading
- **Auto-cleanup**: No manual container management needed

## 📁 File Structure

```
userdata/
├── config/
│   ├── backtest-config.json
│   ├── fyers-config.json
│   └── my-config.json
├── results/
│   └── backtests/
│       ├── backtest_AggressiveMA_RELIANCE-TCS_20241127_143022.json
│       ├── backtest_DefaultStrategy_INFY_20241127_144512.json
│       └── ...
└── historical_data/
    └── yfinance/
        ├── NSE_RELIANCE_1d.csv
        ├── NSE_TCS_1d.csv
        └── ...
```

## ⚙️ Configuration

### Backtest Configuration Example
```json
{
  "strategy": {
    "name": "AggressiveMA",
    "params": {
      "short_ma_period": 5,
      "long_ma_period": 20
    }
  },
  "exchange": {
    "pair_whitelist": ["RELIANCE", "TCS", "HDFCBANK"]
  },
  "backtest": {
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "initial_balance": 100000,
    "fee": 0.001
  },
  "timeframe": "1d"
}
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Docker Image Not Found
```bash
# Build the RapidTrader image
docker-compose build
```

#### 2. Config File Not Found
```bash
# Check available configs
ls userdata/config/*.json
ls userdata/configs/*.json
```

#### 3. Data Download Failures
```bash
# Manual data download
python -m core.rapidtrader data download --symbols RELIANCE --timeframe 1d
```

#### 4. Container Name Conflicts
The system automatically generates unique names with timestamps to avoid conflicts.

### Debug Mode
```bash
# Run with verbose output
./scripts/run-enhanced-backtest.sh --strategy MyStrategy --config my-config --verbose
```

## 🚀 Advanced Usage

### Batch Backtesting
```bash
# Run multiple backtests
for strategy in AggressiveMA ConservativeMA; do
  ./scripts/run-enhanced-backtest.sh --strategy $strategy --config fyers-config
done
```

### Custom Container Names
```bash
export BACKTEST_CONTAINER_NAME="my-custom-backtest-name"
docker-compose --profile enhanced-backtest up
```

### Integration with CI/CD
```yaml
# GitHub Actions example
- name: Run Enhanced Backtest
  run: |
    ./scripts/run-enhanced-backtest.sh \
      --strategy ${{ matrix.strategy }} \
      --config production-config \
      --timerange 20240101-20240331
```

## 📈 Performance

### Optimization Tips
1. **Use appropriate timeframes**: Higher timeframes = faster execution
2. **Limit symbols**: Start with 1-3 symbols for testing
3. **Optimize date ranges**: Use specific timeranges for faster testing
4. **Resource allocation**: Ensure sufficient Docker resources

### Monitoring
- Real-time log streaming during execution
- Container resource usage monitoring
- Automatic performance metrics in results

## 🎯 Next Steps

After running enhanced backtests:

1. **Analyze Results**: Review JSON files in `userdata/results/backtests/`
2. **Compare Strategies**: Use result comparison tools
3. **Optimize Parameters**: Run parameter optimization
4. **Deploy Live**: Move successful strategies to live trading

## 📚 Related Documentation

- [Basic Backtesting](BACKTESTING.md)
- [Docker Guide](DOCKER_QUICKSTART.md)
- [Strategy Development](../userdata/strategies/README.md)
- [Configuration Management](../config/README.md)
