# 🔄 RapidTrader Backtesting Engine: Complete Flowchart

## 📊 Overview

This document provides a comprehensive flowchart of how the RapidTrader backtesting engine works, including all decision points, conditions, and optimization paths.

---

## 🚀 Main Backtesting Flow

```mermaid
flowchart TD
    A[🎯 User Runs Backtest Command] --> B{CLI Arguments Check}
    B -->|--optimized flag| C[🚀 Use OptimizedBacktestEngine]
    B -->|Standard| D[📊 Use BacktestEngine]

    C --> E[Load Configuration]
    D --> E

    E --> F{Configuration Valid?}
    F -->|❌ No| G[❌ Display Error & Exit]
    F -->|✅ Yes| H[Parse Timerange & Symbols]

    H --> I{Symbols Available?}
    I -->|❌ No| J[❌ Error: No symbols in pair_whitelist]
    I -->|✅ Yes| K[Initialize Engine Components]

    K --> L[🔄 Start Strategy Processing Loop]

    L --> M{More Strategies?}
    M -->|❌ No| N[📊 Display Results]
    M -->|✅ Yes| O[Load Next Strategy]

    O --> P{Strategy Load Success?}
    P -->|❌ No| Q[⚠️ Skip Strategy & Log Error]
    P -->|✅ Yes| R[🎯 Execute Backtest for Strategy]

    Q --> M
    R --> S[📈 Calculate Strategy Results]
    S --> M

    N --> T[💾 Save Results to JSON]
    T --> U[✅ Backtest Complete]
```

---

## 🎯 Strategy Execution Flow

```mermaid
flowchart TD
    A[🎯 Execute Backtest for Strategy] --> B[Initialize Components]

    B --> C[📊 TradeSimulator.reset()]
    C --> D[📈 BacktestResults.reset()]
    D --> E[🔄 Data Availability Check]

    E --> F{Data Manager Check}
    F -->|Missing Data| G[📥 Auto Download Data]
    F -->|Data Available| H[📂 Load Historical Data]

    G --> I{Download Success?}
    I -->|❌ No| J[⚠️ Skip Symbol & Continue]
    I -->|✅ Yes| K[🔄 Convert Data Format]

    K --> H
    H --> L[🔄 Symbol Processing Loop]

    L --> M{More Symbols?}
    M -->|❌ No| N[📊 Calculate Final Results]
    M -->|✅ Yes| O[📈 Process Next Symbol]

    O --> P{Optimized Engine?}
    P -->|✅ Yes| Q[🚀 Vectorized Processing]
    P -->|❌ No| R[📊 Standard Processing]

    Q --> S[🎯 Strategy Analysis]
    R --> S

    S --> T{Analysis Success?}
    T -->|❌ No| U[⚠️ Log Error & Skip Symbol]
    T -->|✅ Yes| V[💹 Trade Simulation]

    U --> M
    V --> W[📊 Collect Symbol Trades]
    W --> M

    N --> X[📈 Return Strategy Results]
```

---

## 📥 Data Management Flow

```mermaid
flowchart TD
    A[🔍 Data Availability Check] --> B[🔄 Check Each Symbol]

    B --> C{Symbol Data Exists?}
    C -->|✅ Yes| D[📅 Check Date Coverage]
    C -->|❌ No| E[📝 Add to Missing List]

    D --> F{Coverage Complete?}
    F -->|✅ Yes| G[✅ Symbol Ready]
    F -->|❌ No| H[📝 Add to Insufficient List]

    E --> I{More Symbols?}
    H --> I
    G --> I

    I -->|✅ Yes| B
    I -->|❌ No| J{Missing Data Found?}

    J -->|❌ No| K[✅ All Data Available]
    J -->|✅ Yes| L[📥 Start Download Process]

    L --> M[🔄 Download Loop]
    M --> N{More Symbols to Download?}
    N -->|❌ No| O[📊 Download Summary]
    N -->|✅ Yes| P[📥 Download Next Symbol]

    P --> Q[🎯 Try YFinanceFetcher]
    Q --> R{YFinance Success?}
    R -->|✅ Yes| S[🔄 Convert Format]
    R -->|❌ No| T[🎯 Try download_data Module]

    T --> U{download_data Success?}
    U -->|✅ Yes| V[🔄 Convert from yfinance Format]
    U -->|❌ No| W[❌ Log Download Failure]

    S --> X[✅ Symbol Download Complete]
    V --> X
    W --> N
    X --> N

    O --> Y{Any Downloads Successful?}
    Y -->|✅ Yes| K
    Y -->|❌ No| Z[⚠️ Warning: Some Data Missing]

    Z --> K
```

---

## 🎯 Strategy Analysis Flow

```mermaid
flowchart TD
    A[🎯 Strategy Analysis] --> B{Engine Type?}
    B -->|🚀 Optimized| C[📊 In-Place DataFrame Operations]
    B -->|📊 Standard| D[📋 Copy DataFrame]

    C --> E[🔧 Optimize Data Types]
    D --> F[📈 Standard Data Processing]

    E --> G[📊 strategy.analyze(dataframe)]
    F --> G

    G --> H[🔧 populate_indicators()]
    H --> I[📈 Calculate Technical Indicators]

    I --> J{Optimized Indicators?}
    J -->|✅ Yes| K[⚡ Vectorized numpy Operations]
    J -->|❌ No| L[📊 Standard pandas Operations]

    K --> M[💾 Cache Indicators]
    L --> M

    M --> N[🎯 populate_buy_signals()]
    N --> O[🔍 Generate Buy Conditions]

    O --> P{Multiple Conditions?}
    P -->|✅ Yes| Q[🔗 Combine with Boolean Logic]
    P -->|❌ No| R[📊 Single Condition Check]

    Q --> S[📊 Apply Filters & Validation]
    R --> S

    S --> T[🎯 populate_sell_signals()]
    T --> U[🔍 Generate Sell Conditions]

    U --> V{Multiple Conditions?}
    V -->|✅ Yes| W[🔗 Combine with Boolean Logic]
    V -->|❌ No| X[📊 Single Condition Check]

    W --> Y[📊 Apply Filters & Validation]
    X --> Y

    Y --> Z[✅ Return Analyzed DataFrame]
    Z --> AA[📊 DataFrame with buy_signal & sell_signal columns]
```

---

## 💹 Trade Simulation Flow

```mermaid
flowchart TD
    A[💹 Trade Simulation] --> B{Engine Type?}
    B -->|🚀 Optimized| C[⚡ Vectorized Trade Simulation]
    B -->|📊 Standard| D[🔄 Row-by-Row Processing]

    C --> E[📊 Convert to numpy Arrays]
    D --> F[🔄 Iterate Through DataFrame]

    E --> G[🔍 Find Signal Indices]
    F --> H[📈 Check Current Row]

    G --> I[🔗 Combine & Sort Signals]
    H --> J{Buy Signal?}

    I --> K[🔄 Process Signal Loop]
    J -->|✅ Yes| L{Position Open?}
    J -->|❌ No| M{Sell Signal?}

    K --> N{More Signals?}
    L -->|❌ No| O[🎯 Attempt Trade Entry]
    L -->|✅ Yes| P[⚠️ Skip - Position Already Open]

    M -->|✅ Yes| Q{Position Open?}
    M -->|❌ No| R[➡️ Continue to Next Row]

    Q -->|✅ Yes| S[🎯 Attempt Trade Exit]
    Q -->|❌ No| T[⚠️ Skip - No Position to Close]

    N -->|❌ No| U[🔄 Close Remaining Positions]
    N -->|✅ Yes| V{Signal Type?}

    V -->|📈 Buy| O
    V -->|📉 Sell| S

    O --> W[🔍 Check Trading Conditions]
    S --> X[🔍 Validate Exit Conditions]

    P --> R
    T --> R
    R --> H

    W --> Y{Can Open Trade?}
    Y -->|✅ Yes| Z[💰 Execute Trade Entry]
    Y -->|❌ No| AA[⚠️ Skip - Cannot Open Trade]

    X --> BB{Valid Exit?}
    BB -->|✅ Yes| CC[💰 Execute Trade Exit]
    BB -->|❌ No| DD[⚠️ Skip - Invalid Exit]

    Z --> EE[📊 Update Trade Records]
    CC --> EE
    AA --> FF[➡️ Continue Processing]
    DD --> FF

    EE --> FF
    FF --> N
    FF --> H

    U --> GG[📊 Return All Trades]
```

---

## 💰 Trade Execution Details

```mermaid
flowchart TD
    A[💰 Execute Trade Entry] --> B[📊 Calculate Quantity]
    B --> C[💱 Apply Entry Slippage]
    C --> D[💰 Calculate Trade Value]
    D --> E[💸 Calculate Entry Fee]
    E --> F[💵 Calculate Total Cost]

    F --> G{Sufficient Balance?}
    G -->|❌ No| H[⚠️ Insufficient Funds]
    G -->|✅ Yes| I[📝 Create Trade Record]

    I --> J[💰 Deduct from Balance]
    J --> K[📊 Update Fee Tracking]
    K --> L[📝 Store Open Position]
    L --> M[✅ Trade Entry Complete]

    H --> N[❌ Trade Entry Failed]

    O[💰 Execute Trade Exit] --> P[💱 Apply Exit Slippage]
    P --> Q[💰 Calculate Exit Value]
    Q --> R[💸 Calculate Exit Fee]
    R --> S[💵 Calculate Net Proceeds]

    S --> T[📊 Calculate P&L]
    T --> U[📈 Calculate P&L Percentage]
    U --> V[⏱️ Calculate Trade Duration]

    V --> W[📝 Update Trade Record]
    W --> X[💰 Add to Balance]
    X --> Y[📊 Update Fee Tracking]
    Y --> Z[🗑️ Remove Open Position]
    Z --> AA[✅ Trade Exit Complete]
```

---

## 📊 Results Calculation Flow

```mermaid
flowchart TD
    A[📊 Calculate Results] --> B[📈 Collect All Trades]
    B --> C{Any Trades?}
    C -->|❌ No| D[📊 Generate Zero Results]
    C -->|✅ Yes| E[📊 Calculate Basic Metrics]

    E --> F[🔢 Total Trades Count]
    F --> G[🏆 Win/Loss Analysis]
    G --> H[💰 P&L Calculations]
    H --> I[📈 Return Calculations]
    I --> J[📉 Drawdown Analysis]

    J --> K[⏱️ Duration Analysis]
    K --> L[💸 Fee Analysis]
    L --> M[📊 Risk Metrics]
    M --> N[📈 Performance Ratios]

    N --> O[📊 Compile Results Dictionary]
    O --> P[✅ Results Complete]

    D --> Q[📊 Empty Results Dictionary]
    Q --> P
```

---

## 🔧 Optimization Conditions

```mermaid
flowchart TD
    A[🔧 Optimization Check] --> B{--optimized Flag?}
    B -->|❌ No| C[📊 Standard Engine Path]
    B -->|✅ Yes| D[🚀 Optimized Engine Path]

    D --> E[⚡ Vectorized Operations]
    E --> F[💾 Memory Optimization]
    F --> G[🔄 In-Place Processing]
    G --> H[📊 Batch Symbol Processing]
    H --> I[🗑️ Garbage Collection]

    C --> J[🔄 Row-by-Row Processing]
    J --> K[📋 DataFrame Copying]
    K --> L[📊 Sequential Symbol Processing]

    I --> M[🎯 Strategy Analysis]
    L --> M

    M --> N{Analysis Method?}
    N -->|🚀 Optimized| O[⚡ numpy Vectorized]
    N -->|📊 Standard| P[📊 pandas Standard]

    O --> Q[💾 float32 Data Types]
    P --> R[💾 float64 Data Types]

    Q --> S[🔄 Cached Indicators]
    R --> T[🔄 Recalculated Indicators]

    S --> U[⚡ Vectorized Trade Sim]
    T --> V[🔄 Iterative Trade Sim]

    U --> W[📊 Results]
    V --> W
```

---

## ⚠️ Error Handling Flow

```mermaid
flowchart TD
    A[⚠️ Error Detected] --> B{Error Type?}

    B -->|📁 File Not Found| C[📝 Log File Error]
    B -->|📊 Data Error| D[📝 Log Data Error]
    B -->|🎯 Strategy Error| E[📝 Log Strategy Error]
    B -->|💹 Trade Error| F[📝 Log Trade Error]
    B -->|🔧 Config Error| G[📝 Log Config Error]

    C --> H{Critical Error?}
    D --> H
    E --> H
    F --> H
    G --> H

    H -->|✅ Yes| I[❌ Stop Execution]
    H -->|❌ No| J[⚠️ Continue with Warning]

    I --> K[📊 Display Error Message]
    J --> L[📊 Display Warning]

    K --> M[🔄 Cleanup Resources]
    L --> N[➡️ Continue Processing]

    M --> O[❌ Exit with Error Code]
    N --> P[✅ Resume Normal Flow]
```

---

## 🎯 Key Decision Points Summary

### 1. **Engine Selection**
- `--optimized` flag → OptimizedBacktestEngine
- Standard → BacktestEngine

### 2. **Data Availability**
- Missing data → Auto-download
- Insufficient coverage → Download additional data
- Download failure → Skip symbol with warning

### 3. **Strategy Processing**
- Optimized → Vectorized, in-place operations
- Standard → Row-by-row, DataFrame copying

### 4. **Trade Execution**
- Buy signal + No position → Attempt entry
- Sell signal + Open position → Attempt exit
- Insufficient balance → Skip trade
- Invalid conditions → Skip with warning

### 5. **Error Handling**
- Critical errors → Stop execution
- Non-critical errors → Continue with warning
- Missing data → Auto-download or skip

### 6. **Results Generation**
- Trades available → Full metrics calculation
- No trades → Zero results with warnings
- Errors → Partial results with error flags

This comprehensive flowchart shows how RapidTrader's backtesting engine handles all scenarios, from simple successful runs to complex error conditions and optimization paths.

---

## 🔍 Detailed Code-Level Flow

### 📊 Core Engine Components

```python
# Main Entry Point
def run_backtest(strategies, config_path, timerange=None, timeframe=None, optimized=False):
    """
    Main backtest execution function

    Flow:
    1. Load configuration from JSON
    2. Parse timerange and symbols
    3. Initialize appropriate engine (optimized vs standard)
    4. Process each strategy
    5. Display and save results
    """

    # Configuration Loading
    with open(config_path, 'r') as f:
        config = json.load(f)

    # Engine Selection
    if optimized:
        backtest_engine = OptimizedBacktestEngine(config)
    else:
        backtest_engine = BacktestEngine(config)

    # Strategy Processing Loop
    for strategy_config in strategies:
        strategy = load_strategy(strategy_config['name'], strategy_path, config)
        results = backtest_engine.run_backtest(strategy, symbols, timeframe, start_date, end_date)
        all_results[strategy_name] = results
```

### 🎯 Strategy Analysis Pipeline

```python
# Standard Engine Analysis
def _simulate_trades(self, symbol, data):
    """
    Row-by-row trade simulation

    Flow:
    1. Initialize empty trades list
    2. Iterate through each row
    3. Check buy/sell signals
    4. Execute trades via TradeSimulator
    5. Return completed trades
    """
    trades = []
    open_position = None

    for timestamp, row in data.iterrows():
        # Buy Signal Processing
        if row.get('buy_signal', 0) == 1 and open_position is None:
            if self.trade_simulator.can_open_trade():
                open_position = self.trade_simulator.open_trade(
                    symbol=symbol,
                    timestamp=timestamp,
                    price=row['close'],
                    quantity=self._calculate_quantity(row['close'])
                )

        # Sell Signal Processing
        elif row.get('sell_signal', 0) == 1 and open_position is not None:
            closed_trade = self.trade_simulator.close_trade(
                open_position,
                timestamp=timestamp,
                price=row['close']
            )
            if closed_trade:
                trades.append(closed_trade)
            open_position = None

    return trades

# Optimized Engine Analysis
def _simulate_trades_vectorized(self, symbol, data):
    """
    Vectorized trade simulation for performance

    Flow:
    1. Convert signals to numpy arrays
    2. Find signal indices using np.where()
    3. Process signals in chronological order
    4. Use fast trade creation/closing methods
    5. Return completed trades
    """
    # Vectorized signal processing
    buy_indices = np.where(data['buy_signal'].values == 1)[0]
    sell_indices = np.where(data['sell_signal'].values == 1)[0]

    # Combine and sort signals
    signal_indices = np.concatenate([buy_indices, sell_indices])
    signal_types = np.concatenate([np.ones(len(buy_indices)), np.zeros(len(sell_indices))])
    sort_order = np.argsort(signal_indices)

    # Process signals efficiently
    for i, signal_type in zip(signal_indices[sort_order], signal_types[sort_order]):
        if signal_type == 1:  # Buy
            open_position = self._create_trade_fast(symbol, timestamp, price)
        else:  # Sell
            closed_trade = self._close_trade_fast(open_position, timestamp, price)
```

### 💹 Trade Execution Logic

```python
# TradeSimulator Core Methods
class TradeSimulator:
    def open_trade(self, symbol, timestamp, price, quantity):
        """
        Execute trade entry with realistic conditions

        Conditions Checked:
        1. Maximum open trades limit
        2. Sufficient account balance
        3. Valid price and quantity
        4. Slippage and fee calculations
        """
        # Check trading limits
        if len(self.open_trades) >= self.max_open_trades:
            return None

        # Apply slippage (pay slightly more on entry)
        entry_price = price * (1 + self.slippage)

        # Calculate costs
        trade_value = entry_price * quantity
        entry_fee = trade_value * self.fee
        total_cost = trade_value + entry_fee

        # Balance check
        if total_cost > self.balance:
            return None

        # Execute trade
        trade = {
            'trade_id': str(uuid.uuid4()),
            'symbol': symbol,
            'entry_timestamp': timestamp,
            'entry_price': entry_price,
            'quantity': quantity,
            'entry_value': trade_value,
            'entry_fee': entry_fee,
            'status': 'open'
        }

        # Update account
        self.balance -= total_cost
        self.total_fees_paid += entry_fee
        self.open_trades[trade['trade_id']] = trade

        return trade

    def close_trade(self, trade, timestamp, price):
        """
        Execute trade exit with realistic conditions

        Calculations:
        1. Apply exit slippage (receive slightly less)
        2. Calculate exit fees
        3. Compute profit/loss
        4. Update account balance
        """
        # Apply slippage (receive slightly less on exit)
        exit_price = price * (1 - self.slippage)

        # Calculate proceeds
        exit_value = exit_price * trade['quantity']
        exit_fee = exit_value * self.fee
        net_proceeds = exit_value - exit_fee

        # Calculate P&L
        total_cost = trade['entry_value'] + trade['entry_fee']
        profit_loss = net_proceeds - total_cost
        profit_loss_pct = (profit_loss / total_cost) * 100

        # Update trade record
        trade.update({
            'exit_timestamp': timestamp,
            'exit_price': exit_price,
            'exit_value': exit_value,
            'exit_fee': exit_fee,
            'total_fees': trade['entry_fee'] + exit_fee,
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct,
            'status': 'closed',
            'duration_hours': (timestamp - trade['entry_timestamp']).total_seconds() / 3600
        })

        # Update account
        self.balance += net_proceeds
        self.total_fees_paid += exit_fee
        del self.open_trades[trade['trade_id']]

        return trade
```

### 📊 Data Management Pipeline

```python
# DataManager Core Logic
class DataManager:
    def ensure_data_available(self, symbols, timeframe, start_date, end_date):
        """
        Comprehensive data availability checking and downloading

        Process:
        1. Check each symbol for existing data
        2. Validate date coverage
        3. Download missing data automatically
        4. Convert formats as needed
        5. Return success status
        """
        missing_symbols = []

        for symbol in symbols:
            if not self._check_data_coverage(symbol, timeframe, start_date, end_date):
                missing_symbols.append(symbol)

        if missing_symbols:
            return self._download_missing_data(missing_symbols, timeframe, start_date, end_date)

        return True

    def _download_missing_data(self, symbols, timeframe, start_date, end_date):
        """
        Multi-source data downloading with fallback

        Download Strategy:
        1. Try YFinanceFetcher first (faster)
        2. Fallback to download_data module
        3. Convert formats automatically
        4. Validate downloaded data
        """
        for symbol in symbols:
            success = False

            # Method 1: YFinanceFetcher
            if self.fetcher:
                try:
                    df = self.fetcher.fetch_ohlcv(symbol, timeframe, start_date, end_date, "NSE")
                    if not df.empty:
                        success = self._convert_to_backtest_format(df, symbol, timeframe)
                except Exception as e:
                    logger.error(f"YFinanceFetcher failed for {symbol}: {e}")

            # Method 2: download_data module
            if not success:
                try:
                    symbol_name, success, rows = download_data(
                        symbol=symbol,
                        exchange="nse",
                        timeframe=timeframe,
                        start_date=start_date,
                        end_date=end_date
                    )
                    if success:
                        success = self._convert_yfinance_file(symbol, timeframe)
                except Exception as e:
                    logger.error(f"download_data failed for {symbol}: {e}")
```

### 📈 Results Calculation Engine

```python
# BacktestResults Core Calculations
class BacktestResults:
    def calculate_metrics(self, trades, starting_balance, start_date, end_date):
        """
        Comprehensive performance metrics calculation

        Metrics Calculated:
        1. Basic trade statistics (count, win rate)
        2. Profit/loss analysis (total, average, best/worst)
        3. Risk metrics (drawdown, Sharpe ratio)
        4. Duration analysis (average trade time)
        5. Fee analysis (total costs)
        """
        if not trades:
            return self._empty_results(starting_balance)

        # Basic Statistics
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['profit_loss'] > 0]
        losing_trades = [t for t in trades if t['profit_loss'] <= 0]
        win_rate = (len(winning_trades) / total_trades) * 100

        # P&L Analysis
        total_profit_loss = sum(t['profit_loss'] for t in trades)
        avg_profit_loss = total_profit_loss / total_trades
        best_trade = max(t['profit_loss'] for t in trades)
        worst_trade = min(t['profit_loss'] for t in trades)

        # Risk Metrics
        ending_balance = starting_balance + total_profit_loss
        total_return_pct = (total_profit_loss / starting_balance) * 100
        max_drawdown_pct = self._calculate_max_drawdown(trades, starting_balance)

        # Advanced Metrics
        profit_factor = self._calculate_profit_factor(winning_trades, losing_trades)
        sharpe_ratio = self._calculate_sharpe_ratio(trades, total_return_pct)

        # Duration Analysis
        durations = [t['duration_hours'] for t in trades if 'duration_hours' in t]
        avg_trade_duration_hours = sum(durations) / len(durations) if durations else 0

        # Fee Analysis
        total_fees = sum(t.get('total_fees', 0) for t in trades)

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_profit_loss': total_profit_loss,
            'avg_profit_loss': avg_profit_loss,
            'best_trade': best_trade,
            'worst_trade': worst_trade,
            'starting_balance': starting_balance,
            'ending_balance': ending_balance,
            'total_return_pct': total_return_pct,
            'max_drawdown_pct': max_drawdown_pct,
            'profit_factor': profit_factor,
            'sharpe_ratio': sharpe_ratio,
            'avg_trade_duration_hours': avg_trade_duration_hours,
            'total_fees': total_fees,
            'start_date': start_date,
            'end_date': end_date
        }
```

---

## 🎯 Critical Decision Points in Code

### 1. **Engine Selection Logic**
```python
# In core/backtest.py
if optimized:
    from core.optimized_backtest_engine import OptimizedBacktestEngine
    backtest_engine = OptimizedBacktestEngine(config)
else:
    backtest_engine = BacktestEngine(config)
```

### 2. **Data Availability Check**
```python
# In core/data_manager.py
def _check_data_coverage(self, symbol, timeframe, start_date, end_date):
    data_file = self.data_dir / f"{symbol}_{timeframe}.csv"
    if not data_file.exists():
        return False

    df = pd.read_csv(data_file, parse_dates=['date'])
    data_start = df['date'].min()
    data_end = df['date'].max()

    required_start = pd.to_datetime(start_date)
    required_end = pd.to_datetime(end_date)

    return data_start <= required_start and data_end >= required_end
```

### 3. **Trade Execution Conditions**
```python
# In core/trade_simulator.py
def can_open_trade(self):
    return len(self.open_trades) < self.max_open_trades

def open_trade(self, symbol, timestamp, price, quantity):
    # Multiple condition checks
    if not self.can_open_trade():
        return None

    total_cost = (price * quantity * (1 + self.slippage)) * (1 + self.fee)
    if total_cost > self.balance:
        return None

    # Execute trade...
```

### 4. **Strategy Analysis Method Selection**
```python
# In strategy classes
def analyze(self, dataframe):
    if self.optimized:
        return self._analyze_vectorized(dataframe)
    else:
        return self._analyze_standard(dataframe)
```

This detailed flowchart and code documentation provides a complete understanding of how RapidTrader's backtesting engine operates at both the high-level workflow and low-level implementation levels.
