"""
Money Management Risk Manager

Handles all risk management calculations and validations for the money management module.
Provides comprehensive risk assessment and limit checking capabilities.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger("MoneyManagement.RiskManager")


class RiskManager:
    """
    Manages risk calculations and validations for the money management module.
    
    Provides comprehensive risk assessment including:
    - Position risk calculations
    - Portfolio risk monitoring
    - Risk limit validations
    - Correlation analysis
    - Concentration limits
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize risk manager with configuration.
        
        Args:
            config: Risk management configuration
        """
        self.config = config
        self.risk_config = config.get("risk_management", {})
        self.global_config = config.get("global_settings", {})
        
    def calculate_position_risk(self, position_value: float, stop_loss_pct: float = 0.05) -> Dict[str, float]:
        """
        Calculate risk for a single position.
        
        Args:
            position_value: Total value of the position
            stop_loss_pct: Stop loss percentage (default 5%)
            
        Returns:
            Dictionary with risk calculations
        """
        try:
            max_loss = position_value * stop_loss_pct
            risk_reward_ratio = 1.0 / stop_loss_pct if stop_loss_pct > 0 else 0
            
            return {
                "position_value": position_value,
                "max_loss": max_loss,
                "stop_loss_percentage": stop_loss_pct * 100,
                "risk_reward_ratio": risk_reward_ratio,
                "risk_amount": max_loss
            }
            
        except Exception as e:
            logger.error(f"Error calculating position risk: {e}")
            return {"error": str(e)}
    
    def calculate_portfolio_risk(self, positions: List[Dict[str, Any]], total_capital: float) -> Dict[str, Any]:
        """
        Calculate total portfolio risk.
        
        Args:
            positions: List of position dictionaries
            total_capital: Total available capital
            
        Returns:
            Portfolio risk analysis
        """
        try:
            total_risk = 0.0
            total_value = 0.0
            position_risks = []
            
            for position in positions:
                position_value = position.get("position_value", 0)
                risk_amount = position.get("risk_amount", 0)
                
                total_value += position_value
                total_risk += risk_amount
                
                position_risks.append({
                    "symbol": position.get("symbol", "Unknown"),
                    "position_value": position_value,
                    "risk_amount": risk_amount,
                    "risk_percentage": (risk_amount / total_capital * 100) if total_capital > 0 else 0
                })
            
            portfolio_risk_pct = (total_risk / total_capital * 100) if total_capital > 0 else 0
            capital_utilization = (total_value / total_capital * 100) if total_capital > 0 else 0
            
            return {
                "total_risk_amount": total_risk,
                "total_portfolio_value": total_value,
                "portfolio_risk_percentage": portfolio_risk_pct,
                "capital_utilization": capital_utilization,
                "position_count": len(positions),
                "position_risks": position_risks,
                "max_allowed_risk": self.global_config.get("max_total_risk", 15.0),
                "within_risk_limits": portfolio_risk_pct <= self.global_config.get("max_total_risk", 15.0)
            }
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return {"error": str(e)}
    
    def check_risk_limits(self, new_positions: List[Dict[str, Any]], 
                         existing_positions: List[Dict[str, Any]] = None,
                         total_capital: float = 0) -> Dict[str, Any]:
        """
        Check if new positions would violate risk limits.
        
        Args:
            new_positions: List of new position dictionaries
            existing_positions: List of existing positions (optional)
            total_capital: Total available capital
            
        Returns:
            Risk limit check results
        """
        try:
            existing_positions = existing_positions or []
            all_positions = existing_positions + new_positions
            
            # Calculate portfolio risk
            portfolio_risk = self.calculate_portfolio_risk(all_positions, total_capital)
            
            violations = []
            warnings = []
            
            # Check total portfolio risk
            max_total_risk = self.global_config.get("max_total_risk", 15.0)
            if portfolio_risk.get("portfolio_risk_percentage", 0) > max_total_risk:
                violations.append(f"Portfolio risk {portfolio_risk['portfolio_risk_percentage']:.2f}% exceeds limit {max_total_risk}%")
            
            # Check per-trade risk limits
            max_risk_per_trade = self.global_config.get("max_risk_per_trade", 2.0)
            for position in new_positions:
                position_risk_pct = (position.get("risk_amount", 0) / total_capital * 100) if total_capital > 0 else 0
                if position_risk_pct > max_risk_per_trade:
                    violations.append(f"Position {position.get('symbol', 'Unknown')} risk {position_risk_pct:.2f}% exceeds per-trade limit {max_risk_per_trade}%")
            
            # Check position concentration
            single_stock_limit = self.risk_config.get("single_stock_limit", 10.0)
            for position in all_positions:
                position_pct = (position.get("position_value", 0) / total_capital * 100) if total_capital > 0 else 0
                if position_pct > single_stock_limit:
                    violations.append(f"Position {position.get('symbol', 'Unknown')} {position_pct:.2f}% exceeds single stock limit {single_stock_limit}%")
            
            # Check strategy position limits
            strategy_positions = {}
            for position in all_positions:
                strategy = position.get("strategy", "unknown")
                strategy_positions[strategy] = strategy_positions.get(strategy, 0) + 1
            
            max_positions_per_strategy = self.risk_config.get("max_positions_per_strategy", 5)
            for strategy, count in strategy_positions.items():
                if count > max_positions_per_strategy:
                    violations.append(f"Strategy {strategy} has {count} positions, exceeds limit {max_positions_per_strategy}")
            
            # Generate warnings for high risk situations
            if portfolio_risk.get("portfolio_risk_percentage", 0) > max_total_risk * 0.8:
                warnings.append(f"Portfolio risk approaching limit: {portfolio_risk['portfolio_risk_percentage']:.2f}%")
            
            if portfolio_risk.get("capital_utilization", 0) > 95:
                warnings.append(f"High capital utilization: {portfolio_risk['capital_utilization']:.2f}%")
            
            return {
                "within_limits": len(violations) == 0,
                "violations": violations,
                "warnings": warnings,
                "portfolio_risk": portfolio_risk,
                "risk_summary": {
                    "total_risk_percentage": portfolio_risk.get("portfolio_risk_percentage", 0),
                    "max_allowed_risk": max_total_risk,
                    "capital_utilization": portfolio_risk.get("capital_utilization", 0),
                    "position_count": len(all_positions)
                }
            }
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {"within_limits": False, "error": str(e)}
    
    def calculate_correlation_risk(self, positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate correlation risk between positions (simplified version).
        
        Args:
            positions: List of position dictionaries
            
        Returns:
            Correlation risk analysis
        """
        try:
            # This is a simplified correlation analysis
            # In a real implementation, you would use historical price data
            
            sectors = {}
            for position in positions:
                sector = position.get("sector", "Unknown")
                if sector not in sectors:
                    sectors[sector] = []
                sectors[sector].append(position)
            
            sector_concentrations = {}
            total_value = sum(pos.get("position_value", 0) for pos in positions)
            
            for sector, sector_positions in sectors.items():
                sector_value = sum(pos.get("position_value", 0) for pos in sector_positions)
                sector_pct = (sector_value / total_value * 100) if total_value > 0 else 0
                sector_concentrations[sector] = {
                    "percentage": sector_pct,
                    "value": sector_value,
                    "position_count": len(sector_positions)
                }
            
            # Check sector concentration limits
            sector_limit = self.risk_config.get("sector_concentration_limit", 30.0)
            violations = []
            
            for sector, data in sector_concentrations.items():
                if data["percentage"] > sector_limit:
                    violations.append(f"Sector {sector} concentration {data['percentage']:.2f}% exceeds limit {sector_limit}%")
            
            return {
                "sector_concentrations": sector_concentrations,
                "violations": violations,
                "within_limits": len(violations) == 0,
                "diversification_score": len(sectors) / max(len(positions), 1) * 100
            }
            
        except Exception as e:
            logger.error(f"Error calculating correlation risk: {e}")
            return {"error": str(e)}
    
    def get_risk_recommendations(self, portfolio_analysis: Dict[str, Any]) -> List[str]:
        """
        Generate risk management recommendations based on portfolio analysis.
        
        Args:
            portfolio_analysis: Portfolio risk analysis results
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        try:
            risk_pct = portfolio_analysis.get("portfolio_risk_percentage", 0)
            capital_util = portfolio_analysis.get("capital_utilization", 0)
            position_count = portfolio_analysis.get("position_count", 0)
            
            # Risk level recommendations
            if risk_pct > 12:
                recommendations.append("Consider reducing position sizes to lower portfolio risk")
            elif risk_pct < 5:
                recommendations.append("Portfolio risk is conservative - consider increasing position sizes")
            
            # Capital utilization recommendations
            if capital_util > 90:
                recommendations.append("High capital utilization - maintain cash reserves for opportunities")
            elif capital_util < 50:
                recommendations.append("Low capital utilization - consider increasing allocations")
            
            # Diversification recommendations
            if position_count < 3:
                recommendations.append("Consider adding more positions for better diversification")
            elif position_count > 15:
                recommendations.append("High number of positions - consider consolidating for better management")
            
            # General recommendations
            recommendations.append("Regularly review and rebalance portfolio allocations")
            recommendations.append("Monitor correlation between positions to avoid concentration risk")
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            recommendations.append("Unable to generate recommendations due to analysis error")
        
        return recommendations
    
    def update_risk_config(self, new_config: Dict[str, Any]) -> bool:
        """
        Update risk management configuration.
        
        Args:
            new_config: New risk configuration
            
        Returns:
            True if successful
        """
        try:
            self.risk_config.update(new_config.get("risk_management", {}))
            self.global_config.update(new_config.get("global_settings", {}))
            logger.info("Risk configuration updated")
            return True
            
        except Exception as e:
            logger.error(f"Error updating risk config: {e}")
            return False
