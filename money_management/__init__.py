"""
RapidTrader Money Management Module

A standalone, modular money management system that can be used by any trading system
or external application. Provides comprehensive capital allocation, risk management,
and position sizing capabilities.

Key Features:
- Real-time balance integration with any broker
- Percentage-based allocation across strategies
- Risk management and position sizing
- Portfolio rebalancing
- Configuration management
- Standalone operation

Usage:
    from money_management import MoneyManager, get_money_manager
    
    # Initialize with any broker that implements the required interface
    money_manager = MoneyManager(broker=your_broker)
    
    # Or use the global instance
    money_manager = get_money_manager(broker=your_broker)
    
    # Get allocation summary
    summary = money_manager.get_allocation_summary()
    
    # Allocate to strategies
    money_manager.add_strategy_allocation("my_strategy", 30.0)
    
    # Calculate position sizes
    position_info = money_manager.get_position_size("my_strategy", "SYMBOL", 100.0)
"""

from .core import MoneyManager, get_money_manager
from .interfaces import BrokerInterface, BalanceProvider
from .config_manager import ConfigManager
from .risk_manager import RiskManager
from .allocation_engine import AllocationEngine
from .position_sizer import PositionSizer

__version__ = "1.0.0"
__author__ = "RapidTrader Team"

__all__ = [
    "MoneyManager",
    "get_money_manager", 
    "BrokerInterface",
    "BalanceProvider",
    "ConfigManager",
    "RiskManager",
    "AllocationEngine",
    "PositionSizer"
]
