"""
Money Management Allocation Engine

Handles all capital allocation logic for the money management module.
Provides sophisticated allocation strategies and rebalancing capabilities.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger("MoneyManagement.AllocationEngine")


class AllocationEngine:
    """
    Manages capital allocation across strategies and positions.
    
    Provides multiple allocation models:
    - Percentage-based allocation
    - Fixed amount allocation
    - Dynamic allocation based on performance
    - Risk-adjusted allocation
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize allocation engine with configuration.
        
        Args:
            config: Allocation configuration
        """
        self.config = config
        self.allocation_config = config.get("allocation_model", {})
        self.global_config = config.get("global_settings", {})
        
    def calculate_strategy_allocations(self, available_capital: float) -> Dict[str, Dict[str, float]]:
        """
        Calculate capital allocation for each strategy.
        
        Args:
            available_capital: Total available capital
            
        Returns:
            Dictionary with strategy allocations
        """
        try:
            allocation_type = self.allocation_config.get("type", "percentage")
            strategies = self.allocation_config.get("strategies", {})
            
            if allocation_type == "percentage":
                return self._calculate_percentage_allocations(available_capital, strategies)
            elif allocation_type == "fixed_amount":
                return self._calculate_fixed_allocations(available_capital, strategies)
            elif allocation_type == "dynamic":
                return self._calculate_dynamic_allocations(available_capital, strategies)
            else:
                logger.warning(f"Unknown allocation type: {allocation_type}, using percentage")
                return self._calculate_percentage_allocations(available_capital, strategies)
                
        except Exception as e:
            logger.error(f"Error calculating strategy allocations: {e}")
            return {}
    
    def _calculate_percentage_allocations(self, available_capital: float, 
                                        strategies: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """Calculate percentage-based allocations."""
        try:
            # Apply global capital usage limit
            total_capital_usage = self.global_config.get("total_capital_usage", 90.0) / 100.0
            usable_capital = available_capital * total_capital_usage
            
            # Normalize percentages if they exceed 100%
            total_percentage = sum(strategies.values())
            if total_percentage > 100:
                logger.warning(f"Total allocation exceeds 100%: {total_percentage}%. Normalizing...")
                strategies = {k: (v / total_percentage) * 100 for k, v in strategies.items()}
                total_percentage = 100.0
            
            allocations = {}
            max_risk_per_trade = self.global_config.get("max_risk_per_trade", 2.0) / 100.0
            
            for strategy, percentage in strategies.items():
                allocation_amount = usable_capital * (percentage / 100.0)
                
                allocations[strategy] = {
                    "percentage": percentage,
                    "amount": allocation_amount,
                    "max_risk_amount": allocation_amount * max_risk_per_trade,
                    "max_positions": self.config.get("risk_management", {}).get("max_positions_per_strategy", 5),
                    "available_amount": allocation_amount,  # Initially all available
                    "used_amount": 0.0,
                    "reserved_amount": 0.0
                }
            
            return allocations
            
        except Exception as e:
            logger.error(f"Error in percentage allocation calculation: {e}")
            return {}
    
    def _calculate_fixed_allocations(self, available_capital: float, 
                                   strategies: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """Calculate fixed amount allocations."""
        try:
            allocations = {}
            total_fixed = sum(strategies.values())
            
            if total_fixed > available_capital:
                logger.warning(f"Total fixed allocations exceed available capital. Scaling down...")
                scale_factor = available_capital / total_fixed
                strategies = {k: v * scale_factor for k, v in strategies.items()}
            
            max_risk_per_trade = self.global_config.get("max_risk_per_trade", 2.0) / 100.0
            
            for strategy, amount in strategies.items():
                percentage = (amount / available_capital) * 100 if available_capital > 0 else 0
                
                allocations[strategy] = {
                    "percentage": percentage,
                    "amount": amount,
                    "max_risk_amount": amount * max_risk_per_trade,
                    "max_positions": self.config.get("risk_management", {}).get("max_positions_per_strategy", 5),
                    "available_amount": amount,
                    "used_amount": 0.0,
                    "reserved_amount": 0.0
                }
            
            return allocations
            
        except Exception as e:
            logger.error(f"Error in fixed allocation calculation: {e}")
            return {}
    
    def _calculate_dynamic_allocations(self, available_capital: float, 
                                     strategies: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """Calculate dynamic allocations based on performance and market conditions."""
        try:
            # This is a simplified dynamic allocation
            # In practice, this would consider historical performance, volatility, etc.
            
            base_allocations = {}
            total_weight = 0
            
            for strategy, config in strategies.items():
                # Get base allocation and performance metrics
                base_percentage = config.get("base_percentage", 10.0)
                performance_score = config.get("performance_score", 1.0)  # 0.5 to 2.0
                volatility_score = config.get("volatility_score", 1.0)    # 0.5 to 2.0
                
                # Calculate dynamic weight
                weight = base_percentage * performance_score / volatility_score
                base_allocations[strategy] = weight
                total_weight += weight
            
            # Normalize to percentage allocations
            percentage_allocations = {}
            for strategy, weight in base_allocations.items():
                percentage_allocations[strategy] = (weight / total_weight) * 100
            
            return self._calculate_percentage_allocations(available_capital, percentage_allocations)
            
        except Exception as e:
            logger.error(f"Error in dynamic allocation calculation: {e}")
            return {}
    
    def add_strategy_allocation(self, strategy_name: str, allocation: float, 
                              allocation_type: str = None) -> bool:
        """
        Add or update strategy allocation.
        
        Args:
            strategy_name: Name of the strategy
            allocation: Allocation amount (percentage or fixed amount)
            allocation_type: Type of allocation (overrides config default)
            
        Returns:
            True if successful
        """
        try:
            current_type = allocation_type or self.allocation_config.get("type", "percentage")
            strategies = self.allocation_config.get("strategies", {})
            
            if current_type == "percentage":
                if allocation < 0 or allocation > 100:
                    logger.error(f"Invalid percentage: {allocation}. Must be between 0-100")
                    return False
                
                # Check if total would exceed 100%
                current_total = sum(v for k, v in strategies.items() if k != strategy_name)
                if current_total + allocation > 100:
                    logger.error(f"Total allocation would exceed 100%: {current_total + allocation}%")
                    return False
            
            strategies[strategy_name] = allocation
            self.allocation_config["strategies"] = strategies
            
            logger.info(f"Added allocation for {strategy_name}: {allocation}{'%' if current_type == 'percentage' else ''}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding strategy allocation: {e}")
            return False
    
    def remove_strategy_allocation(self, strategy_name: str) -> bool:
        """
        Remove strategy allocation.
        
        Args:
            strategy_name: Name of the strategy to remove
            
        Returns:
            True if successful
        """
        try:
            strategies = self.allocation_config.get("strategies", {})
            
            if strategy_name in strategies:
                del strategies[strategy_name]
                self.allocation_config["strategies"] = strategies
                logger.info(f"Removed allocation for {strategy_name}")
                return True
            else:
                logger.warning(f"Strategy {strategy_name} not found in allocations")
                return False
                
        except Exception as e:
            logger.error(f"Error removing strategy allocation: {e}")
            return False
    
    def check_rebalancing_needed(self, current_allocations: Dict[str, Dict[str, float]], 
                               target_allocations: Dict[str, Dict[str, float]], 
                               threshold: float = 5.0) -> Dict[str, Any]:
        """
        Check if portfolio rebalancing is needed.
        
        Args:
            current_allocations: Current allocation state
            target_allocations: Target allocation state
            threshold: Rebalancing threshold percentage
            
        Returns:
            Rebalancing analysis
        """
        try:
            rebalancing_needed = False
            rebalancing_actions = []
            
            for strategy in target_allocations:
                current = current_allocations.get(strategy, {})
                target = target_allocations[strategy]
                
                current_pct = current.get("percentage", 0)
                target_pct = target.get("percentage", 0)
                
                deviation = abs(current_pct - target_pct)
                
                if deviation > threshold:
                    rebalancing_needed = True
                    action = "increase" if current_pct < target_pct else "decrease"
                    rebalancing_actions.append({
                        "strategy": strategy,
                        "action": action,
                        "current_percentage": current_pct,
                        "target_percentage": target_pct,
                        "deviation": deviation
                    })
            
            return {
                "rebalancing_needed": rebalancing_needed,
                "threshold": threshold,
                "actions": rebalancing_actions,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error checking rebalancing: {e}")
            return {"error": str(e)}
    
    def calculate_rebalancing_trades(self, current_allocations: Dict[str, Dict[str, float]], 
                                   target_allocations: Dict[str, Dict[str, float]]) -> List[Dict[str, Any]]:
        """
        Calculate specific trades needed for rebalancing.
        
        Args:
            current_allocations: Current allocation state
            target_allocations: Target allocation state
            
        Returns:
            List of rebalancing trades
        """
        try:
            trades = []
            
            for strategy in target_allocations:
                current = current_allocations.get(strategy, {})
                target = target_allocations[strategy]
                
                current_amount = current.get("amount", 0)
                target_amount = target.get("amount", 0)
                
                difference = target_amount - current_amount
                
                if abs(difference) > 100:  # Minimum trade threshold
                    trade_type = "buy" if difference > 0 else "sell"
                    trades.append({
                        "strategy": strategy,
                        "type": trade_type,
                        "amount": abs(difference),
                        "current_allocation": current_amount,
                        "target_allocation": target_amount
                    })
            
            return trades
            
        except Exception as e:
            logger.error(f"Error calculating rebalancing trades: {e}")
            return []
    
    def get_allocation_summary(self, allocations: Dict[str, Dict[str, float]], 
                             total_capital: float) -> Dict[str, Any]:
        """
        Generate comprehensive allocation summary.
        
        Args:
            allocations: Strategy allocations
            total_capital: Total available capital
            
        Returns:
            Allocation summary
        """
        try:
            total_allocated = sum(alloc["amount"] for alloc in allocations.values())
            total_percentage = sum(alloc["percentage"] for alloc in allocations.values())
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "total_capital": total_capital,
                "total_allocated_amount": total_allocated,
                "total_allocated_percentage": total_percentage,
                "unallocated_amount": total_capital - total_allocated,
                "unallocated_percentage": 100 - total_percentage,
                "strategy_count": len(allocations),
                "allocation_efficiency": (total_allocated / total_capital * 100) if total_capital > 0 else 0,
                "strategies": allocations
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating allocation summary: {e}")
            return {"error": str(e)}
