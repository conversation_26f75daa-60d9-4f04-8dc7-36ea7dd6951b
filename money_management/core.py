"""
Money Management Core Module

The main orchestrator for the standalone money management system.
Integrates all components and provides a unified interface.
"""

import os
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

from .interfaces import B<PERSON>rInter<PERSON>, BalanceProvider, Broker<PERSON>dapter, SimpleBalanceProvider
from .config_manager import ConfigManager
from .risk_manager import RiskManager
from .allocation_engine import AllocationEngine
from .position_sizer import PositionSizer

logger = logging.getLogger("MoneyManagement.Core")


class MoneyManager:
    """
    Main Money Management System

    A comprehensive, standalone money management system that can integrate
    with any broker or trading system. Provides capital allocation, risk
    management, and position sizing capabilities.

    Features:
    - Real-time balance integration
    - Multiple allocation strategies
    - Comprehensive risk management
    - Advanced position sizing
    - Portfolio rebalancing
    - Modular design for easy integration
    """

    def __init__(self, broker: Union[BrokerInterface, BalanceProvider, Any] = None,
                 config_path: str = None, auto_create_config: bool = True):
        """
        Initialize Money Manager.

        Args:
            broker: Broker instance (any object implementing required methods)
            config_path: Path to configuration file
            auto_create_config: Whether to auto-create default config
        """
        # Set up configuration
        default_config_path = config_path or "userdata/config/money_management.json"
        self.config_manager = ConfigManager(default_config_path, auto_create_config)
        self.config = self.config_manager.get_config()

        # Set up broker interface
        self.broker = self._setup_broker_interface(broker)

        # Initialize components
        self.risk_manager = RiskManager(self.config)
        self.allocation_engine = AllocationEngine(self.config)
        self.position_sizer = PositionSizer(self.config)

        # Cache for broker data
        self._last_balance_fetch = None
        self._cached_balance = None
        self._balance_cache_duration = self.config.get("broker_settings", {}).get("balance_refresh_interval", 300)

        logger.info("MoneyManager initialized successfully")

    def _setup_broker_interface(self, broker: Any) -> Optional[BrokerInterface]:
        """Set up broker interface with automatic adaptation."""
        if broker is None:
            logger.info("No broker provided, using mock balance provider")
            return SimpleBalanceProvider(150000.0, 200000.0, 50000.0)

        # Check if broker already implements BrokerInterface
        if isinstance(broker, BrokerInterface):
            return broker

        # Check if broker implements BalanceProvider
        if isinstance(broker, BalanceProvider):
            return broker

        # Try to adapt existing broker
        try:
            # Check if broker has required methods
            required_methods = ['get_fund_limit', 'get_holdings', 'get_positions', 'is_dry_run']
            if all(hasattr(broker, method) for method in required_methods):
                # Log broker configuration
                is_dry_run = getattr(broker, 'is_dry_run', lambda: True)()
                is_live_data = getattr(broker, 'is_live_data_enabled', lambda: False)()
                logger.info(f"Using DhanBroker - Dry Run: {is_dry_run}, Live Data: {is_live_data}")
                return BrokerAdapter(broker)
            else:
                logger.warning("Broker doesn't implement required methods, using mock provider")
                return SimpleBalanceProvider(150000.0, 200000.0, 50000.0)
        except Exception as e:
            logger.error(f"Error setting up broker interface: {e}")
            return SimpleBalanceProvider(150000.0, 200000.0, 50000.0)

    def get_available_balance(self, force_refresh: bool = False) -> Dict[str, float]:
        """
        Get available trading balance from broker.

        Args:
            force_refresh: Force refresh from broker API

        Returns:
            Dictionary with balance information
        """
        # Check cache first
        if (not force_refresh and
            self._cached_balance and
            self._last_balance_fetch and
            (datetime.now() - self._last_balance_fetch).seconds < self._balance_cache_duration):
            return self._cached_balance

        try:
            if not self.broker:
                return self._get_mock_balance()

            # Handle different broker interface types
            if hasattr(self.broker, 'get_fund_limit'):
                # Full broker interface
                fund_info = self.broker.get_fund_limit()

                if isinstance(fund_info, dict):
                    balance_info = {
                        "total_balance": fund_info.get("totalBalance", fund_info.get("total_balance", 0.0)),
                        "available_balance": fund_info.get("availableBalance", fund_info.get("available_balance", 0.0)),
                        "used_margin": fund_info.get("utilizedAmount", fund_info.get("used_margin", 0.0)),
                        "withdrawable_balance": fund_info.get("withdrawableBalance", fund_info.get("withdrawable_balance", 0.0)),
                        "blocked_amount": fund_info.get("blockedPayoutAmount", fund_info.get("blocked_amount", 0.0))
                    }
                else:
                    return self._get_mock_balance()

            elif hasattr(self.broker, 'get_available_balance'):
                # Simple balance provider
                balance_info = {
                    "total_balance": self.broker.get_total_balance(),
                    "available_balance": self.broker.get_available_balance(),
                    "used_margin": getattr(self.broker, 'get_used_margin', lambda: 0.0)(),
                    "withdrawable_balance": self.broker.get_available_balance(),
                    "blocked_amount": 0.0
                }
            else:
                return self._get_mock_balance()

            # Cache the result
            self._cached_balance = balance_info
            self._last_balance_fetch = datetime.now()

            # Log data source
            is_live_data = False
            if hasattr(self.broker, 'is_live_data_enabled'):
                is_live_data = self.broker.is_live_data_enabled()
            elif hasattr(self.broker, 'broker') and hasattr(self.broker.broker, 'is_live_data_enabled'):
                # For BrokerAdapter case
                is_live_data = self.broker.broker.is_live_data_enabled()

            data_source = "Live API" if is_live_data else "Mock/Simulated"
            logger.info(f"Fetched balance from {data_source}: Available ₹{balance_info['available_balance']:,.2f}")
            return balance_info

        except Exception as e:
            logger.error(f"Error fetching balance from broker: {e}")
            return self._get_mock_balance()

    def _get_mock_balance(self) -> Dict[str, float]:
        """Get mock balance for testing/dry-run."""
        return {
            "total_balance": 200000.0,
            "available_balance": 150000.0,
            "used_margin": 50000.0,
            "withdrawable_balance": 145000.0,
            "blocked_amount": 5000.0
        }

    def calculate_strategy_allocations(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate money allocation for each strategy based on configuration.

        Returns:
            Dictionary with strategy allocations
        """
        balance_info = self.get_available_balance()
        available_capital = balance_info["available_balance"]

        return self.allocation_engine.calculate_strategy_allocations(available_capital)

    def add_strategy_allocation(self, strategy_name: str, allocation: float,
                              allocation_type: str = None) -> bool:
        """
        Add or update strategy allocation.

        Args:
            strategy_name: Name of the strategy
            allocation: Allocation amount (percentage or fixed amount)
            allocation_type: Type of allocation (overrides config default)

        Returns:
            True if successful
        """
        success = self.allocation_engine.add_strategy_allocation(strategy_name, allocation, allocation_type)

        if success:
            # Update configuration
            self.config_manager.update_config({"allocation_model": self.allocation_engine.allocation_config})

        return success

    def remove_strategy_allocation(self, strategy_name: str) -> bool:
        """
        Remove strategy allocation.

        Args:
            strategy_name: Name of the strategy to remove

        Returns:
            True if successful
        """
        success = self.allocation_engine.remove_strategy_allocation(strategy_name)

        if success:
            # Update configuration
            self.config_manager.update_config({"allocation_model": self.allocation_engine.allocation_config})

        return success

    def get_position_size(self, strategy_name: str, symbol: str, price: float,
                         risk_percentage: Optional[float] = None,
                         sizing_method: str = None) -> Dict[str, Any]:
        """
        Calculate position size for a trade.

        Args:
            strategy_name: Name of the strategy
            symbol: Trading symbol
            price: Entry price
            risk_percentage: Custom risk percentage (optional)
            sizing_method: Position sizing method (optional)

        Returns:
            Dictionary with position sizing information
        """
        try:
            allocations = self.calculate_strategy_allocations()

            if strategy_name not in allocations:
                logger.error(f"Strategy {strategy_name} not found in allocations")
                return {"error": "Strategy not found"}

            strategy_allocation = allocations[strategy_name]

            return self.position_sizer.calculate_position_size(
                strategy_name, symbol, price, strategy_allocation,
                risk_percentage, sizing_method=sizing_method
            )

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {"error": str(e)}

    def check_risk_limits(self, new_positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check if new positions would violate risk limits.

        Args:
            new_positions: List of new position dictionaries

        Returns:
            Risk check results
        """
        try:
            # Get current portfolio information
            existing_positions = []
            if self.broker and hasattr(self.broker, 'get_positions'):
                try:
                    broker_positions = self.broker.get_positions()
                    # Convert broker positions to standard format
                    for pos in broker_positions:
                        if isinstance(pos, dict):
                            existing_positions.append({
                                "symbol": pos.get("symbol", "Unknown"),
                                "position_value": abs(pos.get("quantity", 0)) * pos.get("avgPrice", 0),
                                "risk_amount": abs(pos.get("unrealizedPnl", 0)) if pos.get("unrealizedPnl", 0) < 0 else 0
                            })
                except Exception as e:
                    logger.warning(f"Could not fetch existing positions: {e}")

            balance_info = self.get_available_balance()
            total_capital = balance_info["available_balance"]

            return self.risk_manager.check_risk_limits(new_positions, existing_positions, total_capital)

        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {"within_limits": False, "error": str(e)}

    def rebalance_allocations(self) -> Dict[str, Any]:
        """
        Check and perform portfolio rebalancing.

        Returns:
            Rebalancing results
        """
        try:
            current_allocations = self.calculate_strategy_allocations()
            target_allocations = current_allocations  # For now, target = current

            rebalance_check = self.allocation_engine.check_rebalancing_needed(
                current_allocations, target_allocations
            )

            if rebalance_check.get("rebalancing_needed", False):
                trades = self.allocation_engine.calculate_rebalancing_trades(
                    current_allocations, target_allocations
                )
                rebalance_check["trades"] = trades

            return rebalance_check

        except Exception as e:
            logger.error(f"Error during rebalancing: {e}")
            return {"error": str(e)}

    def get_allocation_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive allocation summary.

        Returns:
            Allocation summary with all details
        """
        try:
            balance_info = self.get_available_balance()
            allocations = self.calculate_strategy_allocations()

            # Generate summary using allocation engine
            summary = self.allocation_engine.get_allocation_summary(allocations, balance_info["available_balance"])

            # Add balance info and risk limits
            summary["balance_info"] = balance_info
            summary["emergency_reserve"] = balance_info["available_balance"] * (
                self.config.get("global_settings", {}).get("emergency_reserve", 10.0) / 100.0
            )
            summary["risk_limits"] = {
                "max_risk_per_trade": self.config.get("global_settings", {}).get("max_risk_per_trade", 2.0),
                "max_total_risk": self.config.get("global_settings", {}).get("max_total_risk", 15.0),
                "stop_loss_global": self.config.get("global_settings", {}).get("stop_loss_global", 20.0)
            }

            return summary

        except Exception as e:
            logger.error(f"Error generating allocation summary: {e}")
            return {"error": str(e)}

    def update_global_settings(self, settings: Dict[str, Any]) -> bool:
        """
        Update global money management settings.

        Args:
            settings: Dictionary of settings to update

        Returns:
            True if successful
        """
        try:
            # Update configuration
            success = self.config_manager.update_config({"global_settings": settings})

            if success:
                # Reload configuration in components
                self.config = self.config_manager.get_config()
                self.risk_manager.update_risk_config(self.config)

                logger.info(f"Updated global settings: {list(settings.keys())}")

            return success

        except Exception as e:
            logger.error(f"Error updating global settings: {e}")
            return False


# Global money manager instance
_money_manager = None

def get_money_manager(broker=None, config_path: str = None, force_new: bool = False) -> MoneyManager:
    """
    Get global money manager instance.

    Args:
        broker: Broker instance (optional)
        config_path: Configuration file path (optional)
        force_new: Force creation of new instance

    Returns:
        MoneyManager instance
    """
    global _money_manager

    if _money_manager is None or force_new:
        _money_manager = MoneyManager(broker=broker, config_path=config_path)
    elif broker is not None and _money_manager.broker != broker:
        # Update broker if different one provided
        _money_manager.broker = _money_manager._setup_broker_interface(broker)

    return _money_manager
