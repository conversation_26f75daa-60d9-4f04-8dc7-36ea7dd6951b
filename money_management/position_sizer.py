"""
Money Management Position Sizer

Handles position sizing calculations for the money management module.
Provides multiple position sizing strategies and risk-based calculations.
"""

import logging
import math
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger("MoneyManagement.PositionSizer")


class PositionSizer:
    """
    Calculates optimal position sizes based on various strategies.
    
    Supports multiple position sizing methods:
    - Fixed percentage of capital
    - Risk-based sizing (Kelly Criterion)
    - Volatility-based sizing
    - Equal weight sizing
    - Custom sizing rules
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize position sizer with configuration.
        
        Args:
            config: Position sizing configuration
        """
        self.config = config
        self.risk_config = config.get("risk_management", {})
        self.global_config = config.get("global_settings", {})
        
    def calculate_position_size(self, strategy_name: str, symbol: str, price: float,
                              strategy_allocation: Dict[str, float],
                              risk_percentage: Optional[float] = None,
                              stop_loss_pct: float = 0.05,
                              sizing_method: str = None) -> Dict[str, Any]:
        """
        Calculate optimal position size for a trade.
        
        Args:
            strategy_name: Name of the strategy
            symbol: Trading symbol
            price: Entry price
            strategy_allocation: Strategy allocation information
            risk_percentage: Custom risk percentage (optional)
            stop_loss_pct: Stop loss percentage
            sizing_method: Position sizing method (optional)
            
        Returns:
            Position sizing information
        """
        try:
            method = sizing_method or self.risk_config.get("position_sizing", "percentage")
            
            if method == "percentage":
                return self._calculate_percentage_size(strategy_name, symbol, price, strategy_allocation, risk_percentage)
            elif method == "risk_based":
                return self._calculate_risk_based_size(strategy_name, symbol, price, strategy_allocation, risk_percentage, stop_loss_pct)
            elif method == "volatility_based":
                return self._calculate_volatility_based_size(strategy_name, symbol, price, strategy_allocation, risk_percentage)
            elif method == "equal_weight":
                return self._calculate_equal_weight_size(strategy_name, symbol, price, strategy_allocation)
            elif method == "kelly":
                return self._calculate_kelly_size(strategy_name, symbol, price, strategy_allocation, risk_percentage)
            else:
                logger.warning(f"Unknown sizing method: {method}, using percentage")
                return self._calculate_percentage_size(strategy_name, symbol, price, strategy_allocation, risk_percentage)
                
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {"error": str(e)}
    
    def _calculate_percentage_size(self, strategy_name: str, symbol: str, price: float,
                                 strategy_allocation: Dict[str, float],
                                 risk_percentage: Optional[float] = None) -> Dict[str, Any]:
        """Calculate position size based on fixed percentage of allocation."""
        try:
            allocation_amount = strategy_allocation.get("amount", 0)
            max_positions = strategy_allocation.get("max_positions", 5)
            
            # Calculate position value as fraction of allocation
            position_value = allocation_amount / max_positions
            
            # Apply single stock limit
            single_stock_limit = self.risk_config.get("single_stock_limit", 10.0) / 100.0
            max_position_value = allocation_amount * single_stock_limit
            position_value = min(position_value, max_position_value)
            
            # Calculate quantity
            quantity = int(position_value / price) if price > 0 else 0
            actual_position_value = quantity * price
            
            # Calculate risk
            risk_pct = risk_percentage or self.global_config.get("max_risk_per_trade", 2.0)
            risk_amount = allocation_amount * (risk_pct / 100.0)
            
            return {
                "symbol": symbol,
                "strategy": strategy_name,
                "sizing_method": "percentage",
                "quantity": quantity,
                "price": price,
                "position_value": actual_position_value,
                "risk_amount": risk_amount,
                "risk_percentage": risk_pct,
                "max_loss": actual_position_value * 0.05,  # Assuming 5% stop loss
                "allocation_used": (actual_position_value / allocation_amount * 100) if allocation_amount > 0 else 0,
                "stop_loss_price": price * 0.95,  # 5% stop loss
                "target_price": price * 1.10,     # 10% target
                "risk_reward_ratio": 2.0
            }
            
        except Exception as e:
            logger.error(f"Error in percentage sizing: {e}")
            return {"error": str(e)}
    
    def _calculate_risk_based_size(self, strategy_name: str, symbol: str, price: float,
                                 strategy_allocation: Dict[str, float],
                                 risk_percentage: Optional[float] = None,
                                 stop_loss_pct: float = 0.05) -> Dict[str, Any]:
        """Calculate position size based on risk amount."""
        try:
            allocation_amount = strategy_allocation.get("amount", 0)
            risk_pct = risk_percentage or self.global_config.get("max_risk_per_trade", 2.0)
            risk_amount = allocation_amount * (risk_pct / 100.0)
            
            # Calculate position size based on risk
            position_value = risk_amount / stop_loss_pct if stop_loss_pct > 0 else 0
            
            # Apply limits
            single_stock_limit = self.risk_config.get("single_stock_limit", 10.0) / 100.0
            max_position_value = allocation_amount * single_stock_limit
            position_value = min(position_value, max_position_value)
            
            # Calculate quantity
            quantity = int(position_value / price) if price > 0 else 0
            actual_position_value = quantity * price
            actual_risk = actual_position_value * stop_loss_pct
            
            return {
                "symbol": symbol,
                "strategy": strategy_name,
                "sizing_method": "risk_based",
                "quantity": quantity,
                "price": price,
                "position_value": actual_position_value,
                "risk_amount": actual_risk,
                "risk_percentage": (actual_risk / allocation_amount * 100) if allocation_amount > 0 else 0,
                "max_loss": actual_risk,
                "allocation_used": (actual_position_value / allocation_amount * 100) if allocation_amount > 0 else 0,
                "stop_loss_price": price * (1 - stop_loss_pct),
                "stop_loss_percentage": stop_loss_pct * 100,
                "target_price": price * (1 + stop_loss_pct * 2),  # 2:1 risk-reward
                "risk_reward_ratio": 2.0
            }
            
        except Exception as e:
            logger.error(f"Error in risk-based sizing: {e}")
            return {"error": str(e)}
    
    def _calculate_volatility_based_size(self, strategy_name: str, symbol: str, price: float,
                                       strategy_allocation: Dict[str, float],
                                       risk_percentage: Optional[float] = None) -> Dict[str, Any]:
        """Calculate position size based on volatility (simplified version)."""
        try:
            # This is a simplified volatility-based sizing
            # In practice, you would use historical volatility data
            
            allocation_amount = strategy_allocation.get("amount", 0)
            base_volatility = 0.20  # Assume 20% annual volatility as base
            symbol_volatility = 0.25  # This would come from historical data
            
            # Adjust position size based on volatility
            volatility_adjustment = base_volatility / symbol_volatility
            base_position_value = allocation_amount / strategy_allocation.get("max_positions", 5)
            adjusted_position_value = base_position_value * volatility_adjustment
            
            # Apply limits
            single_stock_limit = self.risk_config.get("single_stock_limit", 10.0) / 100.0
            max_position_value = allocation_amount * single_stock_limit
            position_value = min(adjusted_position_value, max_position_value)
            
            # Calculate quantity
            quantity = int(position_value / price) if price > 0 else 0
            actual_position_value = quantity * price
            
            # Calculate risk based on volatility
            risk_pct = risk_percentage or self.global_config.get("max_risk_per_trade", 2.0)
            volatility_risk = actual_position_value * symbol_volatility * 0.1  # 10% of volatility as risk
            
            return {
                "symbol": symbol,
                "strategy": strategy_name,
                "sizing_method": "volatility_based",
                "quantity": quantity,
                "price": price,
                "position_value": actual_position_value,
                "risk_amount": volatility_risk,
                "risk_percentage": (volatility_risk / allocation_amount * 100) if allocation_amount > 0 else 0,
                "max_loss": volatility_risk,
                "allocation_used": (actual_position_value / allocation_amount * 100) if allocation_amount > 0 else 0,
                "volatility": symbol_volatility,
                "volatility_adjustment": volatility_adjustment,
                "stop_loss_price": price * 0.90,  # 10% stop loss for volatile stocks
                "target_price": price * 1.20,     # 20% target
                "risk_reward_ratio": 2.0
            }
            
        except Exception as e:
            logger.error(f"Error in volatility-based sizing: {e}")
            return {"error": str(e)}
    
    def _calculate_equal_weight_size(self, strategy_name: str, symbol: str, price: float,
                                   strategy_allocation: Dict[str, float]) -> Dict[str, Any]:
        """Calculate equal weight position size."""
        try:
            allocation_amount = strategy_allocation.get("amount", 0)
            max_positions = strategy_allocation.get("max_positions", 5)
            
            # Equal weight across all positions
            position_value = allocation_amount / max_positions
            
            # Calculate quantity
            quantity = int(position_value / price) if price > 0 else 0
            actual_position_value = quantity * price
            
            # Calculate risk
            risk_pct = self.global_config.get("max_risk_per_trade", 2.0)
            risk_amount = actual_position_value * (risk_pct / 100.0)
            
            return {
                "symbol": symbol,
                "strategy": strategy_name,
                "sizing_method": "equal_weight",
                "quantity": quantity,
                "price": price,
                "position_value": actual_position_value,
                "risk_amount": risk_amount,
                "risk_percentage": risk_pct,
                "max_loss": risk_amount,
                "allocation_used": (actual_position_value / allocation_amount * 100) if allocation_amount > 0 else 0,
                "weight": 1.0 / max_positions,
                "stop_loss_price": price * 0.95,
                "target_price": price * 1.10,
                "risk_reward_ratio": 2.0
            }
            
        except Exception as e:
            logger.error(f"Error in equal weight sizing: {e}")
            return {"error": str(e)}
    
    def _calculate_kelly_size(self, strategy_name: str, symbol: str, price: float,
                            strategy_allocation: Dict[str, float],
                            risk_percentage: Optional[float] = None) -> Dict[str, Any]:
        """Calculate position size using Kelly Criterion (simplified)."""
        try:
            # Simplified Kelly Criterion calculation
            # In practice, you would use historical win rate and average win/loss
            
            win_rate = 0.55  # 55% win rate (would come from strategy performance)
            avg_win = 0.08   # 8% average win
            avg_loss = 0.04  # 4% average loss
            
            # Kelly formula: f = (bp - q) / b
            # where b = odds received (avg_win/avg_loss), p = win probability, q = loss probability
            b = avg_win / avg_loss if avg_loss > 0 else 2.0
            p = win_rate
            q = 1 - win_rate
            
            kelly_fraction = (b * p - q) / b if b > 0 else 0
            kelly_fraction = max(0, min(kelly_fraction, 0.25))  # Cap at 25%
            
            allocation_amount = strategy_allocation.get("amount", 0)
            position_value = allocation_amount * kelly_fraction
            
            # Apply limits
            single_stock_limit = self.risk_config.get("single_stock_limit", 10.0) / 100.0
            max_position_value = allocation_amount * single_stock_limit
            position_value = min(position_value, max_position_value)
            
            # Calculate quantity
            quantity = int(position_value / price) if price > 0 else 0
            actual_position_value = quantity * price
            
            # Calculate risk
            risk_amount = actual_position_value * avg_loss
            
            return {
                "symbol": symbol,
                "strategy": strategy_name,
                "sizing_method": "kelly",
                "quantity": quantity,
                "price": price,
                "position_value": actual_position_value,
                "risk_amount": risk_amount,
                "risk_percentage": (risk_amount / allocation_amount * 100) if allocation_amount > 0 else 0,
                "max_loss": risk_amount,
                "allocation_used": (actual_position_value / allocation_amount * 100) if allocation_amount > 0 else 0,
                "kelly_fraction": kelly_fraction,
                "win_rate": win_rate,
                "avg_win": avg_win,
                "avg_loss": avg_loss,
                "stop_loss_price": price * (1 - avg_loss),
                "target_price": price * (1 + avg_win),
                "risk_reward_ratio": avg_win / avg_loss
            }
            
        except Exception as e:
            logger.error(f"Error in Kelly sizing: {e}")
            return {"error": str(e)}
    
    def calculate_batch_sizes(self, trades: List[Dict[str, Any]], 
                            strategy_allocations: Dict[str, Dict[str, float]]) -> List[Dict[str, Any]]:
        """
        Calculate position sizes for multiple trades.
        
        Args:
            trades: List of trade dictionaries
            strategy_allocations: Strategy allocation information
            
        Returns:
            List of trades with calculated position sizes
        """
        try:
            sized_trades = []
            
            for trade in trades:
                strategy = trade.get("strategy")
                symbol = trade.get("symbol")
                price = trade.get("price")
                
                if strategy in strategy_allocations:
                    position_info = self.calculate_position_size(
                        strategy, symbol, price, strategy_allocations[strategy]
                    )
                    
                    # Merge trade info with position sizing
                    sized_trade = {**trade, **position_info}
                    sized_trades.append(sized_trade)
                else:
                    logger.warning(f"Strategy {strategy} not found in allocations")
                    sized_trades.append({**trade, "error": "Strategy not allocated"})
            
            return sized_trades
            
        except Exception as e:
            logger.error(f"Error calculating batch sizes: {e}")
            return []
