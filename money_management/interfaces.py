"""
Money Management Module Interfaces

Defines the interfaces that external systems must implement to integrate
with the money management module. This allows the module to work with
any broker, data provider, or trading system.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime


class BrokerInterface(ABC):
    """
    Interface that any broker must implement to work with the money management module.

    This allows the money manager to work with any broker (DhanHQ, Zerodha, etc.)
    as long as they implement these required methods.
    """

    @abstractmethod
    def get_fund_limit(self) -> Dict[str, Any]:
        """
        Get fund/balance information from the broker.

        Returns:
            Dictionary containing balance information with keys:
            - totalBalance: Total account balance
            - availableBalance: Available balance for trading
            - utilizedAmount: Currently used margin
            - withdrawableBalance: Amount that can be withdrawn
            - blockedPayoutAmount: Blocked amount (optional)
        """
        pass

    @abstractmethod
    def get_holdings(self) -> List[Dict[str, Any]]:
        """
        Get current holdings from the broker.

        Returns:
            List of holdings with each holding containing:
            - symbol: Trading symbol
            - quantity: Number of shares held
            - avgCostPrice: Average cost price
            - currentPrice: Current market price (optional)
            - unrealizedPnl: Unrealized P&L (optional)
        """
        pass

    @abstractmethod
    def get_positions(self) -> List[Dict[str, Any]]:
        """
        Get current positions from the broker.

        Returns:
            List of positions with each position containing:
            - symbol: Trading symbol
            - quantity: Position quantity (positive for long, negative for short)
            - avgPrice: Average price
            - unrealizedPnl: Unrealized P&L
            - realizedPnl: Realized P&L (optional)
        """
        pass

    @abstractmethod
    def is_dry_run(self) -> bool:
        """
        Check if the broker is in dry run/simulation mode.

        Returns:
            True if in dry run mode, False for live trading
        """
        pass

    def get_user_profile(self) -> Dict[str, Any]:
        """
        Get user profile information (optional).

        Returns:
            Dictionary with user profile information
        """
        return {}

    def calculate_margin(self, symbol: str, quantity: int, price: float,
                        product_type: str = "CNC") -> Dict[str, Any]:
        """
        Calculate margin requirements for a trade (optional).

        Args:
            symbol: Trading symbol
            quantity: Number of shares
            price: Price per share
            product_type: Product type (CNC, INTRADAY, etc.)

        Returns:
            Dictionary with margin calculation details
        """
        return {"totalMargin": quantity * price}


class BalanceProvider(ABC):
    """
    Interface for any system that can provide balance information.

    This is a simpler interface for systems that only need to provide
    balance data without full broker functionality.
    """

    @abstractmethod
    def get_available_balance(self) -> float:
        """
        Get available balance for trading.

        Returns:
            Available balance as float
        """
        pass

    @abstractmethod
    def get_total_balance(self) -> float:
        """
        Get total account balance.

        Returns:
            Total balance as float
        """
        pass

    def get_used_margin(self) -> float:
        """
        Get currently used margin (optional).

        Returns:
            Used margin as float
        """
        return 0.0


class PortfolioProvider(ABC):
    """
    Interface for systems that can provide portfolio information.
    """

    @abstractmethod
    def get_portfolio_value(self) -> float:
        """
        Get current portfolio value.

        Returns:
            Total portfolio value as float
        """
        pass

    @abstractmethod
    def get_portfolio_positions(self) -> List[Dict[str, Any]]:
        """
        Get current portfolio positions.

        Returns:
            List of position dictionaries
        """
        pass

    def get_portfolio_risk(self) -> float:
        """
        Get current portfolio risk (optional).

        Returns:
            Portfolio risk as float
        """
        return 0.0


class ConfigProvider(ABC):
    """
    Interface for configuration providers.
    """

    @abstractmethod
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from specified path.

        Args:
            config_path: Path to configuration file

        Returns:
            Configuration dictionary
        """
        pass

    @abstractmethod
    def save_config(self, config: Dict[str, Any], config_path: str) -> bool:
        """
        Save configuration to specified path.

        Args:
            config: Configuration dictionary
            config_path: Path to save configuration

        Returns:
            True if successful
        """
        pass


class NotificationProvider(ABC):
    """
    Interface for notification systems.
    """

    @abstractmethod
    def send_notification(self, message: str, level: str = "info") -> bool:
        """
        Send a notification.

        Args:
            message: Notification message
            level: Notification level (info, warning, error)

        Returns:
            True if notification sent successfully
        """
        pass


class DataProvider(ABC):
    """
    Interface for market data providers.
    """

    @abstractmethod
    def get_current_price(self, symbol: str) -> Optional[float]:
        """
        Get current market price for a symbol.

        Args:
            symbol: Trading symbol

        Returns:
            Current price or None if not available
        """
        pass

    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """
        Get symbol information (optional).

        Args:
            symbol: Trading symbol

        Returns:
            Symbol information dictionary
        """
        return {}


# Adapter classes for easy integration

class BrokerAdapter:
    """
    Adapter to convert any broker to the BrokerInterface.

    Use this if your broker doesn't directly implement BrokerInterface.
    """

    def __init__(self, broker, method_mapping: Dict[str, str] = None):
        """
        Initialize adapter with broker and method mapping.

        Args:
            broker: The broker instance to adapt
            method_mapping: Dictionary mapping interface methods to broker methods
        """
        self.broker = broker
        self.method_mapping = method_mapping or {}

    def get_fund_limit(self) -> Dict[str, Any]:
        method_name = self.method_mapping.get('get_fund_limit', 'get_fund_limit')
        return getattr(self.broker, method_name)()

    def get_holdings(self) -> List[Dict[str, Any]]:
        method_name = self.method_mapping.get('get_holdings', 'get_holdings')
        return getattr(self.broker, method_name)()

    def get_positions(self) -> List[Dict[str, Any]]:
        method_name = self.method_mapping.get('get_positions', 'get_positions')
        return getattr(self.broker, method_name)()

    def is_dry_run(self) -> bool:
        method_name = self.method_mapping.get('is_dry_run', 'is_dry_run')
        return getattr(self.broker, method_name)()

    def is_live_data_enabled(self) -> bool:
        """Check if live data is enabled in the underlying broker."""
        if hasattr(self.broker, 'is_live_data_enabled'):
            return self.broker.is_live_data_enabled()
        return False


class SimpleBalanceProvider(BalanceProvider):
    """
    Simple implementation of BalanceProvider for testing or basic use.
    """

    def __init__(self, available_balance: float, total_balance: float = None, used_margin: float = 0.0):
        self.available_balance = available_balance
        self.total_balance = total_balance or available_balance
        self.used_margin = used_margin

    def get_available_balance(self) -> float:
        return self.available_balance

    def get_total_balance(self) -> float:
        return self.total_balance

    def get_used_margin(self) -> float:
        return self.used_margin
