"""
Money Management Configuration Manager

Handles all configuration management for the money management module.
Provides a clean interface for loading, saving, and validating configurations.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

logger = logging.getLogger("MoneyManagement.ConfigManager")


class ConfigManager:
    """
    Manages configuration for the money management module.
    
    Handles loading, saving, validation, and default configuration creation.
    """
    
    def __init__(self, config_path: str = None, auto_create: bool = True):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Path to configuration file
            auto_create: Whether to auto-create default config if not found
        """
        self.config_path = config_path or "money_management_config.json"
        self.auto_create = auto_create
        self._config = None
        
        # Load configuration on initialization
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        Load configuration from file.
        
        Returns:
            Configuration dictionary
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    self._config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
            else:
                if self.auto_create:
                    self._config = self._create_default_config()
                    self.save_config()
                    logger.info(f"Created default configuration at {self.config_path}")
                else:
                    raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            # Validate configuration
            if not self._validate_config(self._config):
                logger.warning("Configuration validation failed, using defaults")
                self._config = self._create_default_config()
                
            return self._config
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self._config = self._create_default_config()
            return self._config
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """
        Save configuration to file.
        
        Args:
            config: Configuration to save (uses current config if None)
            
        Returns:
            True if successful
        """
        try:
            config_to_save = config or self._config
            if not config_to_save:
                logger.error("No configuration to save")
                return False
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # Update timestamp
            config_to_save["last_updated"] = datetime.now().isoformat()
            
            with open(self.config_path, 'w') as f:
                json.dump(config_to_save, f, indent=4)
            
            self._config = config_to_save
            logger.info(f"Saved configuration to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get current configuration.
        
        Returns:
            Configuration dictionary
        """
        if self._config is None:
            self.load_config()
        return self._config.copy()
    
    def update_config(self, updates: Dict[str, Any], save: bool = True) -> bool:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of updates to apply
            save: Whether to save after updating
            
        Returns:
            True if successful
        """
        try:
            if self._config is None:
                self.load_config()
            
            # Deep update configuration
            self._deep_update(self._config, updates)
            
            if save:
                return self.save_config()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            return False
    
    def get_setting(self, key_path: str, default: Any = None) -> Any:
        """
        Get a specific setting using dot notation.
        
        Args:
            key_path: Dot-separated path to setting (e.g., "global_settings.max_risk_per_trade")
            default: Default value if setting not found
            
        Returns:
            Setting value or default
        """
        try:
            if self._config is None:
                self.load_config()
            
            keys = key_path.split('.')
            value = self._config
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.error(f"Error getting setting {key_path}: {e}")
            return default
    
    def set_setting(self, key_path: str, value: Any, save: bool = True) -> bool:
        """
        Set a specific setting using dot notation.
        
        Args:
            key_path: Dot-separated path to setting
            value: Value to set
            save: Whether to save after setting
            
        Returns:
            True if successful
        """
        try:
            if self._config is None:
                self.load_config()
            
            keys = key_path.split('.')
            config = self._config
            
            # Navigate to parent of target key
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # Set the value
            config[keys[-1]] = value
            
            if save:
                return self.save_config()
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting {key_path}: {e}")
            return False
    
    def reset_to_defaults(self, save: bool = True) -> bool:
        """
        Reset configuration to defaults.
        
        Args:
            save: Whether to save after resetting
            
        Returns:
            True if successful
        """
        try:
            self._config = self._create_default_config()
            
            if save:
                return self.save_config()
            
            return True
            
        except Exception as e:
            logger.error(f"Error resetting configuration: {e}")
            return False
    
    def export_config(self, export_path: str) -> bool:
        """
        Export configuration to a different file.
        
        Args:
            export_path: Path to export configuration
            
        Returns:
            True if successful
        """
        try:
            if self._config is None:
                self.load_config()
            
            with open(export_path, 'w') as f:
                json.dump(self._config, f, indent=4)
            
            logger.info(f"Exported configuration to {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting configuration: {e}")
            return False
    
    def import_config(self, import_path: str, save: bool = True) -> bool:
        """
        Import configuration from a file.
        
        Args:
            import_path: Path to import configuration from
            save: Whether to save after importing
            
        Returns:
            True if successful
        """
        try:
            with open(import_path, 'r') as f:
                imported_config = json.load(f)
            
            if self._validate_config(imported_config):
                self._config = imported_config
                
                if save:
                    return self.save_config()
                
                logger.info(f"Imported configuration from {import_path}")
                return True
            else:
                logger.error("Invalid configuration format in import file")
                return False
                
        except Exception as e:
            logger.error(f"Error importing configuration: {e}")
            return False
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration."""
        return {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "global_settings": {
                "total_capital_usage": 90.0,
                "emergency_reserve": 10.0,
                "max_risk_per_trade": 2.0,
                "max_total_risk": 15.0,
                "rebalance_frequency": "daily",
                "auto_rebalance": True,
                "stop_loss_global": 20.0
            },
            "allocation_model": {
                "type": "percentage",
                "strategies": {},
                "default_allocation": 10.0
            },
            "risk_management": {
                "position_sizing": "percentage",
                "max_positions_per_strategy": 5,
                "correlation_limit": 0.7,
                "sector_concentration_limit": 30.0,
                "single_stock_limit": 10.0
            },
            "broker_settings": {
                "balance_refresh_interval": 300,
                "margin_buffer": 5.0,
                "auto_fetch_balance": True
            }
        }
    
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate configuration structure."""
        required_sections = ["global_settings", "allocation_model", "risk_management", "broker_settings"]
        
        for section in required_sections:
            if section not in config:
                logger.error(f"Missing required section: {section}")
                return False
        
        return True
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """Deep update dictionary."""
        for key, value in update_dict.items():
            if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
