version: '3.8'

services:
  # RapidTrader Unified API Gateway
  rapidtrader-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rapidtrader-unified-api
    ports:
      - "8000:8000"
    volumes:
      - ./userdata:/rapidtrader/userdata
      - ./logs:/rapidtrader/logs
      - ./broker:/rapidtrader/broker  # For symbol mappers
      - /var/run/docker.sock:/var/run/docker.sock  # For container management
    environment:
      - RAPIDTRADER_MODE=api
      - PYTHONPATH=/rapidtrader
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=unified-api"
      - "rapidtrader.version=2.0"
      - "rapidtrader.managed=true"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["python", "-m", "api_gateway.unified_api"]

  # RapidTrader Frontend
  rapidtrader-frontend:
    build:
      context: ./frontend_v2
      dockerfile: Dockerfile
    container_name: rapidtrader-frontend
    ports:
      - "3000:80"
    depends_on:
      - rapidtrader-api
    restart: unless-stopped
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=frontend"
      - "rapidtrader.version=2.0"
      - "rapidtrader.managed=true"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Trading containers will be managed dynamically by the API
  # No need to define them here - they'll be created on demand

networks:
  rapidtrader-network:
    driver: bridge
    labels:
      - "rapidtrader.network=main"
