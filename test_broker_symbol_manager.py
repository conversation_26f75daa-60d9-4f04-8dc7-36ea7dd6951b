#!/usr/bin/env python3
"""
Test script for the new Broker-Specific Symbol Manager

This script demonstrates:
1. Registering multiple brokers
2. Downloading broker-specific symbols
3. Creating unified symbol mappings
4. Searching across brokers
5. Testing the complete workflow
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.symbol_manager import BrokerSymbolManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("test_broker_symbol_manager")

def test_broker_registration():
    """Test broker registration functionality"""
    print("🔧 Testing Broker Registration...")
    
    manager = BrokerSymbolManager()
    
    # Test broker configurations
    test_brokers = {
        "fyers": {
            "client_id": "KQL8MIGYUG-100",
            "access_token": "demo_access_token",
            "dry_run": True,
            "live_data": False
        },
        "dhan": {
            "client_id": "demo_dhan_client",
            "access_token": "demo_dhan_token",
            "dry_run": True,
            "live_data": False
        },
        "zerodha": {
            "api_key": "demo_zerodha_key",
            "access_token": "demo_zerodha_token",
            "dry_run": True
        }
    }
    
    results = {}
    
    for broker_name, config in test_brokers.items():
        print(f"  📝 Registering {broker_name}...")
        success = manager.register_broker_config(broker_name, config)
        results[broker_name] = success
        
        if success:
            print(f"    ✅ {broker_name} registered successfully")
        else:
            print(f"    ❌ {broker_name} registration failed")
    
    return results

def test_symbol_download():
    """Test symbol download for registered brokers"""
    print("\n📥 Testing Symbol Download...")
    
    manager = BrokerSymbolManager()
    
    # Get list of registered brokers
    brokers = list(manager.broker_configs.keys())
    
    if not brokers:
        print("  ⚠️  No brokers registered. Run registration test first.")
        return {}
    
    results = {}
    
    for broker in brokers:
        print(f"  📊 Downloading symbols for {broker}...")
        success = manager.update_broker_symbols(broker)
        results[broker] = success
        
        if success:
            # Get symbol count
            symbols = manager.load_broker_symbols(broker)
            print(f"    ✅ {broker}: {len(symbols)} symbols downloaded")
        else:
            print(f"    ❌ {broker}: Symbol download failed")
    
    return results

def test_symbol_search():
    """Test symbol search functionality"""
    print("\n🔍 Testing Symbol Search...")
    
    manager = BrokerSymbolManager()
    
    # Test searches
    test_queries = ["RELIANCE", "TCS", "HDFC", "SBIN"]
    
    for query in test_queries:
        print(f"  🔎 Searching for '{query}'...")
        
        # Search across all brokers
        results = manager.search_broker_symbols(query, limit=5)
        
        if results:
            print(f"    ✅ Found {len(results)} matches:")
            for symbol in results[:3]:  # Show first 3
                broker = symbol.get('broker', 'unknown')
                name = symbol.get('name', 'N/A')
                exchange = symbol.get('exchange', 'N/A')
                print(f"      • {symbol['symbol']} - {name} ({exchange}) [{broker}]")
        else:
            print(f"    ❌ No matches found for '{query}'")

def test_broker_specific_search():
    """Test broker-specific search"""
    print("\n🎯 Testing Broker-Specific Search...")
    
    manager = BrokerSymbolManager()
    
    brokers = list(manager.broker_configs.keys())
    
    if not brokers:
        print("  ⚠️  No brokers registered.")
        return
    
    # Test search in specific broker
    test_broker = brokers[0]  # Use first registered broker
    query = "RELIANCE"
    
    print(f"  🔎 Searching for '{query}' in {test_broker}...")
    results = manager.search_broker_symbols(query, test_broker, limit=3)
    
    if results:
        print(f"    ✅ Found {len(results)} matches in {test_broker}:")
        for symbol in results:
            name = symbol.get('name', 'N/A')
            broker_symbol = symbol.get('broker_symbol', 'N/A')
            print(f"      • {symbol['symbol']} → {broker_symbol} ({name})")
    else:
        print(f"    ❌ No matches found in {test_broker}")

def test_symbol_mapping():
    """Test symbol mapping functionality"""
    print("\n🔗 Testing Symbol Mapping...")
    
    manager = BrokerSymbolManager()
    
    brokers = list(manager.broker_configs.keys())
    
    if not brokers:
        print("  ⚠️  No brokers registered.")
        return
    
    # Test symbol mappings
    test_symbols = ["RELIANCE", "TCS", "SBIN"]
    
    for symbol in test_symbols:
        print(f"  🔗 Getting mappings for '{symbol}':")
        
        for broker in brokers:
            mapping = manager.get_broker_symbol_mapping(symbol, broker)
            if mapping:
                print(f"    • {broker}: {symbol} → {mapping}")
            else:
                print(f"    • {broker}: No mapping found")

def test_master_contract():
    """Test master contract creation"""
    print("\n📋 Testing Master Contract Creation...")
    
    manager = BrokerSymbolManager()
    
    success = manager.create_broker_master_contract()
    
    if success:
        print("  ✅ Master contract created successfully")
        
        # Load and display summary
        if manager.master_contract_file.exists():
            with open(manager.master_contract_file, 'r') as f:
                master_contract = json.load(f)
            
            total_symbols = master_contract.get('total_symbols', 0)
            total_brokers = master_contract.get('total_brokers', 0)
            
            print(f"    📊 Summary: {total_symbols} symbols from {total_brokers} brokers")
            
            for broker, info in master_contract.get('brokers', {}).items():
                count = info.get('count', 0)
                status = info.get('status', 'unknown')
                print(f"      • {broker}: {count} symbols ({status})")
    else:
        print("  ❌ Master contract creation failed")

def test_broker_status():
    """Test broker status functionality"""
    print("\n📊 Testing Broker Status...")
    
    manager = BrokerSymbolManager()
    
    status = manager.get_broker_status()
    
    if status:
        print(f"  📈 Broker Status Summary:")
        print(f"    Total Brokers: {status.get('total_brokers', 0)}")
        
        summary = status.get('summary', {})
        print(f"    Active: {summary.get('active', 0)}")
        print(f"    Error: {summary.get('error', 0)}")
        print(f"    Registered: {summary.get('registered', 0)}")
        
        print(f"  📋 Detailed Status:")
        for broker, info in status.get('brokers', {}).items():
            status_str = info['status']
            count = info['symbol_count']
            print(f"    • {broker}: {status_str} ({count} symbols)")
    else:
        print("  ❌ Failed to get broker status")

def test_file_structure():
    """Test file structure creation"""
    print("\n📁 Testing File Structure...")
    
    manager = BrokerSymbolManager()
    
    # Check main directories
    directories = [
        manager.data_dir,
        manager.brokers_dir
    ]
    
    for directory in directories:
        if directory.exists():
            print(f"  ✅ {directory} exists")
        else:
            print(f"  ❌ {directory} missing")
    
    # Check broker-specific directories
    for broker in manager.broker_configs.keys():
        broker_dir = manager.brokers_dir / broker
        if broker_dir.exists():
            print(f"  ✅ {broker_dir} exists")
            
            # Check for symbol files
            symbol_file = broker_dir / f"{broker}_symbols.json"
            csv_file = broker_dir / f"{broker}_symbols.csv"
            
            if symbol_file.exists():
                print(f"    ✅ {symbol_file.name} exists")
            if csv_file.exists():
                print(f"    ✅ {csv_file.name} exists")
        else:
            print(f"  ❌ {broker_dir} missing")

def main():
    """Run all tests"""
    print("🚀 Starting Broker Symbol Manager Tests\n")
    
    try:
        # Run tests in sequence
        test_broker_registration()
        test_symbol_download()
        test_symbol_search()
        test_broker_specific_search()
        test_symbol_mapping()
        test_master_contract()
        test_broker_status()
        test_file_structure()
        
        print("\n🎉 All tests completed!")
        print("\n📖 Next steps:")
        print("  1. Check data/symbols/brokers/ for downloaded symbols")
        print("  2. Review data/symbols/master_contract.json")
        print("  3. Test CLI commands:")
        print("     python data/symbol_manager.py --list-brokers")
        print("     python data/symbol_manager.py --search RELIANCE")
        print("     python data/symbol_manager.py --broker-status")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.error(f"Test error: {e}", exc_info=True)

if __name__ == "__main__":
    main()
