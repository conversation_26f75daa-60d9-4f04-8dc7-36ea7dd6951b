<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RapidTrader Dashboard</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 2rem;
            font-weight: 300;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border: 1px solid #404040;
        }
        
        .card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background-color: #4CAF50; }
        .status-stopped { background-color: #f44336; }
        .status-dry-run { background-color: #ff9800; }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #388e3c 100%);
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #404040;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-value {
            font-weight: bold;
            color: #667eea;
        }
        
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        
        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        select, input {
            background: #404040;
            color: white;
            border: 1px solid #555;
            padding: 0.5rem;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .market-data {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .symbol-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            border-bottom: 1px solid #404040;
        }
        
        .symbol-row:hover {
            background: #404040;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }
        
        .log-container {
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 5px;
            padding: 1rem;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
        }
        
        .log-info { color: #2196F3; }
        .log-success { color: #4CAF50; }
        .log-warning { color: #ff9800; }
        .log-error { color: #f44336; }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 RapidTrader Dashboard</h1>
    </div>
    
    <div class="container">
        <!-- Trading Controls -->
        <div class="card">
            <h3>Trading Controls</h3>
            <div class="controls">
                <select id="brokerSelect">
                    <option value="fyers">Fyers</option>
                    <option value="dhan">DhanHQ</option>
                </select>
                <select id="modeSelect">
                    <option value="dry_run">Dry Run</option>
                    <option value="live">Live Trading</option>
                </select>
                <select id="strategySelect">
                    <option value="DefaultStrategy">Default Strategy</option>
                </select>
            </div>
            <div class="controls">
                <button class="btn btn-success" onclick="startTrading()">Start Trading</button>
                <button class="btn btn-danger" onclick="stopTrading()">Stop Trading</button>
                <button class="btn" onclick="refreshData()">Refresh</button>
            </div>
        </div>
        
        <div class="grid">
            <!-- Trading Status -->
            <div class="card">
                <h3>Trading Status</h3>
                <div class="metric">
                    <span>Status:</span>
                    <span id="tradingStatus" class="metric-value">
                        <span class="status-indicator status-stopped"></span>Stopped
                    </span>
                </div>
                <div class="metric">
                    <span>Mode:</span>
                    <span id="tradingMode" class="metric-value">Dry Run</span>
                </div>
                <div class="metric">
                    <span>Broker:</span>
                    <span id="tradingBroker" class="metric-value">Fyers</span>
                </div>
                <div class="metric">
                    <span>Strategy:</span>
                    <span id="tradingStrategy" class="metric-value">-</span>
                </div>
                <div class="metric">
                    <span>Runtime:</span>
                    <span id="tradingRuntime" class="metric-value">-</span>
                </div>
            </div>
            
            <!-- Portfolio Summary -->
            <div class="card">
                <h3>Portfolio Summary</h3>
                <div class="metric">
                    <span>Available Cash:</span>
                    <span id="availableCash" class="metric-value">₹0.00</span>
                </div>
                <div class="metric">
                    <span>Total Value:</span>
                    <span id="totalValue" class="metric-value">₹0.00</span>
                </div>
                <div class="metric">
                    <span>P&L Today:</span>
                    <span id="dailyPnL" class="metric-value">₹0.00</span>
                </div>
                <div class="metric">
                    <span>Open Positions:</span>
                    <span id="openPositions" class="metric-value">0</span>
                </div>
                <div class="metric">
                    <span>Total Trades:</span>
                    <span id="totalTrades" class="metric-value">0</span>
                </div>
            </div>
            
            <!-- Broker Connection -->
            <div class="card">
                <h3>Broker Connection</h3>
                <div id="brokerStatus">
                    <div class="metric">
                        <span>Fyers:</span>
                        <span class="metric-value">
                            <span class="status-indicator status-stopped"></span>Disconnected
                        </span>
                    </div>
                    <div class="metric">
                        <span>DhanHQ:</span>
                        <span class="metric-value">
                            <span class="status-indicator status-stopped"></span>Disconnected
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="grid">
            <!-- Live Market Data -->
            <div class="card">
                <h3>Live Market Data</h3>
                <div class="controls">
                    <button class="btn" onclick="subscribeMarketData()">Subscribe to Market Data</button>
                </div>
                <div id="marketData" class="market-data">
                    <div class="symbol-row">
                        <span>Symbol</span>
                        <span>LTP</span>
                        <span>Change</span>
                    </div>
                </div>
            </div>
            
            <!-- Performance Chart -->
            <div class="card">
                <h3>Performance Chart</h3>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Activity Log -->
        <div class="card">
            <h3>Activity Log</h3>
            <div id="activityLog" class="log-container">
                <div class="log-entry log-info">[INFO] RapidTrader Dashboard initialized</div>
            </div>
        </div>
    </div>
    
    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Chart initialization
        const ctx = document.getElementById('performanceChart').getContext('2d');
        const performanceChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Portfolio Value',
                    data: [],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#ffffff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#ffffff' },
                        grid: { color: '#404040' }
                    },
                    y: {
                        ticks: { color: '#ffffff' },
                        grid: { color: '#404040' }
                    }
                }
            }
        });
        
        // Socket event handlers
        socket.on('connect', function() {
            addLogEntry('Connected to RapidTrader', 'success');
            refreshData();
        });
        
        socket.on('trading_status', function(data) {
            updateTradingStatus(data);
        });
        
        socket.on('market_data', function(data) {
            updateMarketData(data);
        });
        
        // Trading control functions
        function startTrading() {
            const broker = document.getElementById('brokerSelect').value;
            const mode = document.getElementById('modeSelect').value;
            const strategy = document.getElementById('strategySelect').value;
            
            fetch('/api/trading/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    broker: broker,
                    mode: mode,
                    strategy: strategy
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    addLogEntry(`Trading started: ${data.message}`, 'success');
                } else {
                    addLogEntry(`Error: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                addLogEntry(`Error starting trading: ${error}`, 'error');
            });
        }
        
        function stopTrading() {
            fetch('/api/trading/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    addLogEntry(`Trading stopped: ${data.message}`, 'warning');
                } else {
                    addLogEntry(`Error: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                addLogEntry(`Error stopping trading: ${error}`, 'error');
            });
        }
        
        function refreshData() {
            // Refresh trading status
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateTradingStatus(data.data);
                    }
                });
            
            // Refresh broker status
            fetch('/api/brokers')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateBrokerStatus(data.data);
                    }
                });
            
            // Refresh portfolio data
            const currentBroker = document.getElementById('brokerSelect').value;
            fetch(`/api/portfolio/${currentBroker}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updatePortfolioData(data.data);
                    }
                });
        }
        
        function subscribeMarketData() {
            const symbols = ['NSE:SBIN-EQ', 'NSE:RELIANCE-EQ', 'NSE:TCS-EQ'];
            const broker = document.getElementById('brokerSelect').value;
            
            socket.emit('subscribe_market_data', {
                broker: broker,
                symbols: symbols
            });
            
            addLogEntry(`Subscribed to market data for ${symbols.length} symbols`, 'info');
        }
        
        function updateTradingStatus(data) {
            const statusElement = document.getElementById('tradingStatus');
            const statusClass = data.status === 'running' ? 'status-running' : 'status-stopped';
            statusElement.innerHTML = `<span class="status-indicator ${statusClass}"></span>${data.status}`;
            
            document.getElementById('tradingMode').textContent = data.mode;
            document.getElementById('tradingBroker').textContent = data.broker;
            document.getElementById('tradingStrategy').textContent = data.strategy || '-';
            
            if (data.start_time) {
                const startTime = new Date(data.start_time);
                const runtime = Math.floor((new Date() - startTime) / 1000);
                document.getElementById('tradingRuntime').textContent = formatDuration(runtime);
            } else {
                document.getElementById('tradingRuntime').textContent = '-';
            }
        }
        
        function updateBrokerStatus(data) {
            const brokerStatusElement = document.getElementById('brokerStatus');
            brokerStatusElement.innerHTML = '';
            
            for (const [broker, status] of Object.entries(data)) {
                const statusClass = status.connected ? 'status-running' : 'status-stopped';
                const statusText = status.connected ? 'Connected' : 'Disconnected';
                
                const brokerDiv = document.createElement('div');
                brokerDiv.className = 'metric';
                brokerDiv.innerHTML = `
                    <span>${broker.charAt(0).toUpperCase() + broker.slice(1)}:</span>
                    <span class="metric-value">
                        <span class="status-indicator ${statusClass}"></span>${statusText}
                    </span>
                `;
                brokerStatusElement.appendChild(brokerDiv);
            }
        }
        
        function updatePortfolioData(data) {
            if (data.funds) {
                const availableCash = data.funds.availablecash || data.funds.available_cash || 0;
                const utilizedMargin = data.funds.utilizedmargin || data.funds.utilized_margin || 0;
                const availableMargin = data.funds.availablemargin || data.funds.available_margin || 0;
                
                document.getElementById('availableCash').textContent = `₹${parseFloat(availableCash).toLocaleString('en-IN', {minimumFractionDigits: 2})}`;
                document.getElementById('totalValue').textContent = `₹${(parseFloat(availableCash) + parseFloat(availableMargin)).toLocaleString('en-IN', {minimumFractionDigits: 2})}`;
            }
            
            if (data.positions) {
                document.getElementById('openPositions').textContent = data.positions.length;
                
                // Calculate total P&L
                let totalPnL = 0;
                data.positions.forEach(pos => {
                    totalPnL += parseFloat(pos.realized_profit || pos.unrealized_profit || 0);
                });
                
                const pnlElement = document.getElementById('dailyPnL');
                pnlElement.textContent = `₹${totalPnL.toLocaleString('en-IN', {minimumFractionDigits: 2})}`;
                pnlElement.className = `metric-value ${totalPnL >= 0 ? 'positive' : 'negative'}`;
            }
        }
        
        function updateMarketData(data) {
            const marketDataElement = document.getElementById('marketData');
            const existingRow = document.querySelector(`[data-symbol="${data.symbol}"]`);
            
            const ltp = data.data.ltp || 0;
            const change = data.data.ch || 0;
            const changeClass = change >= 0 ? 'positive' : 'negative';
            
            const rowHTML = `
                <span>${data.symbol}</span>
                <span>₹${parseFloat(ltp).toFixed(2)}</span>
                <span class="${changeClass}">${change >= 0 ? '+' : ''}${parseFloat(change).toFixed(2)}</span>
            `;
            
            if (existingRow) {
                existingRow.innerHTML = rowHTML;
            } else {
                const newRow = document.createElement('div');
                newRow.className = 'symbol-row';
                newRow.setAttribute('data-symbol', data.symbol);
                newRow.innerHTML = rowHTML;
                marketDataElement.appendChild(newRow);
            }
        }
        
        function addLogEntry(message, type = 'info') {
            const logContainer = document.getElementById('activityLog');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Keep only last 50 entries
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            
            if (hours > 0) {
                return `${hours}h ${minutes}m ${secs}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            
            // Auto-refresh every 30 seconds
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
