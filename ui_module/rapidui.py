#!/usr/bin/env python3
"""
RapidUI - Web Interface for RapidTrader

This module provides a web-based user interface for RapidTrader,
similar to FreqTrade's FreqUI. It allows users to monitor trading,
view performance, and control the trading bot through a web browser.

Features:
- Real-time trading dashboard
- Portfolio monitoring
- Strategy performance analysis
- Live market data visualization
- Trading controls (start/stop/dry-run)
- Configuration management
"""

import os
import sys
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from flask import Flask, render_template, jsonify, request, send_from_directory
    from flask_cors import CORS
    from flask_socketio import SocketIO, emit
    _has_flask = True
except ImportError:
    _has_flask = False
    Flask = None
    CORS = None
    SocketIO = None

try:
    from broker.fyers_wrapper import FyersBroker
    from broker.dhan_wrapper import DhanBroker
    _has_brokers = True
except ImportError:
    _has_brokers = False
    FyersBroker = None
    DhanBroker = None

import logging

logger = logging.getLogger(__name__)


class RapidUI:
    """
    RapidUI - Web interface for RapidTrader.

    Provides a comprehensive web dashboard for monitoring and controlling
    RapidTrader trading operations.
    """

    def __init__(self, host='0.0.0.0', port=8080, debug=False):
        """
        Initialize RapidUI.

        Args:
            host: Host to bind to
            port: Port to listen on
            debug: Enable debug mode
        """
        if not _has_flask:
            raise ImportError("Flask dependencies not installed. Run: pip install flask flask-cors flask-socketio")

        self.host = host
        self.port = port
        self.debug = debug

        # Initialize Flask app
        self.app = Flask(__name__,
                        template_folder=str(project_root / 'ui_module' / 'templates'),
                        static_folder=str(project_root / 'ui_module' / 'static'))

        # Configure CORS
        CORS(self.app, origins=["http://localhost:3000", "http://127.0.0.1:3000"])

        # Initialize SocketIO
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")

        # Initialize brokers
        self.brokers = {}
        self._init_brokers()

        # Setup routes
        self._setup_routes()
        self._setup_socketio_events()

        # Trading state
        self.trading_state = {
            'status': 'stopped',
            'mode': 'dry_run',
            'broker': 'fyers',
            'strategy': None,
            'start_time': None,
            'trades_count': 0,
            'profit_loss': 0.0
        }

        logger.info("RapidUI initialized")

    def _init_brokers(self):
        """Initialize available brokers."""
        if not _has_brokers:
            logger.warning("Broker modules not available")
            return

        try:
            # Initialize Fyers broker
            self.brokers['fyers'] = FyersBroker()
            logger.info("Fyers broker initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Fyers broker: {e}")

        try:
            # Initialize DhanHQ broker
            self.brokers['dhan'] = DhanBroker()
            logger.info("DhanHQ broker initialized")
        except Exception as e:
            logger.error(f"Failed to initialize DhanHQ broker: {e}")

    def _setup_routes(self):
        """Setup Flask routes."""

        @self.app.route('/')
        def index():
            """Main dashboard page."""
            return render_template('dashboard.html')

        @self.app.route('/api/status')
        def api_status():
            """Get current trading status."""
            return jsonify({
                'status': 'success',
                'data': self.trading_state,
                'timestamp': datetime.now().isoformat()
            })

        @self.app.route('/api/brokers')
        def api_brokers():
            """Get available brokers and their status."""
            broker_status = {}

            for name, broker in self.brokers.items():
                try:
                    if name == 'fyers':
                        profile = broker.get_profile()
                        connected = profile.get('s') == 'ok' if profile else False
                        user_info = profile.get('data', {}) if connected else {}
                    elif name == 'dhan':
                        profile = broker.get_user_profile()
                        connected = bool(profile and profile.get('dhanClientId'))
                        user_info = profile if connected else {}
                    else:
                        connected = False
                        user_info = {}

                    broker_status[name] = {
                        'connected': connected,
                        'user_info': user_info,
                        'websocket_enabled': getattr(broker, 'is_websocket_connected', lambda: False)()
                    }
                except Exception as e:
                    broker_status[name] = {
                        'connected': False,
                        'error': str(e),
                        'websocket_enabled': False
                    }

            return jsonify({
                'status': 'success',
                'data': broker_status,
                'timestamp': datetime.now().isoformat()
            })

        @self.app.route('/api/portfolio/<broker_name>')
        def api_portfolio(broker_name):
            """Get portfolio information for a broker."""
            if broker_name not in self.brokers:
                return jsonify({'status': 'error', 'message': 'Broker not found'}), 404

            broker = self.brokers[broker_name]

            try:
                portfolio_data = {}

                if broker_name == 'fyers':
                    # Get funds
                    funds = broker.get_funds()
                    if funds and funds.get('s') == 'ok':
                        funds_data = funds.get('fund_limit', funds.get('data', {}))
                        if isinstance(funds_data, list) and funds_data:
                            funds_data = funds_data[0]
                        portfolio_data['funds'] = funds_data

                    # Get positions
                    positions = broker.get_positions()
                    if positions and positions.get('s') == 'ok':
                        portfolio_data['positions'] = positions.get('netPositions', [])

                    # Get holdings
                    holdings = broker.get_holdings()
                    if holdings and holdings.get('s') == 'ok':
                        portfolio_data['holdings'] = holdings.get('holdings', [])

                elif broker_name == 'dhan':
                    # Get funds
                    funds = broker.get_fund_limits()
                    if funds:
                        portfolio_data['funds'] = funds

                    # Get positions
                    positions = broker.get_positions()
                    if positions:
                        portfolio_data['positions'] = positions

                    # Get holdings
                    holdings = broker.get_holdings()
                    if holdings:
                        portfolio_data['holdings'] = holdings

                return jsonify({
                    'status': 'success',
                    'data': portfolio_data,
                    'timestamp': datetime.now().isoformat()
                })

            except Exception as e:
                return jsonify({
                    'status': 'error',
                    'message': str(e),
                    'timestamp': datetime.now().isoformat()
                }), 500

        @self.app.route('/api/trading/start', methods=['POST'])
        def api_trading_start():
            """Start trading."""
            data = request.get_json()

            broker_name = data.get('broker', 'fyers')
            mode = data.get('mode', 'dry_run')
            strategy = data.get('strategy', 'DefaultStrategy')

            if broker_name not in self.brokers:
                return jsonify({'status': 'error', 'message': 'Broker not found'}), 400

            # Update trading state
            self.trading_state.update({
                'status': 'running',
                'mode': mode,
                'broker': broker_name,
                'strategy': strategy,
                'start_time': datetime.now().isoformat(),
                'trades_count': 0,
                'profit_loss': 0.0
            })

            # Emit status update via WebSocket
            self.socketio.emit('trading_status', self.trading_state)

            return jsonify({
                'status': 'success',
                'message': f'Trading started in {mode} mode with {strategy} strategy',
                'data': self.trading_state
            })

        @self.app.route('/api/trading/stop', methods=['POST'])
        def api_trading_stop():
            """Stop trading."""
            self.trading_state.update({
                'status': 'stopped',
                'start_time': None
            })

            # Emit status update via WebSocket
            self.socketio.emit('trading_status', self.trading_state)

            return jsonify({
                'status': 'success',
                'message': 'Trading stopped',
                'data': self.trading_state
            })

        @self.app.route('/api/configs')
        def api_configs():
            """Get available configuration files."""
            configs_dir = project_root / 'userdata' / 'configs'
            configs = []

            if configs_dir.exists():
                for config_file in configs_dir.glob('*.json'):
                    try:
                        with open(config_file, 'r') as f:
                            config_data = json.load(f)

                        configs.append({
                            'name': config_file.stem,
                            'filename': config_file.name,
                            'broker': config_data.get('broker', {}).get('name', 'unknown'),
                            'dry_run': config_data.get('trading', {}).get('dry_run', True),
                            'strategies': config_data.get('strategies', [])
                        })
                    except Exception as e:
                        logger.error(f"Error reading config {config_file}: {e}")

            return jsonify({
                'status': 'success',
                'data': configs,
                'timestamp': datetime.now().isoformat()
            })

        @self.app.route('/api/strategies')
        def api_strategies():
            """Get available strategies."""
            strategies_dir = project_root / 'userdata' / 'strategies'
            strategies = []

            if strategies_dir.exists():
                for strategy_file in strategies_dir.glob('*.py'):
                    if strategy_file.name.startswith('__') or strategy_file.name == 'base_strategy.py':
                        continue

                    strategies.append({
                        'name': strategy_file.stem,
                        'filename': strategy_file.name,
                        'path': str(strategy_file)
                    })

            return jsonify({
                'status': 'success',
                'data': strategies,
                'timestamp': datetime.now().isoformat()
            })

    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time communication."""

        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection."""
            logger.info("Client connected to WebSocket")
            emit('trading_status', self.trading_state)

        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection."""
            logger.info("Client disconnected from WebSocket")

        @self.socketio.on('subscribe_market_data')
        def handle_subscribe_market_data(data):
            """Handle market data subscription."""
            broker_name = data.get('broker', 'fyers')
            symbols = data.get('symbols', [])

            if broker_name in self.brokers:
                broker = self.brokers[broker_name]

                # Enable WebSocket if not already enabled
                if hasattr(broker, 'enable_websocket') and not broker.is_websocket_connected():
                    broker.enable_websocket()

                # Subscribe to symbols
                if hasattr(broker, 'subscribe_live_quotes'):
                    broker.subscribe_live_quotes(symbols)

                    # Add callback to emit data to clients
                    def on_quote_update(symbol, quote_data):
                        self.socketio.emit('market_data', {
                            'symbol': symbol,
                            'data': quote_data,
                            'timestamp': datetime.now().isoformat()
                        })

                    broker.add_quote_callback(on_quote_update)

                emit('subscription_status', {
                    'status': 'success',
                    'message': f'Subscribed to {len(symbols)} symbols',
                    'symbols': symbols
                })
            else:
                emit('subscription_status', {
                    'status': 'error',
                    'message': 'Broker not found'
                })

    def run(self):
        """Run the web interface."""
        logger.info(f"Starting RapidUI on {self.host}:{self.port}")
        self.socketio.run(self.app, host=self.host, port=self.port, debug=self.debug, allow_unsafe_werkzeug=True)


def main():
    """Main function to run RapidUI."""
    import argparse

    parser = argparse.ArgumentParser(description='RapidUI - Web Interface for RapidTrader')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8080, help='Port to listen on')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')

    args = parser.parse_args()

    # Setup logging
    logging.basicConfig(
        level=logging.DEBUG if args.debug else logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        ui = RapidUI(host=args.host, port=args.port, debug=args.debug)
        ui.run()
    except KeyboardInterrupt:
        logger.info("RapidUI stopped by user")
    except Exception as e:
        logger.error(f"Error running RapidUI: {e}")
        raise


if __name__ == '__main__':
    main()
