{"version": "1.0", "last_updated": "2024-01-01T00:00:00", "description": "RapidTrader Money Management Configuration Template", "global_settings": {"total_capital_usage": 90.0, "emergency_reserve": 10.0, "max_risk_per_trade": 2.0, "max_total_risk": 15.0, "rebalance_frequency": "daily", "auto_rebalance": true, "stop_loss_global": 20.0, "_comments": {"total_capital_usage": "Percentage of available capital to use for trading (0-100)", "emergency_reserve": "Percentage to keep as emergency reserve (0-100)", "max_risk_per_trade": "Maximum risk per individual trade (0-100)", "max_total_risk": "Maximum total portfolio risk at any time (0-100)", "rebalance_frequency": "How often to rebalance: daily, weekly, monthly", "auto_rebalance": "Whether to automatically rebalance allocations", "stop_loss_global": "Global stop loss - halt all trading if this loss is reached (0-100)"}}, "allocation_model": {"type": "percentage", "strategies": {"example_strategy_1": 30.0, "example_strategy_2": 25.0, "example_strategy_3": 20.0, "example_strategy_4": 15.0, "example_strategy_5": 10.0}, "default_allocation": 10.0, "_comments": {"type": "Allocation model type: percentage, fixed_amount, dynamic", "strategies": "Strategy name to percentage allocation mapping", "default_allocation": "Default percentage for new strategies"}}, "risk_management": {"position_sizing": "percentage", "max_positions_per_strategy": 5, "correlation_limit": 0.7, "sector_concentration_limit": 30.0, "single_stock_limit": 10.0, "_comments": {"position_sizing": "Position sizing method: percentage, fixed, volatility_based", "max_positions_per_strategy": "Maximum number of positions per strategy", "correlation_limit": "Maximum correlation between positions (0-1)", "sector_concentration_limit": "Maximum percentage in single sector (0-100)", "single_stock_limit": "Maximum percentage in single stock (0-100)"}}, "broker_settings": {"balance_refresh_interval": 300, "margin_buffer": 5.0, "auto_fetch_balance": true, "_comments": {"balance_refresh_interval": "How often to refresh balance from broker (seconds)", "margin_buffer": "Percentage margin buffer to maintain (0-100)", "auto_fetch_balance": "Whether to automatically fetch balance from broker"}}, "advanced_settings": {"volatility_adjustment": true, "market_regime_detection": false, "correlation_monitoring": true, "drawdown_protection": true, "max_drawdown_limit": 10.0, "_comments": {"volatility_adjustment": "Adjust position sizes based on volatility", "market_regime_detection": "Detect market regimes and adjust allocations", "correlation_monitoring": "Monitor and limit position correlations", "drawdown_protection": "Enable drawdown protection mechanisms", "max_drawdown_limit": "Maximum drawdown before reducing positions (0-100)"}}, "examples": {"conservative_profile": {"total_capital_usage": 70.0, "emergency_reserve": 30.0, "max_risk_per_trade": 1.0, "max_total_risk": 8.0, "stop_loss_global": 15.0}, "moderate_profile": {"total_capital_usage": 85.0, "emergency_reserve": 15.0, "max_risk_per_trade": 2.0, "max_total_risk": 12.0, "stop_loss_global": 20.0}, "aggressive_profile": {"total_capital_usage": 95.0, "emergency_reserve": 5.0, "max_risk_per_trade": 3.0, "max_total_risk": 20.0, "stop_loss_global": 25.0}}, "strategy_templates": {"scalping_strategy": {"allocation_percentage": 15.0, "max_positions": 3, "risk_per_trade": 1.0, "holding_period": "minutes"}, "swing_strategy": {"allocation_percentage": 25.0, "max_positions": 5, "risk_per_trade": 2.0, "holding_period": "days"}, "long_term_strategy": {"allocation_percentage": 40.0, "max_positions": 8, "risk_per_trade": 3.0, "holding_period": "weeks"}}}