{"engine_settings": {"initial_capital": 100000.0, "data_interval": "1m", "refresh_interval": 60, "risk_per_trade": 0.02, "max_position_size": 0.1, "min_confidence": 0.5}, "symbols": ["RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK", "KOTAKBANK", "BHARTIARTL", "ITC", "SBIN", "MARUTI"], "strategies": {"momentum_rsi": {"name": "RSI Momentum Strategy", "description": "Buy when RSI < 30 (oversold), sell when RSI > 70 (overbought)", "enabled": true, "parameters": {"rsi_period": 14, "rsi_oversold": 30, "rsi_overbought": 70, "volume_threshold": 1.5, "confidence_multiplier": 1.0}, "indicators": [{"name": "RSI", "period": 14, "weight": 0.4}, {"name": "MACD", "fast_period": 12, "slow_period": 26, "signal_period": 9, "weight": 0.3}, {"name": "Bollinger_Bands", "period": 20, "std_dev": 2, "weight": 0.2}, {"name": "Volume_SMA", "period": 20, "weight": 0.1}], "entry_conditions": {"buy": ["RSI < 30", "MACD > MACD_Signal", "Price < BB_Lower * 1.02", "Volume > Volume_SMA * 1.5"], "sell": ["RSI > 70", "MACD < MACD_Signal", "Price > BB_Upper * 0.98"]}, "exit_conditions": {"stop_loss": 0.05, "take_profit": 0.1, "trailing_stop": 0.03}}, "mean_reversion": {"name": "Mean Reversion Strategy", "description": "Buy when price is below Bollinger Band lower, sell when above upper", "enabled": false, "parameters": {"bb_period": 20, "bb_std_dev": 2, "rsi_confirmation": true, "volume_confirmation": true}, "indicators": [{"name": "Bollinger_Bands", "period": 20, "std_dev": 2, "weight": 0.5}, {"name": "RSI", "period": 14, "weight": 0.3}, {"name": "Volume_SMA", "period": 20, "weight": 0.2}], "entry_conditions": {"buy": ["Price < BB_Lower", "RSI < 40", "Volume > Volume_SMA * 1.2"], "sell": ["Price > BB_Upper", "RSI > 60"]}, "exit_conditions": {"stop_loss": 0.03, "take_profit": 0.06, "trailing_stop": 0.02}}, "breakout": {"name": "Breakout Strategy", "description": "Buy on upward breakouts with volume confirmation", "enabled": false, "parameters": {"lookback_period": 20, "breakout_threshold": 0.02, "volume_multiplier": 2.0}, "indicators": [{"name": "SMA", "period": 20, "weight": 0.3}, {"name": "High_Low_Range", "period": 20, "weight": 0.4}, {"name": "Volume_SMA", "period": 20, "weight": 0.3}], "entry_conditions": {"buy": ["Price > High_20 * 1.02", "Volume > Volume_SMA * 2.0", "Price > SMA_20"], "sell": ["Price < Low_20 * 0.98", "Volume > Volume_SMA * 2.0", "Price < SMA_20"]}, "exit_conditions": {"stop_loss": 0.04, "take_profit": 0.08, "trailing_stop": 0.025}}}, "risk_management": {"max_portfolio_risk": 0.15, "max_single_position": 0.1, "max_correlation_exposure": 0.3, "stop_loss_global": 0.2, "daily_loss_limit": 0.05, "max_drawdown_limit": 0.25}, "logging": {"log_level": "INFO", "log_trades": true, "log_signals": true, "log_portfolio": true, "save_performance": true, "performance_file": "userdata/logs/dry_run_performance.json"}, "data_sources": {"primary": "yfinance", "backup": "none", "cache_duration": 300, "retry_attempts": 3, "timeout": 30}}