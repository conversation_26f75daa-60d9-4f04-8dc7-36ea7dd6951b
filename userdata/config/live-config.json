{"mode": "live", "dry_run": false, "timeframe": "1d", "max_open_trades": 3, "stake_currency": "INR", "stake_amount": 10000, "tradable_balance_ratio": 0.99, "fiat_display_currency": "INR", "cancel_open_orders_on_exit": true, "exchange": {"name": "dhan", "pair_whitelist": ["RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK", "KOTAKBANK", "BHARTIARTL", "ITC", "SBIN", "MARUTI"], "pair_blacklist": []}, "entry_pricing": {"price_side": "same", "use_order_book": false, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": false, "order_book_top": 1}, "bot_name": "RapidTrader-Live", "initial_state": "running", "force_entry_enable": false, "internals": {"process_only_new_candles": true, "pair_whitelist_cache_refresh_period": 1800}, "datadir": "userdata/historical_data", "strategy": "UnifiedStrategyTemplate", "strategy_path": "userdata/strategies/", "user_data_dir": "userdata", "strategy_params": {"sma_short_period": 20, "sma_long_period": 50, "rsi_period": 14, "rsi_overbought": 70, "rsi_oversold": 30, "volume_threshold": 1.5}, "broker": {"name": "dhan", "dry_run": false, "live_data": true}, "risk_management": {"max_risk_per_trade": 0.02, "max_portfolio_risk": 0.1, "stop_loss_global": 0.15}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": true, "jwt_secret_key": "your-secret-key", "CORS_origins": ["http://localhost:8080"], "username": "rapidtrader", "password": "rapidtrader"}, "logging": {"verbosity": 3, "logfile": "userdata/logs/live.log"}}