"""
Enhanced Base Strategy for RapidTrader with TA-Lib and pandas-ta Integration

This enhanced base class provides:
1. All existing optimized custom indicators
2. Full TA-Lib integration (150+ indicators)
3. pandas-ta integration (100+ additional indicators)
4. Automatic fallbacks and error handling
5. Performance optimization with caching
"""

import logging
import pandas as pd
import numpy as np
import warnings
from typing import Dict, Any, Optional, Union
from userdata.strategies.optimized_base_strategy import OptimizedBaseStrategy

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

logger = logging.getLogger(__name__)

class EnhancedBaseStrategy(OptimizedBaseStrategy):
    """
    Enhanced base strategy with comprehensive indicator library.

    Features:
    - All optimized custom indicators from OptimizedBaseStrategy
    - Full TA-Lib integration (150+ indicators)
    - pandas-ta integration (100+ additional indicators)
    - Intelligent fallbacks and caching
    - Performance monitoring
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize enhanced strategy with indicator libraries."""
        super().__init__(config)

        # Check available indicator libraries
        self.talib_available = self._check_talib()
        self.pandas_ta_available = self._check_pandas_ta()

        # Enhanced caching for external libraries
        self._talib_cache = {}
        self._pandas_ta_cache = {}

        logger.info(f"Enhanced strategy initialized:")
        logger.info(f"  TA-Lib available: {self.talib_available}")
        logger.info(f"  pandas-ta available: {self.pandas_ta_available}")

    def _check_talib(self) -> bool:
        """Check if TA-Lib is available."""
        try:
            import talib
            self.talib = talib
            return True
        except ImportError:
            logger.warning("TA-Lib not available. Install with: pip install TA-Lib")
            return False

    def _check_pandas_ta(self) -> bool:
        """Check if pandas-ta is available."""
        try:
            import pandas_ta as ta
            self.pandas_ta = ta
            return True
        except ImportError:
            logger.warning("pandas-ta not available. Install with: pip install pandas-ta")
            return False

    # =============================================================================
    # TA-LIB INTEGRATION METHODS
    # =============================================================================

    def talib_sma(self, series: pd.Series, period: int) -> pd.Series:
        """TA-Lib Simple Moving Average with caching."""
        if not self.talib_available:
            return self._sma_fast(series, period)  # Fallback to custom

        cache_key = f"talib_sma_{period}_{id(series)}"
        if cache_key in self._talib_cache:
            return self._talib_cache[cache_key]

        result = pd.Series(self.talib.SMA(series.values, timeperiod=period),
                          index=series.index, dtype='float32')

        if self.use_cache:
            self._talib_cache[cache_key] = result

        return result

    def talib_rsi(self, series: pd.Series, period: int = 14) -> pd.Series:
        """TA-Lib RSI with caching."""
        if not self.talib_available:
            return self._rsi_fast(series, period)  # Fallback to custom

        cache_key = f"talib_rsi_{period}_{id(series)}"
        if cache_key in self._talib_cache:
            return self._talib_cache[cache_key]

        result = pd.Series(self.talib.RSI(series.values, timeperiod=period),
                          index=series.index, dtype='float32')

        if self.use_cache:
            self._talib_cache[cache_key] = result

        return result

    def talib_macd(self, series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """TA-Lib MACD with caching."""
        if not self.talib_available:
            return self._macd_fast(series, fast, slow, signal)  # Fallback to custom

        cache_key = f"talib_macd_{fast}_{slow}_{signal}_{id(series)}"
        if cache_key in self._talib_cache:
            return self._talib_cache[cache_key]

        macd, signal_line, histogram = self.talib.MACD(series.values,
                                                      fastperiod=fast,
                                                      slowperiod=slow,
                                                      signalperiod=signal)

        result = {
            'macd': pd.Series(macd, index=series.index, dtype='float32'),
            'signal': pd.Series(signal_line, index=series.index, dtype='float32'),
            'histogram': pd.Series(histogram, index=series.index, dtype='float32')
        }

        if self.use_cache:
            self._talib_cache[cache_key] = result

        return result

    def talib_bbands(self, series: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """TA-Lib Bollinger Bands with caching."""
        if not self.talib_available:
            return self._bollinger_bands_fast(series, period, std_dev)  # Fallback to custom

        cache_key = f"talib_bbands_{period}_{std_dev}_{id(series)}"
        if cache_key in self._talib_cache:
            return self._talib_cache[cache_key]

        upper, middle, lower = self.talib.BBANDS(series.values,
                                               timeperiod=period,
                                               nbdevup=std_dev,
                                               nbdevdn=std_dev)

        result = {
            'upper': pd.Series(upper, index=series.index, dtype='float32'),
            'middle': pd.Series(middle, index=series.index, dtype='float32'),
            'lower': pd.Series(lower, index=series.index, dtype='float32')
        }

        if self.use_cache:
            self._talib_cache[cache_key] = result

        return result

    # Advanced TA-Lib indicators
    def talib_adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """TA-Lib Average Directional Index."""
        if not self.talib_available:
            logger.warning("ADX requires TA-Lib. Returning NaN series.")
            return pd.Series(np.nan, index=close.index)

        result = self.talib.ADX(high.values, low.values, close.values, timeperiod=period)
        return pd.Series(result, index=close.index, dtype='float32')

    def talib_stoch(self, high: pd.Series, low: pd.Series, close: pd.Series,
                   k_period: int = 14, d_period: int = 3) -> Dict[str, pd.Series]:
        """TA-Lib Stochastic Oscillator."""
        if not self.talib_available:
            logger.warning("Stochastic requires TA-Lib. Returning NaN series.")
            return {
                'slowk': pd.Series(np.nan, index=close.index),
                'slowd': pd.Series(np.nan, index=close.index)
            }

        slowk, slowd = self.talib.STOCH(high.values, low.values, close.values,
                                       fastk_period=k_period, slowk_period=d_period)

        return {
            'slowk': pd.Series(slowk, index=close.index, dtype='float32'),
            'slowd': pd.Series(slowd, index=close.index, dtype='float32')
        }

    # =============================================================================
    # PANDAS-TA INTEGRATION METHODS
    # =============================================================================

    def pandas_ta_indicator(self, dataframe: pd.DataFrame, indicator_name: str, **kwargs) -> Union[pd.Series, pd.DataFrame]:
        """Generic pandas-ta indicator caller with caching."""
        if not self.pandas_ta_available:
            logger.warning(f"pandas-ta not available for {indicator_name}")
            return pd.Series(np.nan, index=dataframe.index)

        cache_key = f"pandas_ta_{indicator_name}_{str(kwargs)}_{id(dataframe)}"
        if cache_key in self._pandas_ta_cache:
            return self._pandas_ta_cache[cache_key]

        try:
            # Get the indicator function from pandas-ta
            indicator_func = getattr(self.pandas_ta, indicator_name.lower())
            result = indicator_func(dataframe, **kwargs)

            if self.use_cache:
                self._pandas_ta_cache[cache_key] = result

            return result
        except AttributeError:
            logger.error(f"Indicator {indicator_name} not found in pandas-ta")
            return pd.Series(np.nan, index=dataframe.index)

    def pandas_ta_vwap(self, dataframe: pd.DataFrame) -> pd.Series:
        """pandas-ta Volume Weighted Average Price."""
        return self.pandas_ta_indicator(dataframe, 'vwap')

    def pandas_ta_supertrend(self, dataframe: pd.DataFrame, period: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
        """pandas-ta SuperTrend indicator."""
        return self.pandas_ta_indicator(dataframe, 'supertrend', length=period, multiplier=multiplier)

    def pandas_ta_ichimoku(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """pandas-ta Ichimoku Cloud."""
        return self.pandas_ta_indicator(dataframe, 'ichimoku')

    # =============================================================================
    # CONVENIENCE METHODS FOR EASY ACCESS
    # =============================================================================

    def get_all_available_indicators(self) -> Dict[str, list]:
        """Get list of all available indicators."""
        indicators = {
            'custom_optimized': [
                '_sma_fast', '_ema_fast', '_rsi_fast', '_macd_fast',
                '_bollinger_bands_fast', '_crossover_fast', '_crossunder_fast'
            ],
            'talib': [],
            'pandas_ta': []
        }

        if self.talib_available:
            indicators['talib'] = [func for func in dir(self.talib)
                                 if not func.startswith('_') and callable(getattr(self.talib, func))]

        if self.pandas_ta_available:
            indicators['pandas_ta'] = [func for func in dir(self.pandas_ta)
                                     if not func.startswith('_') and callable(getattr(self.pandas_ta, func))]

        return indicators

    def clear_all_caches(self):
        """Clear all indicator caches."""
        super().clear_cache()
        self._talib_cache.clear()
        self._pandas_ta_cache.clear()
        logger.info("All indicator caches cleared")

    # =============================================================================
    # SMART INDICATOR SELECTION
    # =============================================================================

    def smart_sma(self, series: pd.Series, period: int, prefer_talib: bool = False) -> pd.Series:
        """Smart SMA selection - custom (fast) or TA-Lib based on preference."""
        if prefer_talib and self.talib_available:
            return self.talib_sma(series, period)
        else:
            return self._sma_fast(series, period)  # Default to optimized custom

    def smart_rsi(self, series: pd.Series, period: int = 14, prefer_talib: bool = False) -> pd.Series:
        """Smart RSI selection - custom (fast) or TA-Lib based on preference."""
        if prefer_talib and self.talib_available:
            return self.talib_rsi(series, period)
        else:
            return self._rsi_fast(series, period)  # Default to optimized custom

    def smart_macd(self, series: pd.Series, fast: int = 12, slow: int = 26,
                  signal: int = 9, prefer_talib: bool = False) -> Dict[str, pd.Series]:
        """Smart MACD selection - custom (fast) or TA-Lib based on preference."""
        if prefer_talib and self.talib_available:
            return self.talib_macd(series, fast, slow, signal)
        else:
            return self._macd_fast(series, fast, slow, signal)  # Default to optimized custom
