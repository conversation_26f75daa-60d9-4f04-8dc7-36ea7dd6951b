"""
DefaultStrategy for RapidTrader

A simple RSI-based trading strategy that demonstrates basic buy/sell signals.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional

# Import the base strategy
from userdata.strategies.base_strategy import BaseStrategy

class DefaultStrategy(BaseStrategy):
    """
    Default RSI-based trading strategy.
    
    Buy when RSI < 30 (oversold)
    Sell when RSI > 70 (overbought)
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the strategy."""
        super().__init__(config)
        self.name = "DefaultStrategy"

        # Get parameters from config or use defaults
        self.buy_rsi_threshold = self.strategy_params.get("buy_rsi_threshold", 30)
        self.sell_rsi_threshold = self.strategy_params.get("sell_rsi_threshold", 70)
        self.rsi_period = self.strategy_params.get("rsi_period", 14)

    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Add RSI indicator to the dataframe.

        Args:
            dataframe: Price data

        Returns:
            Dataframe with RSI indicator
        """
        # Calculate RSI
        dataframe['rsi'] = self.calculate_rsi(dataframe['close'], self.rsi_period)
        
        return dataframe

    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate buy signals based on RSI.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with buy signals
        """
        # Buy when RSI is oversold (< threshold)
        dataframe['buy'] = np.where(
            (dataframe['rsi'] < self.buy_rsi_threshold) &
            (dataframe['rsi'].shift(1) >= self.buy_rsi_threshold),  # RSI just crossed below threshold
            1, 0
        )
        
        return dataframe

    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate sell signals based on RSI.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with sell signals
        """
        # Sell when RSI is overbought (> threshold)
        dataframe['sell'] = np.where(
            (dataframe['rsi'] > self.sell_rsi_threshold) &
            (dataframe['rsi'].shift(1) <= self.sell_rsi_threshold),  # RSI just crossed above threshold
            1, 0
        )
        
        return dataframe

    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """
        Calculate Relative Strength Index (RSI).

        Args:
            prices: Price series (typically close prices)
            period: RSI calculation period

        Returns:
            RSI values
        """
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
