"""
Unified Strategy Template for RapidTrader

This template uses a focused set of indicators from both sources:
- Custom optimized indicators (fastest performance)
- TA-Lib indicators (proven reliability)

Works across ALL modules: backtest, dry-run, live trading, optimization
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
from userdata.strategies.optimized_base_strategy import OptimizedBaseStrategy

logger = logging.getLogger(__name__)

class UnifiedStrategyTemplate(OptimizedBaseStrategy):
    """
    Unified strategy template using custom + TA-Lib indicators.
    
    Indicators used:
    - Custom: SMA, EMA, RSI (for speed)
    - TA-Lib: ADX, Stochastic, CCI (for advanced analysis)
    
    Compatible with: backtest, dry-run, live trading, optimization
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the unified strategy."""
        super().__init__(config)
        self.name = "UnifiedStrategyTemplate"
        
        # Strategy parameters (configurable via JSON)
        self.sma_short_period = self.strategy_params.get("sma_short_period", 10)
        self.sma_long_period = self.strategy_params.get("sma_long_period", 20)
        self.ema_period = self.strategy_params.get("ema_period", 12)
        self.rsi_period = self.strategy_params.get("rsi_period", 14)
        self.rsi_buy_threshold = self.strategy_params.get("rsi_buy_threshold", 30)
        self.rsi_sell_threshold = self.strategy_params.get("rsi_sell_threshold", 70)
        self.adx_period = self.strategy_params.get("adx_period", 14)
        self.adx_threshold = self.strategy_params.get("adx_threshold", 25)
        self.stoch_k_period = self.strategy_params.get("stoch_k_period", 14)
        self.stoch_d_period = self.strategy_params.get("stoch_d_period", 3)
        self.volume_threshold = self.strategy_params.get("volume_threshold", 0)
        
        # Check TA-Lib availability
        self.talib_available = self._check_talib()
        
        logger.info(f"UnifiedStrategyTemplate initialized:")
        logger.info(f"  SMA periods: {self.sma_short_period}/{self.sma_long_period}")
        logger.info(f"  RSI period: {self.rsi_period}")
        logger.info(f"  TA-Lib available: {self.talib_available}")
    
    def _check_talib(self) -> bool:
        """Check if TA-Lib is available."""
        try:
            import talib
            self.talib = talib
            return True
        except ImportError:
            logger.warning("TA-Lib not available. Using custom indicators only.")
            return False
    
    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Add indicators using both custom and TA-Lib.
        
        Custom indicators (speed optimized):
        - SMA short/long for trend
        - EMA for responsive signals
        - RSI for momentum
        
        TA-Lib indicators (advanced analysis):
        - ADX for trend strength
        - Stochastic for overbought/oversold
        - CCI for momentum confirmation
        """
        
        # =================================================================
        # CUSTOM OPTIMIZED INDICATORS (always available, fastest)
        # =================================================================
        
        # Moving averages for trend detection
        dataframe['sma_short'] = self._sma_fast(dataframe['close'], self.sma_short_period)
        dataframe['sma_long'] = self._sma_fast(dataframe['close'], self.sma_long_period)
        dataframe['ema'] = self._ema_fast(dataframe['close'], self.ema_period)
        
        # RSI for momentum
        dataframe['rsi'] = self._rsi_fast(dataframe['close'], self.rsi_period)
        
        # Trend signals using custom crossover detection
        dataframe['golden_cross'] = self._crossover_fast(dataframe['sma_short'], dataframe['sma_long'])
        dataframe['death_cross'] = self._crossunder_fast(dataframe['sma_short'], dataframe['sma_long'])
        
        # Price position relative to moving averages
        dataframe['price_above_sma_short'] = (dataframe['close'] > dataframe['sma_short']).astype('int8')
        dataframe['price_above_sma_long'] = (dataframe['close'] > dataframe['sma_long']).astype('int8')
        dataframe['price_above_ema'] = (dataframe['close'] > dataframe['ema']).astype('int8')
        
        # =================================================================
        # TA-LIB INDICATORS (advanced analysis, if available)
        # =================================================================
        
        if self.talib_available:
            # ADX for trend strength
            dataframe['adx'] = pd.Series(
                self.talib.ADX(dataframe['high'].values, 
                              dataframe['low'].values, 
                              dataframe['close'].values, 
                              timeperiod=self.adx_period),
                index=dataframe.index, dtype='float32'
            )
            
            # Stochastic Oscillator for overbought/oversold
            slowk, slowd = self.talib.STOCH(
                dataframe['high'].values,
                dataframe['low'].values, 
                dataframe['close'].values,
                fastk_period=self.stoch_k_period,
                slowk_period=self.stoch_d_period
            )
            dataframe['stoch_k'] = pd.Series(slowk, index=dataframe.index, dtype='float32')
            dataframe['stoch_d'] = pd.Series(slowd, index=dataframe.index, dtype='float32')
            
            # CCI for momentum confirmation
            dataframe['cci'] = pd.Series(
                self.talib.CCI(dataframe['high'].values,
                              dataframe['low'].values,
                              dataframe['close'].values,
                              timeperiod=14),
                index=dataframe.index, dtype='float32'
            )
        else:
            # Fallback values if TA-Lib not available
            dataframe['adx'] = pd.Series(50.0, index=dataframe.index, dtype='float32')  # Assume strong trend
            dataframe['stoch_k'] = pd.Series(50.0, index=dataframe.index, dtype='float32')
            dataframe['stoch_d'] = pd.Series(50.0, index=dataframe.index, dtype='float32')
            dataframe['cci'] = pd.Series(0.0, index=dataframe.index, dtype='float32')
        
        return dataframe
    
    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate buy signals using both custom and TA-Lib indicators.
        
        Buy conditions (any can trigger):
        1. Golden cross + strong trend (ADX)
        2. RSI oversold + price above EMA
        3. Stochastic oversold + uptrend
        """
        
        # Condition 1: Golden cross with strong trend
        condition1 = (
            (dataframe['golden_cross'] == 1) &
            (dataframe['adx'] > self.adx_threshold)
        )
        
        # Condition 2: RSI oversold with price above EMA
        condition2 = (
            (dataframe['rsi'] < self.rsi_buy_threshold) &
            (dataframe['price_above_ema'] == 1) &
            (dataframe['price_above_sma_long'] == 1)
        )
        
        # Condition 3: Stochastic oversold in uptrend
        condition3 = (
            (dataframe['stoch_k'] < 20) &
            (dataframe['price_above_sma_short'] == 1) &
            (dataframe['adx'] > self.adx_threshold)
        )
        
        # Condition 4: CCI momentum with trend confirmation
        condition4 = (
            (dataframe['cci'] < -100) &  # Oversold
            (dataframe['price_above_sma_long'] == 1) &
            (dataframe['rsi'] > 25)  # Not extremely oversold
        )
        
        # Volume filter
        volume_ok = dataframe['volume'] > self.volume_threshold
        
        # Combine all conditions
        buy_signal = (
            (condition1 | condition2 | condition3 | condition4) &
            volume_ok &
            (~dataframe['rsi'].isna()) &
            (~dataframe['sma_short'].isna())
        )
        
        dataframe['buy_signal'] = buy_signal.astype('int8')
        return dataframe
    
    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate sell signals using both custom and TA-Lib indicators.
        
        Sell conditions (any can trigger):
        1. Death cross
        2. RSI overbought
        3. Stochastic overbought
        4. Price below EMA in weak trend
        """
        
        # Condition 1: Death cross
        condition1 = dataframe['death_cross'] == 1
        
        # Condition 2: RSI overbought
        condition2 = dataframe['rsi'] > self.rsi_sell_threshold
        
        # Condition 3: Stochastic overbought
        condition3 = (
            (dataframe['stoch_k'] > 80) &
            (dataframe['stoch_d'] > 80)
        )
        
        # Condition 4: Price below EMA with weak trend
        condition4 = (
            (dataframe['price_above_ema'] == 0) &
            (dataframe['adx'] < self.adx_threshold)
        )
        
        # Condition 5: CCI extreme overbought
        condition5 = dataframe['cci'] > 100
        
        # Volume filter
        volume_ok = dataframe['volume'] > self.volume_threshold
        
        # Combine all conditions
        sell_signal = (
            (condition1 | condition2 | condition3 | condition4 | condition5) &
            volume_ok &
            (~dataframe['rsi'].isna()) &
            (~dataframe['sma_short'].isna())
        )
        
        dataframe['sell_signal'] = sell_signal.astype('int8')
        return dataframe
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information for all modules."""
        return {
            "name": self.name,
            "version": "1.0",
            "indicators": {
                "custom": ["SMA", "EMA", "RSI", "Crossovers"],
                "talib": ["ADX", "Stochastic", "CCI"] if self.talib_available else []
            },
            "parameters": {
                "sma_short_period": self.sma_short_period,
                "sma_long_period": self.sma_long_period,
                "ema_period": self.ema_period,
                "rsi_period": self.rsi_period,
                "rsi_buy_threshold": self.rsi_buy_threshold,
                "rsi_sell_threshold": self.rsi_sell_threshold,
                "adx_period": self.adx_period,
                "adx_threshold": self.adx_threshold
            },
            "compatible_modules": ["backtest", "dry_run", "live_trading", "optimization"]
        }
