"""
Optimized Base Strategy for RapidTrader

This module provides an optimized base class for trading strategies with focus on:
- Memory efficiency
- Vectorized operations
- Minimal data copying
- Fast indicator calculations
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import warnings

# Suppress pandas warnings for performance
warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)

logger = logging.getLogger(__name__)

class OptimizedBaseStrategy:
    """
    Optimized base class for trading strategies.

    Key optimizations:
    - In-place operations to avoid data copying
    - Vectorized calculations using numpy
    - Memory-efficient data types
    - Cached indicator calculations
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the optimized strategy.

        Args:
            config: Strategy configuration
        """
        # Ensure config is a dictionary
        if isinstance(config, dict):
            self.config = config
        else:
            self.config = {}

        self.name = "optimized_base_strategy"

        # Extract configuration
        self.user_profile = self.config.get("user_profile", {})
        self.broker_info = self.config.get("broker_info", {})

        # Strategy parameters
        strategy_config = self.config.get("strategy", {})
        if isinstance(strategy_config, dict):
            self.strategy_params = strategy_config.get("params", {})
        else:
            self.strategy_params = {}

        # Performance settings
        self.use_cache = True
        self._indicator_cache = {}

        logger.info(f"OptimizedBaseStrategy initialized: {self.name}")

    def analyze(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Analyze the dataframe and generate trading signals.

        This method performs all analysis in-place for maximum performance.

        Args:
            dataframe: Price data

        Returns:
            Dataframe with indicators and signals (same object, modified in-place)
        """
        # Convert to optimal data types for performance
        self._optimize_dtypes(dataframe)

        # Add indicators (in-place)
        self.populate_indicators(dataframe)

        # Generate buy signals (in-place)
        self.populate_buy_signals(dataframe)

        # Generate sell signals (in-place)
        self.populate_sell_signals(dataframe)

        # Initialize signal columns if they don't exist
        if 'buy_signal' not in dataframe.columns:
            dataframe['buy_signal'] = 0
        if 'sell_signal' not in dataframe.columns:
            dataframe['sell_signal'] = 0

        return dataframe

    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Add technical indicators to the dataframe.

        Override this method in your strategy.
        All operations should be done in-place for performance.

        Args:
            dataframe: Price data

        Returns:
            Dataframe with indicators (same object)
        """
        # Example: Simple moving average
        dataframe['sma_20'] = self._sma_fast(dataframe['close'], 20)
        return dataframe

    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate buy signals.

        Override this method in your strategy.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with buy signals (same object)
        """
        # Default: no signals
        dataframe['buy_signal'] = 0
        return dataframe

    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate sell signals.

        Override this method in your strategy.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with sell signals (same object)
        """
        # Default: no signals
        dataframe['sell_signal'] = 0
        return dataframe

    def _optimize_dtypes(self, dataframe: pd.DataFrame):
        """
        Optimize data types for memory efficiency and speed.

        Args:
            dataframe: DataFrame to optimize
        """
        # Convert price columns to float32 for memory efficiency
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in dataframe.columns:
                dataframe[col] = dataframe[col].astype('float32')

        # Convert volume to int32
        if 'volume' in dataframe.columns:
            dataframe['volume'] = dataframe['volume'].astype('int32')

    def _sma_fast(self, series: pd.Series, period: int) -> pd.Series:
        """
        Fast Simple Moving Average calculation using numpy.

        Args:
            series: Price series
            period: Moving average period

        Returns:
            SMA values
        """
        cache_key = f"sma_{period}_{id(series)}"
        if self.use_cache and cache_key in self._indicator_cache:
            return self._indicator_cache[cache_key]

        # Use numpy for faster calculation
        values = series.values
        result = np.full(len(values), np.nan, dtype=np.float32)

        if len(values) >= period:
            # Calculate using numpy convolution for speed
            kernel = np.ones(period) / period
            convolved = np.convolve(values, kernel, mode='valid')
            result[period-1:] = convolved

        result_series = pd.Series(result, index=series.index, dtype='float32')

        if self.use_cache:
            self._indicator_cache[cache_key] = result_series

        return result_series

    def _ema_fast(self, series: pd.Series, period: int) -> pd.Series:
        """
        Fast Exponential Moving Average calculation.

        Args:
            series: Price series
            period: EMA period

        Returns:
            EMA values
        """
        cache_key = f"ema_{period}_{id(series)}"
        if self.use_cache and cache_key in self._indicator_cache:
            return self._indicator_cache[cache_key]

        # Calculate EMA using pandas ewm (optimized)
        result = series.ewm(span=period, adjust=False).mean().astype('float32')

        if self.use_cache:
            self._indicator_cache[cache_key] = result

        return result

    def _rsi_fast(self, series: pd.Series, period: int = 14) -> pd.Series:
        """
        Fast RSI calculation using vectorized operations.

        Args:
            series: Price series
            period: RSI period

        Returns:
            RSI values
        """
        cache_key = f"rsi_{period}_{id(series)}"
        if self.use_cache and cache_key in self._indicator_cache:
            return self._indicator_cache[cache_key]

        # Vectorized RSI calculation
        delta = series.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        # Use exponential moving average for speed
        avg_gain = gain.ewm(span=period, adjust=False).mean()
        avg_loss = loss.ewm(span=period, adjust=False).mean()

        rs = avg_gain / avg_loss
        rsi = (100 - (100 / (1 + rs))).astype('float32')

        if self.use_cache:
            self._indicator_cache[cache_key] = rsi

        return rsi

    def _macd_fast(self, series: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """
        Fast MACD calculation.

        Args:
            series: Price series
            fast: Fast EMA period
            slow: Slow EMA period
            signal: Signal line EMA period

        Returns:
            Dictionary with MACD, signal, and histogram
        """
        cache_key = f"macd_{fast}_{slow}_{signal}_{id(series)}"
        if self.use_cache and cache_key in self._indicator_cache:
            return self._indicator_cache[cache_key]

        # Calculate MACD components
        ema_fast = self._ema_fast(series, fast)
        ema_slow = self._ema_fast(series, slow)

        macd_line = (ema_fast - ema_slow).astype('float32')
        signal_line = self._ema_fast(macd_line, signal)
        histogram = (macd_line - signal_line).astype('float32')

        result = {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }

        if self.use_cache:
            self._indicator_cache[cache_key] = result

        return result

    def _bollinger_bands_fast(self, series: pd.Series, period: int = 20, std_dev: float = 2) -> Dict[str, pd.Series]:
        """
        Fast Bollinger Bands calculation.

        Args:
            series: Price series
            period: Moving average period
            std_dev: Standard deviation multiplier

        Returns:
            Dictionary with upper, middle, and lower bands
        """
        cache_key = f"bb_{period}_{std_dev}_{id(series)}"
        if self.use_cache and cache_key in self._indicator_cache:
            return self._indicator_cache[cache_key]

        # Calculate Bollinger Bands
        middle = self._sma_fast(series, period)
        std = series.rolling(window=period).std().astype('float32')

        upper = (middle + (std * std_dev)).astype('float32')
        lower = (middle - (std * std_dev)).astype('float32')

        result = {
            'upper': upper,
            'middle': middle,
            'lower': lower
        }

        if self.use_cache:
            self._indicator_cache[cache_key] = result

        return result

    def _crossover_fast(self, series1: pd.Series, series2: pd.Series) -> pd.Series:
        """
        Fast crossover detection using vectorized operations.

        Args:
            series1: First series
            series2: Second series

        Returns:
            Boolean series indicating crossovers
        """
        # Vectorized crossover detection
        above = series1 > series2
        above_prev = above.shift(1).fillna(False).infer_objects(copy=False)

        return (above & ~above_prev).astype('int8')

    def _crossunder_fast(self, series1: pd.Series, series2: pd.Series) -> pd.Series:
        """
        Fast crossunder detection using vectorized operations.

        Args:
            series1: First series
            series2: Second series

        Returns:
            Boolean series indicating crossunders
        """
        # Vectorized crossunder detection
        below = series1 < series2
        below_prev = below.shift(1).fillna(False).infer_objects(copy=False)

        return (below & ~below_prev).astype('int8')

    def clear_cache(self):
        """Clear indicator cache to free memory."""
        self._indicator_cache.clear()
        logger.debug(f"Cleared indicator cache for {self.name}")
