"""
Base Strategy for RapidTrader

This module defines the BaseStrategy class that all trading strategies should inherit from.
Similar to FreqTrade's approach, this provides a unified interface for all strategies.
"""

import abc
import logging
from typing import Dict, Any, Optional
import pandas as pd

# Configure logging
logger = logging.getLogger("base_strategy")

class BaseStrategy(abc.ABC):
    """
    Base class for all trading strategies.

    All trading strategies should inherit from this class and implement the required methods.
    This follows FreqTrade's pattern for strategy development.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the strategy.

        Args:
            config: Strategy configuration including user profile data
        """
        # Ensure config is a dictionary
        if isinstance(config, dict):
            self.config = config
        else:
            # If config is not a dict (e.g., string or None), create empty dict
            self.config = {}

        self.name = "base_strategy"

        # Extract user profile information if available
        self.user_profile = self.config.get("user_profile", {})
        self.broker_info = self.config.get("broker_info", {})

        # Strategy parameters
        strategy_config = self.config.get("strategy", {})
        if isinstance(strategy_config, dict):
            self.strategy_params = strategy_config.get("params", {})
        else:
            # If strategy is just a string name, use empty params
            self.strategy_params = {}

        # Trading parameters
        self.trading_params = self.config.get("trading", {})
        self.max_open_trades = self.trading_params.get("max_open_trades", 3)
        self.stake_amount = self.trading_params.get("stake_amount", 1000)
        self.stake_currency = self.trading_params.get("stake_currency", "INR")

        # Risk management parameters
        self.risk_params = self.config.get("risk_management", {})
        self.max_risk_per_trade = self.risk_params.get("max_risk_per_trade", 0.02)  # 2%
        self.max_portfolio_risk = self.risk_params.get("max_portfolio_risk", 0.10)  # 10%

        logger.info(f"Strategy {self.name} initialized with user profile: {self.user_profile.get('dhanClientId', 'Unknown')}")

    def get_user_balance(self) -> float:
        """
        Get user's available balance for trading.

        Returns:
            Available balance amount
        """
        return self.broker_info.get("available_balance", 0.0)

    def get_user_risk_capacity(self) -> float:
        """
        Calculate user's risk capacity based on profile and balance.

        Returns:
            Maximum amount user can risk per trade
        """
        balance = self.get_user_balance()
        return balance * self.max_risk_per_trade

    def calculate_position_size(self, price: float, stop_loss: Optional[float] = None) -> int:
        """
        Calculate optimal position size based on user's risk capacity.

        Args:
            price: Entry price
            stop_loss: Stop loss price (optional)

        Returns:
            Number of shares to buy
        """
        risk_amount = self.get_user_risk_capacity()

        if stop_loss and stop_loss > 0:
            # Calculate position size based on stop loss
            risk_per_share = abs(price - stop_loss)
            if risk_per_share > 0:
                position_size = int(risk_amount / risk_per_share)
            else:
                position_size = int(self.stake_amount / price)
        else:
            # Use fixed stake amount
            position_size = int(self.stake_amount / price)

        return max(1, position_size)  # Minimum 1 share

    def is_user_eligible_for_segment(self, segment: str) -> bool:
        """
        Check if user is eligible for trading in specific segment.

        Args:
            segment: Trading segment (Equity, Derivative, Currency, Commodity)

        Returns:
            True if user can trade in this segment
        """
        active_segments = self.user_profile.get("activeSegment", "")
        return segment in active_segments

    def get_user_trading_limits(self) -> Dict[str, Any]:
        """
        Get user's trading limits and restrictions.

        Returns:
            Dictionary with trading limits
        """
        return {
            "max_open_trades": self.max_open_trades,
            "stake_amount": self.stake_amount,
            "max_risk_per_trade": self.max_risk_per_trade,
            "max_portfolio_risk": self.max_portfolio_risk,
            "available_balance": self.get_user_balance(),
            "risk_capacity": self.get_user_risk_capacity()
        }

    @abc.abstractmethod
    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Add indicators to the dataframe.

        Args:
            dataframe: Price data

        Returns:
            Dataframe with indicators
        """
        pass

    @abc.abstractmethod
    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate buy signals.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with buy signals
        """
        pass

    @abc.abstractmethod
    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Generate sell signals.

        Args:
            dataframe: Price data with indicators

        Returns:
            Dataframe with sell signals
        """
        pass

    def analyze(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """
        Analyze the dataframe and generate trading signals.

        Args:
            dataframe: Price data

        Returns:
            Dataframe with trading signals
        """
        # Add indicators
        dataframe = self.populate_indicators(dataframe)

        # Generate buy signals
        dataframe = self.populate_buy_signals(dataframe)

        # Generate sell signals
        dataframe = self.populate_sell_signals(dataframe)

        return dataframe

    def get_strategy_name(self) -> str:
        """
        Get the strategy name.

        Returns:
            Strategy name
        """
        return self.name