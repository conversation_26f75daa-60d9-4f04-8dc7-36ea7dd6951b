{"trading": {"max_open_trades": 3, "stake_currency": "INR", "stake_amount": 1000, "dry_run": false, "cancel_open_orders_on_exit": true}, "exchange": {"name": "fyers", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:INFY-EQ", "NSE:HDFCBANK-EQ"], "pair_blacklist": []}, "broker": {"name": "fyers", "dry_run": false, "live_data": true, "websocket_enabled": true, "rate_limiting": {"enabled": true, "strict_mode": true}}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": true, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "pairlists": [{"method": "StaticPairList", "config": {"pairs": ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:INFY-EQ", "NSE:HDFCBANK-EQ"]}}], "protections": [{"method": "<PERSON><PERSON><PERSON><PERSON>", "lookback_period_candles": 60, "trade_limit": 2, "stop_duration_candles": 120, "only_per_pair": false}, {"method": "MaxDrawdown", "lookback_period_candles": 200, "trade_limit": 10, "stop_duration_candles": 30, "max_allowed_drawdown": 0.15}, {"method": "LowProfitPairs", "lookback_period_candles": 60, "trade_limit": 1, "stop_duration_candles": 120, "required_profit": 0.03}], "timeframe": "1d", "timerange": "", "dataformat_ohlcv": "json", "startup_candle_count": 30, "unfilledtimeout": {"buy": 5, "sell": 5, "exit_timeout_count": 0, "unit": "minutes"}, "bid_strategy": {"price_side": "bid", "ask_last_balance": 0.0, "use_order_book": true, "order_book_top": 1, "check_depth_of_market": {"enabled": true, "bids_to_ask_delta": 1}}, "ask_strategy": {"price_side": "ask", "use_order_book": true, "order_book_top": 1}, "order_types": {"buy": "limit", "sell": "limit", "emergencysell": "market", "forcebuy": "market", "forcesell": "market", "stoploss": "market", "stoploss_on_exchange": true, "stoploss_on_exchange_interval": 60, "stoploss_on_exchange_limit_ratio": 0.99}, "order_time_in_force": {"buy": "gtc", "sell": "gtc"}, "strategies": [{"name": "DefaultStrategy", "enabled": true, "params": {"buy_rsi_threshold": 30, "sell_rsi_threshold": 70, "minimal_roi": {"0": 0.08, "40": 0.04, "100": 0.02, "180": 0}, "stoploss": -0.08}}], "minimal_roi": {"0": 0.08, "40": 0.04, "100": 0.02, "180": 0}, "stoploss": -0.08, "trailing_stop": true, "trailing_stop_positive": 0.02, "trailing_stop_positive_offset": 0.03, "trailing_only_offset_is_reached": true, "use_sell_signal": true, "sell_profit_only": false, "sell_profit_offset": 0.0, "ignore_roi_if_buy_signal": false, "ignore_buying_expired_candle_after": 300, "position_adjustment_enable": false, "max_entry_position_adjustment": -1, "money_management": {"enabled": true, "allocation_method": "risk_parity", "risk_per_trade": 0.015, "max_portfolio_risk": 0.08, "position_sizing": {"method": "risk_based", "risk_amount": 500}, "risk_management": {"max_drawdown": 0.15, "stop_loss": 0.08, "take_profit": 0.12, "daily_loss_limit": 0.05}}, "telegram": {"enabled": false, "token": "", "chat_id": "", "notification_settings": {"status": "on", "warning": "on", "startup": "on", "buy": "on", "sell": "on", "buy_cancel": "on", "sell_cancel": "on", "buy_fill": "on", "sell_fill": "on", "protection_trigger": "on", "protection_trigger_global": "on"}}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "your-secret-key-change-this", "CORS_origins": ["http://localhost:3000"], "username": "rapidtrader", "password": "change-this-password"}, "db_url": "sqlite:///userdata/tradesv3.sqlite", "initial_state": "running", "forcebuy_enable": false, "internals": {"process_throttle_secs": 5}, "logging": {"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}, "handlers": {"default": {"level": "INFO", "formatter": "default", "class": "logging.StreamHandler", "stream": "ext://sys.stdout"}, "file": {"level": "DEBUG", "formatter": "default", "class": "logging.FileHandler", "filename": "userdata/logs/rapidtrader.log"}}, "loggers": {"rapidtrader": {"handlers": ["default", "file"], "level": "INFO"}, "fyers": {"handlers": ["default", "file"], "level": "INFO"}}}}