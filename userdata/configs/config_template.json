{"max_open_trades": 3, "stake_currency": "INR", "stake_amount": 1000, "tradable_balance_ratio": 0.99, "fiat_display_currency": "INR", "dry_run": true, "dry_run_wallet": 100000, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "isolated", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "dhan", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": [], "pair_blacklist": []}, "broker": {"name": "dhan", "enabled": true, "dry_run": true, "config": {"client_id": "", "access_token": ""}}, "strategies": [{"name": "ta_sma_crossover", "enabled": true, "params": {"short_window": 20, "long_window": 50}}], "pairlists": [{"method": "StaticPairList", "config": {"pairs": ["SBIN-EQ", "RELIANCE-EQ", "INFY-EQ"]}}], "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": false, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "", "CORS_origins": [], "username": "", "password": ""}, "bot_name": "rapidtrader", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 5}}