{"trading": {"max_open_trades": 3, "stake_currency": "INR", "stake_amount": 1000, "dry_run": true, "dry_run_wallet": 100000, "cancel_open_orders_on_exit": true}, "exchange": {"name": "fyers", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:INFY-EQ", "NSE:HDFCBANK-EQ"], "pair_blacklist": []}, "broker": {"name": "fyers", "dry_run": true, "live_data": true, "websocket_enabled": true, "rate_limiting": {"enabled": true, "strict_mode": false}}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "pairlists": [{"method": "StaticPairList", "config": {"pairs": ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ", "NSE:INFY-EQ", "NSE:HDFCBANK-EQ"]}}], "protections": [{"method": "<PERSON><PERSON><PERSON><PERSON>", "lookback_period_candles": 60, "trade_limit": 4, "stop_duration_candles": 60, "only_per_pair": false}, {"method": "MaxDrawdown", "lookback_period_candles": 200, "trade_limit": 20, "stop_duration_candles": 10, "max_allowed_drawdown": 0.2}, {"method": "LowProfitPairs", "lookback_period_candles": 60, "trade_limit": 2, "stop_duration_candles": 60, "required_profit": 0.02}], "timeframe": "1d", "timerange": "", "dataformat_ohlcv": "json", "startup_candle_count": 30, "unfilledtimeout": {"buy": 10, "sell": 10, "exit_timeout_count": 0, "unit": "minutes"}, "bid_strategy": {"price_side": "bid", "ask_last_balance": 0.0, "use_order_book": true, "order_book_top": 1, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "ask_strategy": {"price_side": "ask", "use_order_book": true, "order_book_top": 1}, "order_types": {"buy": "market", "sell": "market", "emergencysell": "market", "forcebuy": "market", "forcesell": "market", "stoploss": "market", "stoploss_on_exchange": false, "stoploss_on_exchange_interval": 60, "stoploss_on_exchange_limit_ratio": 0.99}, "order_time_in_force": {"buy": "gtc", "sell": "gtc"}, "strategies": [{"name": "DefaultStrategy", "enabled": true, "params": {"buy_rsi_threshold": 30, "sell_rsi_threshold": 70, "minimal_roi": {"0": 0.1, "40": 0.05, "100": 0.02, "180": 0}, "stoploss": -0.1}}], "minimal_roi": {"0": 0.1, "40": 0.05, "100": 0.02, "180": 0}, "stoploss": -0.1, "trailing_stop": false, "trailing_stop_positive": null, "trailing_stop_positive_offset": 0.0, "trailing_only_offset_is_reached": false, "use_sell_signal": true, "sell_profit_only": false, "sell_profit_offset": 0.0, "ignore_roi_if_buy_signal": false, "ignore_buying_expired_candle_after": 300, "position_adjustment_enable": false, "max_entry_position_adjustment": -1, "backtest": {"enabled": true, "timeframe": "1d", "timerange": "20230101-20241201", "startup_candle_count": 30, "enable_protections": true, "strategy_list": ["DefaultStrategy"]}, "hyperopt": {"enabled": false, "hyperopt_loss": "SharpeHyperOptLoss", "epochs": 100, "spaces": ["buy", "sell", "roi", "stoploss"], "hyperopt_min_trades": 1}, "edge": {"enabled": false, "process_throttle_secs": 3600, "calculate_since_number_of_days": 7, "allowed_risk": 0.01, "stoploss_range_min": -0.01, "stoploss_range_max": -0.1, "stoploss_range_step": -0.01, "minimum_winrate": 0.6, "minimum_expectancy": 0.2, "min_trade_number": 10, "max_trade_duration_minute": 1440, "remove_pumps": false}, "telegram": {"enabled": false, "token": "", "chat_id": "", "notification_settings": {"status": "on", "warning": "on", "startup": "on", "buy": "on", "sell": "on", "buy_cancel": "on", "sell_cancel": "on", "buy_fill": "on", "sell_fill": "on", "protection_trigger": "on", "protection_trigger_global": "on"}}, "api_server": {"enabled": false, "listen_ip_address": "127.0.0.1", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "your-secret-key", "CORS_origins": [], "username": "", "password": ""}, "db_url": "sqlite:///userdata/tradesv3.sqlite", "initial_state": "running", "forcebuy_enable": false, "internals": {"process_throttle_secs": 5}, "money_management": {"enabled": true, "allocation_method": "equal_weight", "risk_per_trade": 0.02, "max_portfolio_risk": 0.1, "position_sizing": {"method": "fixed_amount", "amount": 1000}, "risk_management": {"max_drawdown": 0.2, "stop_loss": 0.1, "take_profit": 0.15}}, "logging": {"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}, "handlers": {"default": {"level": "INFO", "formatter": "default", "class": "logging.StreamHandler", "stream": "ext://sys.stdout"}, "file": {"level": "DEBUG", "formatter": "default", "class": "logging.FileHandler", "filename": "userdata/logs/rapidtrader.log"}}, "loggers": {"rapidtrader": {"handlers": ["default", "file"], "level": "INFO"}, "fyers": {"handlers": ["default", "file"], "level": "INFO"}}}}