# 🚀 RapidTrader Complete API Integration Guide

## 📋 Overview

This document demonstrates the complete API integration in RapidTrader, showing how all components work together to provide a comprehensive trading platform.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 Frontend (React/TypeScript)          │
│  ├── 📊 Dashboard & Trading Interface                      │
│  ├── 💼 Portfolio Management                               │
│  ├── 📈 Real-time Charts & Data                           │
│  └── ⚙️ Settings & Configuration                          │
├─────────────────────────────────────────────────────────────┤
│                    🚪 API Gateway (FastAPI)                │
│  ├── 🔐 Authentication (API Keys)                         │
│  ├── 🏦 Unified Broker Interface                          │
│  ├── 📊 Symbol Management                                 │
│  ├── 🐳 Container Orchestration                           │
│  └── 📡 WebSocket Real-time Updates                       │
├─────────────────────────────────────────────────────────────┤
│                    🔌 Broker Adapters                      │
│  ├── Fyers API v3 (WebSocket + REST)                      │
│  ├── DhanHQ (REST API)                                    │
│  ├── Symbol Mapping System                                │
│  └── Rate Limiting & Error Handling                       │
├─────────────────────────────────────────────────────────────┤
│                    💾 Data Layer                           │
│  ├── 📈 Market Data Service                               │
│  ├── 🗃️ Symbol Manager (Daily Updates)                    │
│  ├── 💰 Money Management                                  │
│  └── 📊 Backtesting Engine                                │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 API Integration Components

### 1. **API Gateway (`api_gateway/main.py`)**

**Core Features:**
- FastAPI-based REST API
- OpenAlgo-inspired endpoint structure
- API key authentication
- Rate limiting and security
- WebSocket support for real-time updates

**Key Endpoints:**
```python
# Health & Status
GET  /health                    # System health check
GET  /                         # API information

# Authentication
POST /auth/create-key          # Create API key
GET  /auth/profile             # User profile

# Broker Management
GET    /brokers                # List brokers
POST   /brokers/{broker_id}    # Add broker
DELETE /brokers/{broker_id}    # Remove broker

# Trading Operations (OpenAlgo Compatible)
POST /api/v1/placeorder        # Place order
GET  /api/v1/orderbook         # Get orders
GET  /api/v1/tradebook         # Get trades
GET  /api/v1/positions         # Get positions
GET  /api/v1/funds             # Get account funds
GET  /api/v1/quotes            # Get market quotes

# Symbol Management
GET  /api/v1/symbols/search    # Search symbols
GET  /api/v1/symbols/{symbol}  # Get symbol info

# Container Management
GET  /containers               # List containers
POST /containers/manage        # Manage containers

# WebSocket
WS   /ws                       # Real-time updates
```

### 2. **Frontend Integration (`frontend_v2/src/services/rapidtraderApi.ts`)**

**TypeScript API Client:**
```typescript
class RapidTraderAPI {
  private api: AxiosInstance
  private apiKey: string

  // Authentication
  async login(apiKey: string): Promise<boolean>
  async logout(): void

  // Trading Operations
  async placeOrder(order: OrderRequest, brokerId: string)
  async getPositions(brokerId: string)
  async getOrderbook(brokerId: string)
  async getFunds(brokerId: string)
  async getQuotes(symbols: string[], brokerId: string)

  // Container Management
  async listContainers()
  async manageContainer(action: string, containerType: string, config?: any)

  // WebSocket Connection
  connectWebSocket(): WebSocket
}
```

**React Components:**
- `Dashboard.tsx` - Main trading dashboard
- `Trading.tsx` - Order placement interface
- `Positions.tsx` - Portfolio management
- `Brokers.tsx` - Broker configuration
- `Symbols.tsx` - Symbol search and management

### 3. **Broker Integration (`broker/`)**

**Unified Broker Interface:**
```python
class BrokerInterface:
    def authenticate(self) -> Dict[str, Any]
    def place_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]
    def get_positions(self) -> Dict[str, Any]
    def get_orderbook(self) -> Dict[str, Any]
    def get_funds(self) -> Dict[str, Any]
    def get_quotes(self, symbols: List[str]) -> Dict[str, Any]
```

**Fyers Integration:**
- `fyers_wrapper.py` - Complete Fyers API v3 wrapper
- `fyers_websocket.py` - Real-time data streaming
- `fyers_symbol_mapper.py` - Symbol conversion system

**DhanHQ Integration:**
- `dhan_wrapper.py` - DhanHQ API wrapper
- `symbol_mapper.py` - Security ID mapping system

### 4. **Symbol Management (`data/symbol_manager.py`)**

**Daily Symbol Updates:**
```python
class SymbolManager:
    def download_nse_symbols(self) -> List[Dict[str, Any]]
    def download_bse_symbols(self) -> List[Dict[str, Any]]
    def create_master_contract(self) -> bool
    def schedule_daily_updates(self)
    def search_symbols(self, query: str) -> List[Dict[str, Any]]
```

**Features:**
- Daily symbol downloads from NSE/BSE
- Master contract creation
- Symbol search and validation
- Fallback symbol lists
- Automatic scheduling

### 5. **Data Caching & Management**

**Market Data Service:**
```python
class MarketDataService:
    def get_quote(self, symbol: str, exchange: str) -> Dict[str, Any]
    def get_historical_data(self, symbol: str, timeframe: str) -> pd.DataFrame
    def subscribe_realtime(self, symbols: List[str]) -> None
```

**Features:**
- Intelligent caching with TTL
- Background refresh loops
- Multiple data source support
- Rate limiting compliance

## 🚀 Usage Examples

### 1. **Start the Complete System**

```bash
# 1. Activate virtual environment
source venv/bin/activate

# 2. Start API Gateway
python scripts/start_api_demo.py

# 3. Start Frontend (in another terminal)
cd frontend_v2
npm run dev

# 4. Access the system
# API: http://localhost:8000
# Frontend: http://localhost:3000
# API Docs: http://localhost:8000/docs
```

### 2. **API Usage Examples**

**Authentication:**
```bash
# Create API key
curl -X POST "http://localhost:8000/auth/create-key" \
  -H "Content-Type: application/json" \
  -d '{"name": "my-trading-key", "permissions": ["read", "write"]}'

# Use API key
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/profile"
```

**Trading Operations:**
```bash
# Place order
curl -X POST "http://localhost:8000/api/v1/placeorder" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "RELIANCE-EQ",
    "quantity": 1,
    "side": "BUY",
    "order_type": "MARKET",
    "product": "MIS"
  }' \
  -G -d "broker_id=fyers"

# Get positions
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/positions?broker_id=fyers"

# Get market quotes
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/quotes?symbols=RELIANCE,TCS,INFY&broker_id=fyers"
```

**Symbol Management:**
```bash
# Search symbols
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/symbols/search?query=RELIANCE&limit=5"

# Get symbol info
curl -H "Authorization: Bearer your-api-key" \
  "http://localhost:8000/api/v1/symbols/RELIANCE"
```

### 3. **Frontend Integration**

**React Component Example:**
```typescript
import { useRapidTraderAPI } from '../services/rapidtraderApi'

function TradingComponent() {
  const api = useRapidTraderAPI()
  
  const placeOrder = async () => {
    try {
      const order = {
        symbol: 'RELIANCE-EQ',
        quantity: 1,
        side: 'BUY',
        order_type: 'MARKET'
      }
      
      const result = await api.placeOrder(order, 'fyers')
      console.log('Order placed:', result)
    } catch (error) {
      console.error('Order failed:', error)
    }
  }
  
  return (
    <button onClick={placeOrder}>
      Buy RELIANCE
    </button>
  )
}
```

### 4. **Daily Symbol Updates**

```bash
# Manual symbol update
python data/symbol_manager.py --update NSE

# Create master contract
python data/symbol_manager.py --master-contract

# Search symbols
python data/symbol_manager.py --search RELIANCE

# Start daily scheduler
python data/symbol_manager.py --schedule
```

## 🔐 Security Features

### 1. **API Key Authentication**
- Secure API key generation
- Permission-based access control
- Rate limiting per API key
- Automatic key expiration

### 2. **Broker Security**
- Encrypted credential storage
- Token refresh mechanisms
- Rate limiting compliance
- Error handling and logging

### 3. **Data Protection**
- Input validation and sanitization
- SQL injection prevention
- CORS configuration
- Secure WebSocket connections

## 📊 Testing & Validation

### 1. **Comprehensive Test Suite**
```bash
# Run all tests
python test/run_comprehensive_tests.py

# Test specific components
python test/run_tests.py --modules test_api_gateway_core
python test/run_tests.py --modules test_broker_integrations
```

### 2. **API Integration Demo**
```bash
# Run API integration demo
python demo/api_integration_demo.py
```

### 3. **Health Monitoring**
```bash
# Check system health
python core/rapidtrader.py health

# API health check
curl http://localhost:8000/health
```

## 🎯 Key Benefits

### 1. **OpenAlgo Compatibility**
- Same endpoint patterns as OpenAlgo
- Compatible request/response formats
- Easy migration from existing tools

### 2. **Multi-Broker Support**
- Unified interface for all brokers
- Automatic symbol mapping
- Consistent error handling

### 3. **Real-time Capabilities**
- WebSocket streaming
- Live market data
- Instant order updates

### 4. **Production Ready**
- Comprehensive error handling
- Rate limiting and security
- Monitoring and logging
- Docker deployment

## 🚀 Next Steps

1. **Deploy to Production**
   - Configure production environment
   - Set up monitoring and alerting
   - Implement backup strategies

2. **Add More Brokers**
   - Implement additional broker adapters
   - Extend symbol mapping system
   - Test with real broker APIs

3. **Enhanced Features**
   - Advanced order types
   - Portfolio analytics
   - Risk management tools
   - Automated trading strategies

---

**🎉 RapidTrader provides a complete, production-ready API integration for algorithmic trading in Indian markets!**
