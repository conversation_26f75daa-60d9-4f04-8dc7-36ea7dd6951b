# RapidTrader .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# RapidTrader Specific

# Historical data files (too large for git)
userdata/historical_data/*.csv
userdata/historical_data/yfinance/*.csv
userdata/historical_data/*.db
userdata/historical_data/*.sqlite

# Results files (generated during backtesting)
userdata/results/*.json

# Logs
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# API Keys and sensitive data
.env.local
.env.production
.env.staging
config/secrets.json
broker/api_keys.json

# Zone Identifier files (Windows)
*:Zone.Identifier

# Backup files
*.bak
*.backup
*~

# Cache directories
.cache/
cache/

# Test outputs
test_output/
test_results/

# Documentation build
docs/_build/
docs/build/

# Local configuration overrides
local_config.json
user_config.json

# Performance test results
performance_*.json
benchmark_*.json

# Temporary strategy files
temp_strategy_*.py
test_strategy_*.py

# Database files
*.db
*.sqlite
*.sqlite3

# CSV data files (keep structure, ignore data)
data/symbols/*.csv
!data/symbols/README.md

# Keep important empty directories
!userdata/historical_data/.gitkeep
!userdata/results/.gitkeep
!logs/.gitkeep