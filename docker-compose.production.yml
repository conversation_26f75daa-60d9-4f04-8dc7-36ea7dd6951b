version: '3.8'

# Production-ready Docker Compose with Alpine images, log aggregation, and separate UI hosting

services:
  # Base service configuration with Alpine image
  rapidtrader-base: &rapidtrader-base
    build:
      context: .
      dockerfile: Dockerfile.alpine
    image: rapidtrader:alpine
    volumes:
      - ./userdata:/rapidtrader/userdata
      - rapidtrader-logs:/rapidtrader/logs
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/config.json
      - RAPIDTRADER_ENV=production
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.managed=true"
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Central API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: api_gateway/Dockerfile
    image: rapidtrader-api:alpine
    container_name: rapidtrader-api-gateway
    ports:
      - "8000:8000"
    volumes:
      - ./userdata:/rapidtrader/userdata
      - rapidtrader-logs:/rapidtrader/logs
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - RAPIDTRADER_ENV=production
      - API_HOST=0.0.0.0
      - API_PORT=8000
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=api-gateway"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      - loki

  # Frontend (served separately)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: rapidtrader-frontend:latest
    container_name: rapidtrader-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - NODE_ENV=production
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=frontend"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy for frontend
  nginx:
    image: nginx:alpine
    container_name: rapidtrader-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=nginx"
    restart: unless-stopped
    depends_on:
      - frontend
      - api-gateway

  # Log aggregation with Loki
  loki:
    image: grafana/loki:2.9.0
    container_name: rapidtrader-loki
    ports:
      - "3100:3100"
    volumes:
      - loki-data:/loki
      - ./logging/loki-config.yml:/etc/loki/local-config.yaml:ro
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=loki"
    restart: unless-stopped

  # Log collection with Promtail
  promtail:
    image: grafana/promtail:2.9.0
    container_name: rapidtrader-promtail
    volumes:
      - rapidtrader-logs:/rapidtrader/logs:ro
      - ./logging/promtail-config.yml:/etc/promtail/config.yml:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=promtail"
    restart: unless-stopped
    depends_on:
      - loki

  # Grafana for log visualization
  grafana:
    image: grafana/grafana:10.1.0
    container_name: rapidtrader-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=rapidtrader123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - rapidtrader-network
    labels:
      - "rapidtrader.service=grafana"
    restart: unless-stopped
    depends_on:
      - loki

  # Enhanced Backtesting service
  enhanced-backtest:
    <<: *rapidtrader-base
    container_name: ${BACKTEST_CONTAINER_NAME:-rapidtrader-enhanced-backtest}
    command: >
      sh -c "
      echo '🚀 Starting Enhanced Backtest';
      python -m core.rapidtrader backtest enhanced 
      -s ${STRATEGY:-DefaultStrategy} 
      -c ${CONFIG:-backtest-config} 
      -y ${SYMBOLS:-RELIANCE,TCS} 
      -t ${TIMEFRAME:-1d} 
      ${TIMERANGE:+--timerange} ${TIMERANGE};
      "
    profiles:
      - enhanced-backtest
    environment:
      - STRATEGY=${STRATEGY:-DefaultStrategy}
      - CONFIG=${CONFIG:-backtest-config}
      - SYMBOLS=${SYMBOLS:-RELIANCE,TCS}
      - TIMEFRAME=${TIMEFRAME:-1d}
      - TIMERANGE=${TIMERANGE:-}
    restart: "no"
    labels:
      - "rapidtrader.managed=true"
      - "rapidtrader.type=backtest"
      - "rapidtrader.strategy=${STRATEGY:-DefaultStrategy}"

  # Fyers trading service (dry-run)
  fyers-dryrun:
    <<: *rapidtrader-base
    container_name: rapidtrader-fyers-dryrun
    command: trade dryrun -c fyers-config.json
    profiles:
      - fyers-dryrun
      - fyers
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/configs/fyers-config.json
    labels:
      - "rapidtrader.managed=true"
      - "rapidtrader.type=dryrun"
      - "rapidtrader.broker=fyers"

  # Fyers trading service (live)
  fyers-live:
    <<: *rapidtrader-base
    container_name: rapidtrader-fyers-live
    command: trade start -c fyers-live-config.json
    profiles:
      - fyers-live
      - fyers
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/configs/fyers-live-config.json
    labels:
      - "rapidtrader.managed=true"
      - "rapidtrader.type=live"
      - "rapidtrader.broker=fyers"

  # Market Hours Manager
  market-manager:
    <<: *rapidtrader-base
    container_name: rapidtrader-market-manager
    command: python scripts/docker_market_manager.py --exchange NSE
    profiles:
      - market-manager
      - auto-trading
    environment:
      - EXCHANGE=NSE
    labels:
      - "rapidtrader.service=market-manager"
    depends_on:
      - api-gateway

  # Log rotation service
  logrotate:
    image: alpine:latest
    container_name: rapidtrader-logrotate
    volumes:
      - rapidtrader-logs:/rapidtrader/logs
      - ./logging/logrotate.conf:/etc/logrotate.conf:ro
    command: >
      sh -c "
      apk add --no-cache logrotate;
      while true; do
        logrotate -f /etc/logrotate.conf;
        sleep 3600;
      done
      "
    profiles:
      - logging
      - production
    labels:
      - "rapidtrader.service=logrotate"
    restart: unless-stopped

# Named volumes for data persistence
volumes:
  rapidtrader-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  loki-data:
    driver: local
  grafana-data:
    driver: local

# Networks
networks:
  rapidtrader-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
