#!/bin/bash
#
# RapidTrader Docker entrypoint script
# This script serves as the main entry point for the RapidTrader Docker container
#

set -e

# Function to print messages
function print_message() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1"
}

# Function to check if a directory exists and create it if it doesn't
function ensure_directory() {
    if [ ! -d "$1" ]; then
        print_message "Creating directory: $1"
        mkdir -p "$1"
    fi
}

# Ensure required directories exist
ensure_directory "/rapidtrader/userdata"
ensure_directory "/rapidtrader/userdata/data"
ensure_directory "/rapidtrader/userdata/logs"
ensure_directory "/rapidtrader/userdata/config"
ensure_directory "/rapidtrader/userdata/strategies"
ensure_directory "/rapidtrader/userdata/historical_data"

# Set default config file if not provided
if [ -z "$CONFIG_FILE" ]; then
    CONFIG_FILE="/rapidtrader/userdata/config/config.json"
    print_message "No config file specified, using default: $CONFIG_FILE"
fi

# Check if we're running in a specific mode
MODE=${1:-help}

case $MODE in
    # Help mode - show available commands
    "help")
        echo "RapidTrader Docker container"
        echo ""
        echo "Available commands:"
        echo "  backtest    - Run backtesting"
        echo "  dryrun      - Run in dry-run mode (broker-based)"
        echo "  paper-trade - Run independent paper trading (yfinance-based)"
        echo "  paper       - Alias for paper-trade"
        echo "  live        - Run in live trading mode"
        echo "  optimize    - Run hyperparameter optimization"
        # Data download mode removed as requested
        echo "  shell       - Start a shell session"
        echo "  help        - Show this help message"
        echo ""
        echo "Examples:"
        echo "  docker run -v $(pwd)/userdata:/rapidtrader/userdata rapidtrader backtest -c config.json"
        echo "  docker run -v $(pwd)/userdata:/rapidtrader/userdata rapidtrader paper-trade --duration 3600"
        echo "  docker run -v $(pwd)/userdata:/rapidtrader/userdata rapidtrader dryrun -c dryrun-config.json"
        # Data download example removed as requested
        echo ""
        ;;

    # Backtesting mode
    "backtest")
        print_message "Starting backtesting mode"
        shift
        exec python /rapidtrader/rapidtrader backtest run -c $CONFIG_FILE $@
        ;;

    # Dry-run mode (traditional broker-based)
    "dryrun")
        print_message "Starting dry-run mode (broker-based)"
        shift
        exec python /rapidtrader/rapidtrader trade dryrun -c $CONFIG_FILE $@
        ;;

    # Paper trading mode (independent)
    "paper-trade"|"paper")
        print_message "Starting independent paper trading mode"
        shift
        exec python /rapidtrader/rapidtrader paper-trade start -c $CONFIG_FILE $@
        ;;

    # Live trading mode
    "live")
        print_message "Starting live trading mode"
        shift
        exec python /rapidtrader/rapidtrader trade start -c $CONFIG_FILE $@
        ;;

    # Optimization mode
    "optimize")
        print_message "Starting optimization mode"
        shift
        exec python /rapidtrader/rapidtrader backtest optimize -c $CONFIG_FILE $@
        ;;

    # Data download mode removed as requested

    # Shell mode
    "shell")
        print_message "Starting shell session"
        exec /bin/bash
        ;;

    # Pass any other commands directly to rapidtrader.py
    *)
        exec python /rapidtrader/rapidtrader $@
        ;;
esac
