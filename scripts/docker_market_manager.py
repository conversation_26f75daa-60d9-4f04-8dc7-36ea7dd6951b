#!/usr/bin/env python3
"""
Docker Market Manager for RapidTrader

This script manages Docker containers based on market hours.
It automatically pauses containers when markets close and resumes them when markets open.

Features:
- Automatic pause/resume of Docker containers
- Market hours monitoring
- Support for multiple exchanges
- Graceful container management
- Logging and monitoring
"""

import os
import sys
import time
import logging
import subprocess
import threading
from datetime import datetime
from typing import List, Dict, Optional
import argparse

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.market_hours_manager import MarketHoursManager, MarketStatus

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("docker_market_manager")

class DockerMarketManager:
    """Manages Docker containers based on market hours."""
    
    def __init__(self, exchange: str = "NSE", containers: List[str] = None):
        """
        Initialize Docker market manager.
        
        Args:
            exchange: Exchange name (NSE, BSE, NYSE, etc.)
            containers: List of container names to manage
        """
        self.exchange = exchange
        self.containers = containers or []
        self.market_manager = MarketHoursManager(exchange)
        self.running = False
        self.monitor_thread = None
        
        # Container states
        self.container_states = {}
        self.paused_containers = set()
        
        # Setup market callbacks
        self.market_manager.add_pause_callback(self._pause_containers)
        self.market_manager.add_resume_callback(self._resume_containers)
        self.market_manager.add_status_callback(self._on_status_change)
        
        logger.info(f"DockerMarketManager initialized for {exchange}")
    
    def add_container(self, container_name: str):
        """Add a container to be managed."""
        if container_name not in self.containers:
            self.containers.append(container_name)
            logger.info(f"Added container: {container_name}")
    
    def remove_container(self, container_name: str):
        """Remove a container from management."""
        if container_name in self.containers:
            self.containers.remove(container_name)
            logger.info(f"Removed container: {container_name}")
    
    def _run_docker_command(self, command: List[str]) -> bool:
        """
        Run a docker command.
        
        Args:
            command: Docker command as list
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                logger.debug(f"Docker command successful: {' '.join(command)}")
                return True
            else:
                logger.error(f"Docker command failed: {' '.join(command)}")
                logger.error(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"Docker command timed out: {' '.join(command)}")
            return False
        except Exception as e:
            logger.error(f"Error running docker command: {e}")
            return False
    
    def _get_container_status(self, container_name: str) -> Optional[str]:
        """Get the status of a container."""
        try:
            result = subprocess.run(
                ["docker", "inspect", "--format={{.State.Status}}", container_name],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return None
    
    def _pause_containers(self):
        """Pause all managed containers."""
        logger.info("🔴 Market closed - Pausing containers")
        
        for container in self.containers:
            status = self._get_container_status(container)
            
            if status == "running":
                logger.info(f"Pausing container: {container}")
                if self._run_docker_command(["docker", "pause", container]):
                    self.paused_containers.add(container)
                    logger.info(f"✅ Container paused: {container}")
                else:
                    logger.error(f"❌ Failed to pause container: {container}")
            elif status == "paused":
                logger.info(f"Container already paused: {container}")
                self.paused_containers.add(container)
            else:
                logger.warning(f"Container not running: {container} (status: {status})")
    
    def _resume_containers(self):
        """Resume all paused containers."""
        logger.info("🟢 Market opened - Resuming containers")
        
        for container in list(self.paused_containers):
            status = self._get_container_status(container)
            
            if status == "paused":
                logger.info(f"Resuming container: {container}")
                if self._run_docker_command(["docker", "unpause", container]):
                    self.paused_containers.remove(container)
                    logger.info(f"✅ Container resumed: {container}")
                else:
                    logger.error(f"❌ Failed to resume container: {container}")
            else:
                logger.warning(f"Container not paused: {container} (status: {status})")
                self.paused_containers.discard(container)
    
    def _on_status_change(self, status: MarketStatus, details: Dict):
        """Handle market status changes."""
        logger.info(f"📊 Market status changed: {status.value.upper()}")
        
        if status == MarketStatus.OPEN:
            logger.info("Market is open - containers should be running")
        elif status == MarketStatus.CLOSED:
            logger.info("Market is closed - containers should be paused")
        elif status == MarketStatus.PRE_MARKET:
            logger.info("Pre-market session - containers running")
        elif status == MarketStatus.POST_MARKET:
            logger.info("Post-market session - containers running")
        elif status == MarketStatus.HOLIDAY:
            logger.info("Market holiday - containers paused")
    
    def start_monitoring(self):
        """Start monitoring market hours and managing containers."""
        if self.running:
            logger.warning("Already monitoring")
            return
        
        logger.info("Starting Docker market manager...")
        
        # Check initial market status
        status, details = self.market_manager.get_current_status()
        logger.info(f"Initial market status: {status.value.upper()}")
        
        # Apply initial state
        if status != MarketStatus.OPEN:
            self._pause_containers()
        
        # Start market monitoring
        self.market_manager.start_monitoring()
        self.running = True
        
        logger.info("Docker market manager started")
    
    def stop_monitoring(self):
        """Stop monitoring and resume all containers."""
        if not self.running:
            return
        
        logger.info("Stopping Docker market manager...")
        
        # Stop market monitoring
        self.market_manager.stop_monitoring()
        
        # Resume all paused containers
        if self.paused_containers:
            logger.info("Resuming all paused containers before shutdown...")
            self._resume_containers()
        
        self.running = False
        logger.info("Docker market manager stopped")
    
    def get_status(self) -> Dict:
        """Get current status of all managed containers."""
        status = {
            "exchange": self.exchange,
            "market_status": self.market_manager.get_current_status()[0].value,
            "containers": {},
            "paused_containers": list(self.paused_containers)
        }
        
        for container in self.containers:
            container_status = self._get_container_status(container)
            status["containers"][container] = container_status
        
        return status

def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Docker Market Manager for RapidTrader")
    parser.add_argument("--exchange", default="NSE", help="Exchange name (default: NSE)")
    parser.add_argument("--containers", nargs="+", help="Container names to manage")
    parser.add_argument("--action", choices=["start", "stop", "status"], default="start",
                       help="Action to perform")
    
    args = parser.parse_args()
    
    # Default containers if none specified
    containers = args.containers or [
        "rapidtrader-fyers-dryrun",
        "rapidtrader-fyers-live",
        "rapidtrader-dhan-dryrun", 
        "rapidtrader-dhan-live"
    ]
    
    manager = DockerMarketManager(args.exchange, containers)
    
    if args.action == "start":
        try:
            manager.start_monitoring()
            
            # Keep running until interrupted
            while True:
                time.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
        finally:
            manager.stop_monitoring()
            
    elif args.action == "stop":
        manager.stop_monitoring()
        
    elif args.action == "status":
        status = manager.get_status()
        print("\n" + "="*50)
        print("Docker Market Manager Status")
        print("="*50)
        print(f"Exchange: {status['exchange']}")
        print(f"Market Status: {status['market_status'].upper()}")
        print(f"Paused Containers: {len(status['paused_containers'])}")
        
        print("\nContainer Status:")
        for container, state in status['containers'].items():
            icon = "⏸️" if container in status['paused_containers'] else "▶️"
            print(f"  {icon} {container}: {state or 'not found'}")

if __name__ == "__main__":
    main()
