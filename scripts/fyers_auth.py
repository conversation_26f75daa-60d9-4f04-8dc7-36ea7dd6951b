#!/usr/bin/env python3
"""
Fyers Authentication Helper Script

This script helps generate and refresh Fyers API access tokens.
It reads credentials from .env file and generates access tokens from auth codes.

Usage:
    python scripts/fyers_auth.py --generate-token
    python scripts/fyers_auth.py --refresh-token
    python scripts/fyers_auth.py --check-token
"""

import os
import sys
import argparse
from pathlib import Path
from dotenv import load_dotenv, set_key

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from fyers_apiv3 import fyersModel
    _has_fyers_api = True
except ImportError:
    _has_fyers_api = False
    print("Error: fyers-apiv3 package not found. Install with: pip install fyers-apiv3")
    sys.exit(1)


class FyersAuthenticator:
    """Helper class for Fyers API authentication."""

    def __init__(self):
        """Initialize with credentials from .env file."""
        # Load environment variables
        env_path = project_root / '.env'
        load_dotenv(env_path)

        self.env_path = env_path
        self.client_id = os.getenv('FYERS_CLIENT_ID')
        self.secret_key = os.getenv('FYERS_SECRET_KEY')
        self.redirect_uri = os.getenv('FYERS_REDIRECT_URI')
        self.auth_code = os.getenv('FYERS_AUTH_CODE')
        self.access_token = os.getenv('FYERS_ACCESS_TOKEN')
        self.refresh_token = os.getenv('FYERS_REFRESH_TOKEN')

        # Validate required credentials
        if not all([self.client_id, self.secret_key, self.redirect_uri]):
            print("Error: Missing required Fyers credentials in .env file")
            print("Required: FYERS_CLIENT_ID, FYERS_SECRET_KEY, FYERS_REDIRECT_URI")
            sys.exit(1)

    def generate_access_token(self):
        """Generate access token from auth code."""
        if not self.auth_code:
            print("Error: FYERS_AUTH_CODE not found in .env file")
            print("Please set FYERS_AUTH_CODE in your .env file")
            return False

        try:
            print("Generating access token from auth code...")

            # Create session model
            session = fyersModel.SessionModel(
                client_id=self.client_id,
                secret_key=self.secret_key,
                redirect_uri=self.redirect_uri,
                response_type="code",
                grant_type="authorization_code"
            )

            # Set the auth code
            session.set_token(self.auth_code)

            # Generate access token
            response = session.generate_token()

            if response.get('s') == 'ok':
                access_token = response.get('access_token')
                refresh_token = response.get('refresh_token')

                if access_token:
                    # Update .env file
                    set_key(self.env_path, 'FYERS_ACCESS_TOKEN', access_token)
                    if refresh_token:
                        set_key(self.env_path, 'FYERS_REFRESH_TOKEN', refresh_token)

                    print("✓ Access token generated successfully!")
                    print(f"Access Token: {access_token[:20]}...")
                    if refresh_token:
                        print(f"Refresh Token: {refresh_token[:20]}...")

                    # Update instance variables
                    self.access_token = access_token
                    self.refresh_token = refresh_token

                    return True
                else:
                    print("✗ No access token in response")
                    print(f"Response: {response}")
                    return False
            else:
                print("✗ Failed to generate access token")
                print(f"Error: {response.get('message', 'Unknown error')}")
                print(f"Response: {response}")
                return False

        except Exception as e:
            print(f"✗ Error generating access token: {e}")
            return False

    def refresh_access_token(self):
        """Refresh access token using refresh token."""
        if not self.refresh_token:
            print("Error: FYERS_REFRESH_TOKEN not found in .env file")
            print("Please generate a new access token first")
            return False

        try:
            print("Refreshing access token...")

            # Create session model
            session = fyersModel.SessionModel(
                client_id=self.client_id,
                secret_key=self.secret_key,
                redirect_uri=self.redirect_uri,
                response_type="code",
                grant_type="refresh_token"
            )

            # Set refresh token
            session.set_token(self.refresh_token)

            # Refresh access token
            response = session.refresh_token()

            if response.get('s') == 'ok':
                access_token = response.get('access_token')
                refresh_token = response.get('refresh_token')

                if access_token:
                    # Update .env file
                    set_key(self.env_path, 'FYERS_ACCESS_TOKEN', access_token)
                    if refresh_token:
                        set_key(self.env_path, 'FYERS_REFRESH_TOKEN', refresh_token)

                    print("✓ Access token refreshed successfully!")
                    print(f"New Access Token: {access_token[:20]}...")
                    if refresh_token:
                        print(f"New Refresh Token: {refresh_token[:20]}...")

                    return True
                else:
                    print("✗ No access token in refresh response")
                    print(f"Response: {response}")
                    return False
            else:
                print("✗ Failed to refresh access token")
                print(f"Error: {response.get('message', 'Unknown error')}")
                print(f"Response: {response}")
                return False

        except Exception as e:
            print(f"✗ Error refreshing access token: {e}")
            return False

    def check_token_validity(self):
        """Check if current access token is valid."""
        if not self.access_token:
            print("No access token found in .env file")
            return False

        try:
            print("Checking access token validity...")

            # Create Fyers model to test token
            fyers = fyersModel.FyersModel(
                client_id=self.client_id,
                token=self.access_token,
                log_path=""
            )

            # Test token by getting profile
            response = fyers.get_profile()

            if response.get('s') == 'ok':
                profile = response.get('data', {})
                print("✓ Access token is valid!")
                print(f"User ID: {profile.get('fy_id', 'N/A')}")
                print(f"Name: {profile.get('name', 'N/A')}")
                print(f"Email: {profile.get('email_id', 'N/A')}")
                return True
            else:
                print("✗ Access token is invalid or expired")
                print(f"Error: {response.get('message', 'Unknown error')}")
                return False

        except Exception as e:
            print(f"✗ Error checking token validity: {e}")
            return False

    def print_status(self):
        """Print current authentication status."""
        print("\n=== Fyers Authentication Status ===")
        print(f"Client ID: {self.client_id}")
        print(f"Secret Key: {'*' * len(self.secret_key) if self.secret_key else 'Not set'}")
        print(f"Redirect URI: {self.redirect_uri}")
        print(f"Auth Code: {'Set' if self.auth_code else 'Not set'}")
        print(f"Access Token: {'Set' if self.access_token else 'Not set'}")
        print(f"Refresh Token: {'Set' if self.refresh_token else 'Not set'}")
        print("=" * 40)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Fyers Authentication Helper')
    parser.add_argument('--generate-token', action='store_true',
                       help='Generate access token from auth code')
    parser.add_argument('--refresh-token', action='store_true',
                       help='Refresh access token using refresh token')
    parser.add_argument('--check-token', action='store_true',
                       help='Check if current access token is valid')
    parser.add_argument('--status', action='store_true',
                       help='Show current authentication status')

    args = parser.parse_args()

    # Create authenticator
    auth = FyersAuthenticator()

    # Show status if requested or no other action
    if args.status or not any([args.generate_token, args.refresh_token, args.check_token]):
        auth.print_status()

    # Execute requested actions
    if args.generate_token:
        success = auth.generate_access_token()
        if success:
            print("\n✓ You can now use the Fyers broker with real credentials!")
        else:
            print("\n✗ Failed to generate access token. Please check your auth code.")

    if args.refresh_token:
        success = auth.refresh_access_token()
        if success:
            print("\n✓ Access token refreshed successfully!")
        else:
            print("\n✗ Failed to refresh access token.")

    if args.check_token:
        is_valid = auth.check_token_validity()
        if is_valid:
            print("\n✓ Your access token is valid and ready to use!")
        else:
            print("\n✗ Your access token is invalid. Please generate a new one.")


if __name__ == "__main__":
    main()
