#!/bin/bash
#
# RapidTrader Docker Management Script
# Similar to FreqTrade's docker approach for easy container management
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DOCKER_IMAGE="rapidtrader:latest"
USERDATA_DIR="$(pwd)/userdata"
CONFIG_FILE=""
COMMAND=""
EXTRA_ARGS=""

# Function to print colored messages
print_message() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "RapidTrader Docker Management Script"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  build                    - Build the Docker image"
    echo "  paper-trade             - Run independent paper trading"
    echo "  dryrun                  - Run broker-based dry run"
    echo "  backtest                - Run backtesting (auto-stops when complete)"
    echo "  live                    - Run live trading"
    echo "  optimize                - Run strategy optimization (auto-stops when complete)"
    echo "  shell                   - Start interactive shell"
    echo "  logs                    - Show container logs"
    echo "  stop                    - Stop running containers"
    echo "  stop-all                - Stop all RapidTrader containers"
    echo "  clean                   - Remove stopped containers"
    echo "  status                  - Show container status"
    echo "  monitor                 - Monitor all containers in real-time"
    echo "  restart                 - Restart a specific container"
    echo ""
    echo "Options:"
    echo "  -c, --config <file>     - Configuration file (default: auto-detect)"
    echo "  -d, --userdata <dir>    - Userdata directory (default: ./userdata)"
    echo "  -i, --image <image>     - Docker image (default: rapidtrader:latest)"
    echo "  --duration <seconds>    - Duration for paper trading (default: indefinite)"
    echo "  --capital <amount>      - Initial capital for paper trading (default: 100000)"
    echo "  -h, --help              - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 paper-trade --duration 3600 --capital 50000"
    echo "  $0 dryrun -c userdata/config/dryrun-config.json"
    echo "  $0 backtest -c userdata/config/backtest-config.json"
    echo "  $0 optimize -c userdata/config/optimize-config.json"
    echo "  $0 live -c userdata/config/live-config.json"
    echo "  $0 monitor"
    echo "  $0 stop-all"
    echo "  $0 shell"
    echo ""
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to build Docker image
build_image() {
    print_message "Building RapidTrader Docker image..."
    docker build -t "$DOCKER_IMAGE" .
    print_message "Docker image built successfully: $DOCKER_IMAGE"
}

# Function to get default config file for command
get_default_config() {
    local cmd="$1"
    case "$cmd" in
        "paper-trade")
            echo "userdata/config/dry_run_config.json"
            ;;
        "dryrun")
            echo "userdata/config/dryrun-config.json"
            ;;
        "backtest")
            echo "userdata/config/backtest-config.json"
            ;;
        "live")
            echo "userdata/config/live-config.json"
            ;;
        "optimize")
            echo "userdata/config/optimize-config.json"
            ;;
        *)
            echo ""
            ;;
    esac
}

# Function to run Docker container
run_container() {
    local cmd="$1"
    local container_name="rapidtrader-$cmd"

    # Set default config if not provided
    if [ -z "$CONFIG_FILE" ]; then
        CONFIG_FILE=$(get_default_config "$cmd")
    fi

    # Check if config file exists
    if [ -n "$CONFIG_FILE" ] && [ ! -f "$CONFIG_FILE" ]; then
        print_error "Configuration file not found: $CONFIG_FILE"
        exit 1
    fi

    # Prepare Docker run command
    local docker_cmd="docker run --rm -it"
    docker_cmd="$docker_cmd --name $container_name"
    docker_cmd="$docker_cmd -v $USERDATA_DIR:/rapidtrader/userdata"
    docker_cmd="$docker_cmd --env-file .env"

    # Add config file environment variable
    if [ -n "$CONFIG_FILE" ]; then
        docker_cmd="$docker_cmd -e CONFIG_FILE=/rapidtrader/$CONFIG_FILE"
    fi

    # Add port mapping for certain services
    case "$cmd" in
        "live")
            docker_cmd="$docker_cmd -p 8080:8080"
            ;;
        "paper-trade")
            docker_cmd="$docker_cmd -p 8081:8081"
            ;;
    esac

    docker_cmd="$docker_cmd $DOCKER_IMAGE $cmd $EXTRA_ARGS"

    print_message "Starting $cmd container..."
    print_info "Command: $docker_cmd"

    eval $docker_cmd
}

# Function to show container logs
show_logs() {
    local container_name="$1"
    if [ -z "$container_name" ]; then
        print_error "Please specify container name"
        echo "Available containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
        exit 1
    fi

    docker logs -f "$container_name"
}

# Function to stop specific container
stop_containers() {
    local container_name="$1"
    if [ -n "$container_name" ]; then
        print_message "Stopping container: $container_name"
        docker stop "$container_name" 2>/dev/null || print_warning "Container $container_name not found or already stopped"
    else
        print_error "Please specify container name to stop"
        echo "Available containers:"
        docker ps --filter "name=rapidtrader-*" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
    fi
}

# Function to stop all RapidTrader containers
stop_all_containers() {
    print_message "Stopping all RapidTrader containers..."
    local containers=$(docker ps --filter "name=rapidtrader-*" --format "{{.Names}}")
    if [ -n "$containers" ]; then
        echo "$containers" | xargs -r docker stop
        print_message "All containers stopped"
    else
        print_info "No RapidTrader containers are currently running"
    fi
}

# Function to clean up containers
clean_containers() {
    print_message "Removing stopped RapidTrader containers..."
    docker ps -a --filter "name=rapidtrader-*" --format "{{.Names}}" | xargs -r docker rm
    print_message "Containers cleaned up"
}

# Function to show container status
show_status() {
    print_message "RapidTrader Container Status:"
    echo ""
    echo "Running Containers:"
    docker ps --filter "name=rapidtrader-*" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}" || echo "No running containers"
    echo ""
    echo "All Containers (including stopped):"
    docker ps -a --filter "name=rapidtrader-*" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}" || echo "No containers found"
}

# Function to monitor containers in real-time
monitor_containers() {
    print_message "Monitoring RapidTrader containers (Press Ctrl+C to stop)..."
    echo ""

    while true; do
        clear
        echo "=== RapidTrader Container Monitor ==="
        echo "Last updated: $(date)"
        echo ""

        # Show running containers
        echo "Running Containers:"
        docker ps --filter "name=rapidtrader-*" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "No running containers"
        echo ""

        # Show resource usage
        echo "Resource Usage:"
        docker stats --no-stream --filter "name=rapidtrader-*" --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" 2>/dev/null || echo "No containers to monitor"
        echo ""

        echo "Press Ctrl+C to exit monitor mode"
        sleep 5
    done
}

# Function to restart a container
restart_container() {
    local container_name="$1"
    if [ -z "$container_name" ]; then
        print_error "Please specify container name to restart"
        echo "Available containers:"
        docker ps -a --filter "name=rapidtrader-*" --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
        return 1
    fi

    print_message "Restarting container: $container_name"
    if docker restart "$container_name" 2>/dev/null; then
        print_message "Container $container_name restarted successfully"
    else
        print_error "Failed to restart container $container_name"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -d|--userdata)
            USERDATA_DIR="$2"
            shift 2
            ;;
        -i|--image)
            DOCKER_IMAGE="$2"
            shift 2
            ;;
        --duration)
            EXTRA_ARGS="$EXTRA_ARGS --duration $2"
            shift 2
            ;;
        --capital)
            EXTRA_ARGS="$EXTRA_ARGS --capital $2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        build|paper-trade|dryrun|backtest|live|optimize|shell|logs|stop|stop-all|clean|status|monitor|restart)
            COMMAND="$1"
            shift
            ;;
        *)
            EXTRA_ARGS="$EXTRA_ARGS $1"
            shift
            ;;
    esac
done

# Check if command is provided
if [ -z "$COMMAND" ]; then
    print_error "No command specified"
    show_usage
    exit 1
fi

# Check Docker availability
check_docker

# Execute command
case "$COMMAND" in
    "build")
        build_image
        ;;
    "paper-trade"|"dryrun"|"backtest"|"live"|"optimize"|"shell")
        run_container "$COMMAND"
        ;;
    "logs")
        show_logs "$2"
        ;;
    "stop")
        stop_containers "$2"
        ;;
    "stop-all")
        stop_all_containers
        ;;
    "clean")
        clean_containers
        ;;
    "status")
        show_status
        ;;
    "monitor")
        monitor_containers
        ;;
    "restart")
        restart_container "$2"
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
