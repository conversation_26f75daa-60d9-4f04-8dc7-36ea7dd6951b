#!/usr/bin/env python3
"""
Cleanup script to organize the RapidTrader root directory

This script moves test files and other clutter from the root directory
to appropriate subdirectories for better organization.
"""

import os
import shutil
from pathlib import Path

def cleanup_root_directory():
    """Clean up the root directory by organizing files."""

    # Define the root directory
    root_dir = Path(".")

    # Create directories if they don't exist
    test_dir = root_dir / "test"
    test_dir.mkdir(exist_ok=True)

    examples_dir = root_dir / "examples"
    examples_dir.mkdir(exist_ok=True)

    docs_dir = root_dir / "docs"
    docs_dir.mkdir(exist_ok=True)

    logs_dir = root_dir / "logs"
    logs_dir.mkdir(exist_ok=True)

    print("🧹 Cleaning up RapidTrader root directory...")
    print("=" * 50)

    # Step 1: Remove duplicate/misspelled directories
    cleanup_duplicate_directories(root_dir)

    # Step 2: Consolidate redundant core subdirectories
    cleanup_core_subdirectories(root_dir)

    # Step 3: Move remaining files
    moved_count = move_scattered_files(root_dir, test_dir, docs_dir, logs_dir)

    print("=" * 50)
    print(f"🎉 Cleanup complete! Moved {moved_count} files.")

    # Show current root directory structure
    show_clean_structure(root_dir)

def cleanup_duplicate_directories(root_dir):
    """Remove duplicate and misspelled directories."""
    print("\n🗂️  Removing duplicate directories...")

    # Remove the misspelled 'brocker' directory
    brocker_dir = root_dir / "brocker"
    if brocker_dir.exists():
        try:
            shutil.rmtree(str(brocker_dir))
            print("✅ Removed duplicate 'brocker/' directory")
        except Exception as e:
            print(f"❌ Failed to remove brocker/: {e}")

def cleanup_core_subdirectories(root_dir):
    """Consolidate redundant core subdirectories."""
    print("\n📁 Consolidating core subdirectories...")

    core_dir = root_dir / "core"

    # Remove redundant subdirectories
    redundant_dirs = [
        "dry run",
        "backtester",
        "livetrader",
        "optimiser"
    ]

    for dir_name in redundant_dirs:
        redundant_dir = core_dir / dir_name
        if redundant_dir.exists():
            try:
                shutil.rmtree(str(redundant_dir))
                print(f"✅ Removed redundant core/{dir_name}/ directory")
            except Exception as e:
                print(f"❌ Failed to remove core/{dir_name}/: {e}")

def move_scattered_files(root_dir, test_dir, docs_dir, logs_dir):
    """Move scattered files to appropriate directories."""
    print("\n📄 Moving scattered files...")
    moved_count = 0

    # Files to move to test directory
    test_files = [
        "test_dry_run_integration.py",
        "test_fyers_integration.py",
        "test_fyers_real_credentials.py",
        "test_fyers_websocket.py",
        "test_independent_dry_run.py",
        "test_market_hours.py",
        "test_market_hours_system.py",
        "test_money_manager_live_data.py",
        "test_quote_debug.py",
        "test_rapidtrader_complete.py"
    ]

    # Documentation files to move to docs
    doc_files = [
        "FYERS_INTEGRATION_SUMMARY.md",
        "FYERS_WEBSOCKET_GUIDE.md",
        "GITHUB_UPLOAD_SUMMARY.md",
        "RAPIDTRADER_FYERS_DOCKER_COMPLETE.md"
    ]

    # Log files to move to logs
    log_files = [
        "fyersApi.log",
        "fyersDataSocket.log",
        "fyersRequests.log"
    ]

    # Move test files
    for file_name in test_files:
        file_path = root_dir / file_name
        if file_path.exists():
            target_path = test_dir / file_name
            try:
                shutil.move(str(file_path), str(target_path))
                print(f"✅ Moved {file_name} -> test/")
                moved_count += 1
            except Exception as e:
                print(f"❌ Failed to move {file_name}: {e}")

    # Move documentation files
    for file_name in doc_files:
        file_path = root_dir / file_name
        if file_path.exists():
            target_path = docs_dir / file_name
            try:
                shutil.move(str(file_path), str(target_path))
                print(f"✅ Moved {file_name} -> docs/")
                moved_count += 1
            except Exception as e:
                print(f"❌ Failed to move {file_name}: {e}")

    # Move log files (try to handle permission issues)
    for file_name in log_files:
        file_path = root_dir / file_name
        if file_path.exists():
            target_path = logs_dir / file_name
            try:
                # Try to copy first, then remove original if successful
                shutil.copy2(str(file_path), str(target_path))
                os.remove(str(file_path))
                print(f"✅ Moved {file_name} -> logs/")
                moved_count += 1
            except Exception as e:
                print(f"❌ Failed to move {file_name}: {e}")
                # If move fails, at least try to copy
                try:
                    shutil.copy2(str(file_path), str(target_path))
                    print(f"⚠️  Copied {file_name} -> logs/ (original remains)")
                except Exception as e2:
                    print(f"❌ Failed to copy {file_name}: {e2}")

    return moved_count

def show_clean_structure(root_dir):
    """Show the cleaned root directory structure."""
    print("\n📁 Current root directory structure:")
    print("=" * 50)

    # List only important files/directories in root
    important_items = []
    for item in sorted(root_dir.iterdir()):
        if item.name.startswith('.'):
            continue
        if item.is_dir():
            important_items.append(f"📁 {item.name}/")
        elif item.suffix in ['.py', '.yml', '.yaml', '.json', '.md', '.txt', '.log']:
            # Only show important config/main files
            if item.name in ['rapidtrader', 'requirements.txt', 'README.md',
                           'LICENSE', 'Dockerfile', 'docker-compose.yml'] or item.suffix == '.log':
                important_items.append(f"📄 {item.name}")

    for item in important_items:
        print(f"  {item}")

    print("\n✨ Root directory is now clean and organized!")
    print("📊 Summary of improvements:")
    print("  • Removed duplicate 'brocker/' directory")
    print("  • Consolidated redundant core/ subdirectories")
    print("  • Moved test files to test/ directory")
    print("  • Moved documentation to docs/ directory")
    print("  • Organized log files in logs/ directory")
    print("  • Cleaner, more maintainable project structure")

if __name__ == "__main__":
    cleanup_root_directory()
