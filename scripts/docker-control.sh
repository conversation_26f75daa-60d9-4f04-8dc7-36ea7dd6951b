#!/bin/bash
#
# RapidTrader Docker Control Script
# Complete control and testing of all RapidTrader modes in Docker
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${PURPLE}🔄 $1${NC}"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to build image if needed
ensure_image() {
    if ! docker image inspect rapidtrader:latest >/dev/null 2>&1; then
        print_step "Building RapidTrader Docker image..."
        cd "$PROJECT_DIR"
        docker build -t rapidtrader:latest .
        print_success "Docker image built successfully"
    else
        print_info "Docker image already exists"
    fi
}

# Function to test paper trading
test_paper_trading() {
    print_header "Testing Paper Trading Mode"
    
    print_step "Starting paper trading container for 60 seconds..."
    cd "$PROJECT_DIR"
    
    # Start paper trading in background
    docker run --rm -d \
        --name rapidtrader-test-paper \
        -v "$PROJECT_DIR/userdata:/rapidtrader/userdata" \
        --env-file .env \
        -e CONFIG_FILE=/rapidtrader/userdata/config/dry_run_config.json \
        rapidtrader:latest paper-trade --duration 60 --capital 50000
    
    print_info "Paper trading started. Container: rapidtrader-test-paper"
    
    # Monitor for 30 seconds
    print_step "Monitoring paper trading for 30 seconds..."
    sleep 10
    
    # Check if container is still running
    if docker ps --filter "name=rapidtrader-test-paper" --format "{{.Names}}" | grep -q "rapidtrader-test-paper"; then
        print_success "Paper trading is running successfully"
        
        # Show logs
        print_info "Recent logs:"
        docker logs --tail 10 rapidtrader-test-paper
        
        # Wait for completion or timeout
        print_step "Waiting for paper trading to complete..."
        docker wait rapidtrader-test-paper >/dev/null 2>&1 || true
        
        print_success "Paper trading test completed"
    else
        print_error "Paper trading container stopped unexpectedly"
        return 1
    fi
}

# Function to test dry run
test_dry_run() {
    print_header "Testing Dry Run Mode"
    
    print_step "Starting dry run container for 60 seconds..."
    cd "$PROJECT_DIR"
    
    # Start dry run in background
    timeout 60 docker run --rm \
        --name rapidtrader-test-dryrun \
        -v "$PROJECT_DIR/userdata:/rapidtrader/userdata" \
        --env-file .env \
        -e CONFIG_FILE=/rapidtrader/userdata/config/dryrun-config.json \
        rapidtrader:latest dryrun &
    
    local dryrun_pid=$!
    print_info "Dry run started with PID: $dryrun_pid"
    
    # Monitor for 30 seconds
    sleep 30
    
    # Check if process is still running
    if kill -0 $dryrun_pid 2>/dev/null; then
        print_success "Dry run is running successfully"
        
        # Stop the process
        kill $dryrun_pid 2>/dev/null || true
        wait $dryrun_pid 2>/dev/null || true
        
        print_success "Dry run test completed"
    else
        print_error "Dry run stopped unexpectedly"
        return 1
    fi
}

# Function to test backtesting (auto-stop)
test_backtesting() {
    print_header "Testing Backtesting Mode (Auto-Stop)"
    
    print_step "Starting backtesting container..."
    cd "$PROJECT_DIR"
    
    # Start backtesting
    docker run --rm \
        --name rapidtrader-test-backtest \
        -v "$PROJECT_DIR/userdata:/rapidtrader/userdata" \
        --env-file .env \
        -e CONFIG_FILE=/rapidtrader/userdata/config/backtest-config.json \
        rapidtrader:latest backtest &
    
    local backtest_pid=$!
    print_info "Backtesting started with PID: $backtest_pid"
    
    # Wait for completion with timeout
    print_step "Waiting for backtesting to complete (max 5 minutes)..."
    
    local timeout=300  # 5 minutes
    local elapsed=0
    
    while kill -0 $backtest_pid 2>/dev/null && [ $elapsed -lt $timeout ]; do
        sleep 10
        elapsed=$((elapsed + 10))
        echo -n "."
    done
    echo ""
    
    if kill -0 $backtest_pid 2>/dev/null; then
        print_warning "Backtesting is taking longer than expected, stopping..."
        kill $backtest_pid 2>/dev/null || true
        wait $backtest_pid 2>/dev/null || true
        print_info "Backtesting stopped (timeout)"
    else
        wait $backtest_pid 2>/dev/null || true
        print_success "Backtesting completed automatically"
    fi
}

# Function to test optimization (auto-stop)
test_optimization() {
    print_header "Testing Optimization Mode (Auto-Stop)"
    
    print_step "Starting optimization container..."
    cd "$PROJECT_DIR"
    
    # Start optimization
    docker run --rm \
        --name rapidtrader-test-optimize \
        -v "$PROJECT_DIR/userdata:/rapidtrader/userdata" \
        --env-file .env \
        -e CONFIG_FILE=/rapidtrader/userdata/config/optimize-config.json \
        rapidtrader:latest optimize &
    
    local optimize_pid=$!
    print_info "Optimization started with PID: $optimize_pid"
    
    # Wait for completion with timeout
    print_step "Waiting for optimization to complete (max 3 minutes for demo)..."
    
    local timeout=180  # 3 minutes for demo
    local elapsed=0
    
    while kill -0 $optimize_pid 2>/dev/null && [ $elapsed -lt $timeout ]; do
        sleep 10
        elapsed=$((elapsed + 10))
        echo -n "."
    done
    echo ""
    
    if kill -0 $optimize_pid 2>/dev/null; then
        print_warning "Optimization is taking longer than expected, stopping..."
        kill $optimize_pid 2>/dev/null || true
        wait $optimize_pid 2>/dev/null || true
        print_info "Optimization stopped (timeout)"
    else
        wait $optimize_pid 2>/dev/null || true
        print_success "Optimization completed automatically"
    fi
}

# Function to demonstrate live trading setup (without actual trading)
demo_live_trading() {
    print_header "Demonstrating Live Trading Setup"
    
    print_warning "This is a demonstration only - no real trades will be executed"
    print_step "Starting live trading container for 30 seconds..."
    cd "$PROJECT_DIR"
    
    # Start live trading in demo mode
    timeout 30 docker run --rm \
        --name rapidtrader-test-live \
        -v "$PROJECT_DIR/userdata:/rapidtrader/userdata" \
        --env-file .env \
        -e CONFIG_FILE=/rapidtrader/userdata/config/live-config.json \
        -p 8080:8080 \
        rapidtrader:latest live &
    
    local live_pid=$!
    print_info "Live trading demo started with PID: $live_pid"
    print_info "Web interface would be available at: http://localhost:8080"
    
    # Monitor for 20 seconds
    sleep 20
    
    # Check if process is still running
    if kill -0 $live_pid 2>/dev/null; then
        print_success "Live trading setup is working"
        
        # Stop the process
        kill $live_pid 2>/dev/null || true
        wait $live_pid 2>/dev/null || true
        
        print_success "Live trading demo completed"
    else
        print_error "Live trading stopped unexpectedly"
        return 1
    fi
}

# Function to run all tests
run_all_tests() {
    print_header "RapidTrader Docker Complete Testing Suite"
    
    local tests_passed=0
    local tests_failed=0
    
    # Check prerequisites
    check_docker
    ensure_image
    
    # Run tests
    if test_paper_trading; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    if test_dry_run; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    if test_backtesting; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    if test_optimization; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    if demo_live_trading; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    # Summary
    print_header "Test Results Summary"
    echo "Tests Passed: $tests_passed"
    echo "Tests Failed: $tests_failed"
    echo "Total Tests: $((tests_passed + tests_failed))"
    
    if [ $tests_failed -eq 0 ]; then
        print_success "All tests passed! RapidTrader Docker implementation is working perfectly."
    else
        print_error "$tests_failed tests failed. Please check the output above."
    fi
}

# Main execution
case "${1:-all}" in
    "paper"|"paper-trade")
        check_docker
        ensure_image
        test_paper_trading
        ;;
    "dryrun"|"dry-run")
        check_docker
        ensure_image
        test_dry_run
        ;;
    "backtest")
        check_docker
        ensure_image
        test_backtesting
        ;;
    "optimize")
        check_docker
        ensure_image
        test_optimization
        ;;
    "live")
        check_docker
        ensure_image
        demo_live_trading
        ;;
    "all")
        run_all_tests
        ;;
    *)
        echo "Usage: $0 [paper|dryrun|backtest|optimize|live|all]"
        echo ""
        echo "Commands:"
        echo "  paper     - Test paper trading mode"
        echo "  dryrun    - Test dry run mode"
        echo "  backtest  - Test backtesting mode (auto-stop)"
        echo "  optimize  - Test optimization mode (auto-stop)"
        echo "  live      - Demo live trading setup"
        echo "  all       - Run all tests (default)"
        exit 1
        ;;
esac
