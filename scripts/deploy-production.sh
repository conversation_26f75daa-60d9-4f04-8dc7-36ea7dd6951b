#!/bin/bash
#
# RapidTrader Production Deployment Script
#
# This script deploys RapidTrader with all production optimizations:
# - Alpine-based Docker images
# - Separate UI hosting
# - Central API Gateway
# - Log rotation and aggregation
# - Lazy-loading frontend
#

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        print_error "docker-compose is not installed or not in PATH."
        exit 1
    fi
    
    # Check if Node.js is available for frontend build
    if ! command -v node >/dev/null 2>&1; then
        print_warning "Node.js not found. Frontend will be built in Docker."
    fi
    
    print_success "Prerequisites check passed"
}

# Function to create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p "$PROJECT_DIR/logs/containers"
    mkdir -p "$PROJECT_DIR/logs/trading"
    mkdir -p "$PROJECT_DIR/logs/errors"
    mkdir -p "$PROJECT_DIR/logs/backtests"
    mkdir -p "$PROJECT_DIR/nginx/ssl"
    mkdir -p "$PROJECT_DIR/grafana/provisioning/datasources"
    mkdir -p "$PROJECT_DIR/grafana/provisioning/dashboards"
    
    print_success "Directories created"
}

# Function to build Alpine images
build_alpine_images() {
    print_info "Building Alpine-based Docker images..."
    
    cd "$PROJECT_DIR"
    
    # Build main RapidTrader Alpine image
    print_info "Building RapidTrader Alpine image..."
    docker build -f Dockerfile.alpine -t rapidtrader:alpine .
    
    # Build API Gateway image
    print_info "Building API Gateway image..."
    docker build -f api_gateway/Dockerfile -t rapidtrader-api:alpine .
    
    # Build Frontend image
    print_info "Building Frontend image..."
    docker build -f frontend/Dockerfile -t rapidtrader-frontend:latest ./frontend
    
    print_success "Alpine images built successfully"
}

# Function to setup Grafana datasources
setup_grafana() {
    print_info "Setting up Grafana configuration..."
    
    cat > "$PROJECT_DIR/grafana/provisioning/datasources/loki.yml" << EOF
apiVersion: 1

datasources:
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    isDefault: true
    editable: true
EOF

    cat > "$PROJECT_DIR/grafana/provisioning/dashboards/dashboard.yml" << EOF
apiVersion: 1

providers:
  - name: 'RapidTrader'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF

    print_success "Grafana configuration created"
}

# Function to deploy production stack
deploy_production() {
    print_info "Deploying production stack..."
    
    cd "$PROJECT_DIR"
    
    # Stop any existing containers
    print_info "Stopping existing containers..."
    docker-compose -f docker-compose.production.yml down --remove-orphans || true
    
    # Start the production stack
    print_info "Starting production services..."
    docker-compose -f docker-compose.production.yml up -d \
        nginx \
        api-gateway \
        frontend \
        loki \
        promtail \
        grafana \
        logrotate
    
    # Wait for services to be ready
    print_info "Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    check_service_health
    
    print_success "Production stack deployed successfully"
}

# Function to check service health
check_service_health() {
    print_info "Checking service health..."
    
    local services=(
        "http://localhost:80/health:Nginx"
        "http://localhost:8000/health:API Gateway"
        "http://localhost:3000:Frontend"
        "http://localhost:3100/ready:Loki"
        "http://localhost:3001:Grafana"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r url name <<< "$service"
        
        if curl -f -s "$url" >/dev/null 2>&1; then
            print_success "$name is healthy"
        else
            print_warning "$name is not responding"
        fi
    done
}

# Function to show deployment information
show_deployment_info() {
    print_header "🎉 RapidTrader Production Deployment Complete!"
    
    echo ""
    echo "📊 Service URLs:"
    echo "  🌐 Frontend:     http://localhost"
    echo "  🔌 API Gateway:  http://localhost/api"
    echo "  📈 Grafana:      http://localhost/grafana (admin/rapidtrader123)"
    echo "  🔍 Loki:         http://localhost:3100"
    echo ""
    
    echo "🐳 Container Status:"
    docker-compose -f docker-compose.production.yml ps
    
    echo ""
    echo "📋 Management Commands:"
    echo "  # View logs"
    echo "  docker-compose -f docker-compose.production.yml logs -f"
    echo ""
    echo "  # Scale services"
    echo "  docker-compose -f docker-compose.production.yml up -d --scale api-gateway=2"
    echo ""
    echo "  # Start trading services"
    echo "  docker-compose -f docker-compose.production.yml --profile fyers-dryrun up -d"
    echo ""
    echo "  # Run enhanced backtest"
    echo "  STRATEGY=MyStrategy CONFIG=my-config SYMBOLS=RELIANCE,TCS \\"
    echo "  docker-compose -f docker-compose.production.yml --profile enhanced-backtest up"
    echo ""
    
    echo "🔧 Monitoring:"
    echo "  • Logs: Grafana dashboard at http://localhost/grafana"
    echo "  • Metrics: API Gateway provides real-time metrics"
    echo "  • Health: http://localhost/health"
    echo ""
    
    echo "🚀 Next Steps:"
    echo "  1. Configure your trading strategies"
    echo "  2. Set up broker credentials"
    echo "  3. Start trading services"
    echo "  4. Monitor via Grafana dashboard"
}

# Function to cleanup on exit
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        print_warning "Deployment interrupted. Cleaning up..."
        cd "$PROJECT_DIR"
        docker-compose -f docker-compose.production.yml down --remove-orphans || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    print_header "RapidTrader Production Deployment"
    
    check_prerequisites
    create_directories
    setup_grafana
    build_alpine_images
    deploy_production
    show_deployment_info
    
    print_success "Production deployment completed successfully!"
}

# Parse command line arguments
case "${1:-deploy}" in
    "build")
        check_prerequisites
        build_alpine_images
        ;;
    "deploy")
        main
        ;;
    "health")
        check_service_health
        ;;
    "info")
        show_deployment_info
        ;;
    "clean")
        print_info "Cleaning up production deployment..."
        cd "$PROJECT_DIR"
        docker-compose -f docker-compose.production.yml down --remove-orphans --volumes
        docker system prune -f
        print_success "Cleanup completed"
        ;;
    *)
        echo "Usage: $0 [build|deploy|health|info|clean]"
        echo ""
        echo "Commands:"
        echo "  build   - Build Alpine-based Docker images"
        echo "  deploy  - Deploy full production stack (default)"
        echo "  health  - Check service health"
        echo "  info    - Show deployment information"
        echo "  clean   - Clean up deployment"
        exit 1
        ;;
esac
