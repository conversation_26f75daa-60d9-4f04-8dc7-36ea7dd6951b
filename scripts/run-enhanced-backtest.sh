#!/bin/bash
#
# Enhanced Backtest Runner Script for RapidTrader
#
# This script provides a convenient way to run enhanced backtests with:
# - Dynamic container naming based on symbol, config, and strategy
# - Automatic data downloading and updating
# - Result storage and display
# - Auto-stop containers after completion
#
# Usage:
#   ./scripts/run-enhanced-backtest.sh --strategy MyStrategy --config my-config --symbols RELIANCE,TCS --timeframe 1h
#

set -e  # Exit on any error

# Default values
STRATEGY="DefaultStrategy"
CONFIG="backtest-config"
SYMBOLS="RELIANCE,TCS"
TIMEFRAME="1d"
TIMERANGE=""
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Function to show usage
show_usage() {
    cat << EOF
Enhanced Backtest Runner for RapidTrader

Usage: $0 [OPTIONS]

Options:
  -s, --strategy STRATEGY    Strategy name (default: DefaultStrategy)
  -c, --config CONFIG        Config file name without .json (default: backtest-config)
  -y, --symbols SYMBOLS      Comma-separated symbols (default: RELIANCE,TCS)
  -t, --timeframe TIMEFRAME  Data timeframe (default: 1d)
  -r, --timerange TIMERANGE  Time range YYYYMMDD-YYYYMMDD (optional)
  -h, --help                 Show this help message

Examples:
  # Basic backtest
  $0 --strategy MyStrategy --config my-config

  # With specific symbols and timeframe
  $0 --strategy AggressiveMA --symbols RELIANCE,TCS,HDFCBANK --timeframe 1h

  # With time range
  $0 --strategy MyStrategy --timerange ********-********

  # Full example
  $0 -s AggressiveMA -c fyers-config -y RELIANCE,TCS,INFY -t 1h -r ********-********

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--strategy)
            STRATEGY="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG="$2"
            shift 2
            ;;
        -y|--symbols)
            SYMBOLS="$2"
            shift 2
            ;;
        -t|--timeframe)
            TIMEFRAME="$2"
            shift 2
            ;;
        -r|--timerange)
            TIMERANGE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Function to generate container name
generate_container_name() {
    local symbols_clean=$(echo "$SYMBOLS" | tr ',' '-' | tr '[:upper:]' '[:lower:]')
    local config_clean=$(echo "$CONFIG" | tr '_' '-' | tr '[:upper:]' '[:lower:]')
    local strategy_clean=$(echo "$STRATEGY" | tr '_' '-' | tr '[:upper:]' '[:lower:]')
    local timestamp=$(date +"%Y%m%d-%H%M%S")

    # Limit symbols to first 3 for container name
    local symbol_array=(${symbols_clean//-/ })
    local symbol_part=""
    for i in "${symbol_array[@]:0:3}"; do
        if [[ -z "$symbol_part" ]]; then
            symbol_part="$i"
        else
            symbol_part="$symbol_part-$i"
        fi
    done

    if [[ ${#symbol_array[@]} -gt 3 ]]; then
        symbol_part="$symbol_part-plus$((${#symbol_array[@]} - 3))"
    fi

    echo "backtest-$symbol_part-$config_clean-$strategy_clean-$timestamp"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        print_error "docker-compose is not installed or not in PATH."
        exit 1
    fi

    # Check if RapidTrader image exists
    if ! docker image inspect rapidtrader:latest >/dev/null 2>&1; then
        print_warning "RapidTrader Docker image not found. Building..."
        cd "$PROJECT_DIR"
        docker-compose build rapidtrader-base
    fi

    print_success "Prerequisites check passed"
}

# Function to validate inputs
validate_inputs() {
    print_info "Validating inputs..."

    # Check if config file exists
    local config_file="$PROJECT_DIR/userdata/config/$CONFIG.json"
    if [[ ! -f "$config_file" ]]; then
        config_file="$PROJECT_DIR/userdata/configs/$CONFIG.json"
        if [[ ! -f "$config_file" ]]; then
            print_error "Config file not found: $CONFIG.json"
            print_info "Available configs:"
            ls -1 "$PROJECT_DIR/userdata/config/"*.json 2>/dev/null | xargs -I {} basename {} .json || true
            ls -1 "$PROJECT_DIR/userdata/configs/"*.json 2>/dev/null | xargs -I {} basename {} .json || true
            exit 1
        fi
    fi

    # Validate symbols format
    if [[ ! "$SYMBOLS" =~ ^[A-Za-z0-9,]+$ ]]; then
        print_error "Invalid symbols format. Use comma-separated symbols like: RELIANCE,TCS,INFY"
        exit 1
    fi

    # Validate timeframe
    if [[ ! "$TIMEFRAME" =~ ^[0-9]+[mhd]$ ]]; then
        print_error "Invalid timeframe format. Use formats like: 1m, 5m, 1h, 1d"
        exit 1
    fi

    # Validate timerange if provided
    if [[ -n "$TIMERANGE" && ! "$TIMERANGE" =~ ^[0-9]{8}-[0-9]{8}$ ]]; then
        print_error "Invalid timerange format. Use YYYYMMDD-YYYYMMDD format like: ********-********"
        exit 1
    fi

    print_success "Input validation passed"
}

# Function to run enhanced backtest
run_enhanced_backtest() {
    local container_name=$(generate_container_name)

    print_header "Enhanced Backtest Configuration"
    echo "Strategy: $STRATEGY"
    echo "Config: $CONFIG"
    echo "Symbols: $SYMBOLS"
    echo "Timeframe: $TIMEFRAME"
    [[ -n "$TIMERANGE" ]] && echo "Timerange: $TIMERANGE"
    echo "Container: $container_name"
    echo "=" * 60

    print_info "Starting enhanced backtest..."

    # Set environment variables for docker-compose
    export BACKTEST_CONTAINER_NAME="$container_name"
    export STRATEGY="$STRATEGY"
    export CONFIG="$CONFIG"
    export SYMBOLS="$SYMBOLS"
    export TIMEFRAME="$TIMEFRAME"
    export TIMERANGE="$TIMERANGE"

    # Change to project directory
    cd "$PROJECT_DIR"

    # Run the enhanced backtest
    print_info "Deploying container: $container_name"

    if docker-compose --profile enhanced-backtest up --remove-orphans; then
        print_success "Enhanced backtest completed successfully!"

        # Show results location
        local results_dir="$PROJECT_DIR/userdata/results"
        if [[ -d "$results_dir" ]]; then
            print_info "Results stored in: $results_dir"

            # Show latest result file
            local latest_result=$(find "$results_dir" -name "*.json" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
            if [[ -n "$latest_result" ]]; then
                print_success "Latest result: $latest_result"
            fi
        fi

        print_success "Container $container_name has been automatically stopped and removed"

    else
        print_error "Enhanced backtest failed!"
        exit 1
    fi
}

# Function to cleanup on exit
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        print_warning "Script interrupted. Cleaning up..."

        # Stop any running containers with our naming pattern
        local containers=$(docker ps -q --filter "name=backtest-*" 2>/dev/null || true)
        if [[ -n "$containers" ]]; then
            print_info "Stopping backtest containers..."
            docker stop $containers >/dev/null 2>&1 || true
        fi
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    print_header "RapidTrader Enhanced Backtest Runner"

    check_prerequisites
    validate_inputs
    run_enhanced_backtest

    print_success "Enhanced backtest process completed!"
}

# Run main function
main "$@"
