#!/usr/bin/env python3
"""
Enhanced Backtest Runner for RapidTrader

This script provides enhanced backtesting functionality with:
- Automatic Docker container deployment
- Dynamic container naming based on symbol, config, and strategy
- Automatic data downloading and updating
- Result storage and display
- Auto-stop containers after completion
"""

import os
import sys
import json
import time
import docker
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class EnhancedBacktestRunner:
    """Enhanced backtest runner with Docker integration"""
    
    def __init__(self):
        """Initialize the enhanced backtest runner"""
        self.docker_client = docker.from_env()
        self.project_root = project_root
        self.userdata_dir = self.project_root / "userdata"
        self.results_dir = self.userdata_dir / "results" / "backtests"
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
    def run_enhanced_backtest(self, 
                            strategy: str,
                            config: str, 
                            symbols: Optional[List[str]] = None,
                            timeframe: str = "1d",
                            timerange: Optional[str] = None,
                            auto_update_data: bool = True) -> Dict[str, Any]:
        """
        Run enhanced backtest with automatic container management
        
        Args:
            strategy: Strategy name
            config: Configuration file name (without .json)
            symbols: List of symbols (if None, read from config)
            timeframe: Data timeframe
            timerange: Time range for backtest
            auto_update_data: Whether to auto-update data
            
        Returns:
            Dictionary with backtest results and metadata
        """
        print(f"🚀 Starting Enhanced Backtest")
        print(f"Strategy: {strategy}")
        print(f"Config: {config}")
        print("=" * 60)
        
        # Load configuration
        config_path = self._get_config_path(config)
        config_data = self._load_config(config_path)
        
        # Get symbols from config if not provided
        if not symbols:
            symbols = config_data.get('exchange', {}).get('pair_whitelist', [])
            if not symbols:
                raise ValueError("No symbols specified in config or parameters")
        
        # Create unique container name
        container_name = self._generate_container_name(symbols, config, strategy)
        print(f"📦 Container: {container_name}")
        
        # Ensure data is available and updated
        if auto_update_data:
            self._ensure_data_updated(symbols, timeframe, timerange)
        
        # Run backtest in container
        results = self._run_backtest_container(
            container_name=container_name,
            strategy=strategy,
            config=config,
            symbols=symbols,
            timeframe=timeframe,
            timerange=timerange
        )
        
        # Store results
        result_file = self._store_results(results, container_name, strategy, symbols)
        
        # Display results
        self._display_results(results, result_file)
        
        return {
            "results": results,
            "container_name": container_name,
            "result_file": result_file,
            "symbols": symbols,
            "strategy": strategy,
            "config": config
        }
    
    def _get_config_path(self, config: str) -> Path:
        """Get full path to config file"""
        if not config.endswith('.json'):
            config += '.json'
        
        # Try different config locations
        possible_paths = [
            self.userdata_dir / "config" / config,
            self.userdata_dir / "configs" / config,
            Path(config)  # Absolute path
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        
        raise FileNotFoundError(f"Config file not found: {config}")
    
    def _load_config(self, config_path: Path) -> Dict[str, Any]:
        """Load configuration from file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            raise ValueError(f"Failed to load config {config_path}: {e}")
    
    def _generate_container_name(self, symbols: List[str], config: str, strategy: str) -> str:
        """Generate unique container name based on parameters"""
        # Clean names for container naming
        symbol_str = "-".join(symbols[:3])  # Limit to first 3 symbols
        if len(symbols) > 3:
            symbol_str += f"-plus{len(symbols)-3}"
        
        config_clean = config.replace('.json', '').replace('_', '-')
        strategy_clean = strategy.replace('_', '-')
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        
        container_name = f"backtest-{symbol_str}-{config_clean}-{strategy_clean}-{timestamp}"
        
        # Docker container names have length limits and character restrictions
        container_name = container_name.lower()
        container_name = ''.join(c if c.isalnum() or c in '-_' else '-' for c in container_name)
        
        # Limit length
        if len(container_name) > 60:
            container_name = container_name[:60].rstrip('-')
        
        return container_name
    
    def _ensure_data_updated(self, symbols: List[str], timeframe: str, timerange: Optional[str]):
        """Ensure data is downloaded and updated for symbols"""
        print(f"📊 Ensuring data is available for {len(symbols)} symbols...")
        
        for symbol in symbols:
            print(f"  📈 Checking data for {symbol}...")
            
            # Use RapidTrader's data download functionality
            try:
                cmd = [
                    "python", "-m", "core.rapidtrader", "data", "download",
                    "--symbol", symbol,
                    "--timeframe", timeframe,
                    "--update"  # Update existing data
                ]
                
                if timerange:
                    cmd.extend(["--timerange", timerange])
                
                result = subprocess.run(cmd, cwd=self.project_root, 
                                      capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"    ✅ Data ready for {symbol}")
                else:
                    print(f"    ⚠️  Data download warning for {symbol}: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"    ⚠️  Data download timeout for {symbol}")
            except Exception as e:
                print(f"    ❌ Data download error for {symbol}: {e}")
        
        print("📊 Data preparation complete")
    
    def _run_backtest_container(self, container_name: str, strategy: str, config: str,
                               symbols: List[str], timeframe: str, 
                               timerange: Optional[str]) -> Dict[str, Any]:
        """Run backtest in Docker container"""
        print(f"🐳 Starting backtest container: {container_name}")
        
        # Prepare environment variables
        env_vars = {
            "STRATEGY": strategy,
            "CONFIG_FILE": f"/rapidtrader/userdata/config/{config}.json",
            "SYMBOLS": ",".join(symbols),
            "TIMEFRAME": timeframe,
        }
        
        if timerange:
            env_vars["TIMERANGE"] = timerange
        
        # Prepare volumes
        volumes = {
            str(self.userdata_dir): {"bind": "/rapidtrader/userdata", "mode": "rw"}
        }
        
        # Build command
        cmd = ["python", "-m", "core.rapidtrader", "backtest", "run", 
               "-c", config, "-s", strategy]
        
        if timerange:
            cmd.extend(["--timerange", timerange])
        
        if timeframe != "1d":
            cmd.extend(["-t", timeframe])
        
        try:
            # Run container
            print(f"🏃 Executing: {' '.join(cmd)}")
            
            container = self.docker_client.containers.run(
                image="rapidtrader:latest",
                command=cmd,
                name=container_name,
                environment=env_vars,
                volumes=volumes,
                detach=True,
                remove=True,  # Auto-remove when stopped
                network="rapidtrader_rapidtrader-network"
            )
            
            print(f"📦 Container {container_name} started")
            
            # Wait for completion and stream logs
            self._monitor_container(container)
            
            # Container auto-removes, so we need to get results from files
            return self._get_results_from_files(strategy, symbols)
            
        except docker.errors.ImageNotFound:
            print("❌ RapidTrader Docker image not found. Please build it first:")
            print("   docker-compose build")
            raise
        except Exception as e:
            print(f"❌ Container execution failed: {e}")
            raise
    
    def _monitor_container(self, container):
        """Monitor container execution and stream logs"""
        print("📋 Monitoring backtest execution...")
        
        try:
            # Stream logs in real-time
            for log in container.logs(stream=True, follow=True):
                print(log.decode('utf-8').strip())
            
            # Wait for container to finish
            result = container.wait()
            exit_code = result['StatusCode']
            
            if exit_code == 0:
                print("✅ Backtest completed successfully")
            else:
                print(f"❌ Backtest failed with exit code: {exit_code}")
                
        except Exception as e:
            print(f"⚠️  Error monitoring container: {e}")
    
    def _get_results_from_files(self, strategy: str, symbols: List[str]) -> Dict[str, Any]:
        """Get backtest results from result files"""
        # Look for recent result files
        result_files = list(self.results_dir.glob("*.json"))
        
        if not result_files:
            return {"error": "No result files found"}
        
        # Get the most recent result file
        latest_file = max(result_files, key=lambda f: f.stat().st_mtime)
        
        try:
            with open(latest_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            return {"error": f"Failed to load results: {e}"}
    
    def _store_results(self, results: Dict[str, Any], container_name: str, 
                      strategy: str, symbols: List[str]) -> Path:
        """Store backtest results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"backtest_{strategy}_{'-'.join(symbols[:2])}_{timestamp}.json"
        result_file = self.results_dir / filename
        
        # Add metadata
        enhanced_results = {
            "metadata": {
                "container_name": container_name,
                "strategy": strategy,
                "symbols": symbols,
                "timestamp": timestamp,
                "backtest_date": datetime.now().isoformat()
            },
            "results": results
        }
        
        try:
            with open(result_file, 'w') as f:
                json.dump(enhanced_results, f, indent=2, default=str)
            
            print(f"💾 Results saved to: {result_file}")
            return result_file
            
        except Exception as e:
            print(f"❌ Failed to save results: {e}")
            return None
    
    def _display_results(self, results: Dict[str, Any], result_file: Optional[Path]):
        """Display backtest results"""
        print("\n" + "=" * 60)
        print("📊 BACKTEST RESULTS")
        print("=" * 60)
        
        if "error" in results:
            print(f"❌ Error: {results['error']}")
            return
        
        # Display key metrics
        if isinstance(results, dict) and "metrics" in results:
            metrics = results["metrics"]
            
            print(f"💰 Total Return: {metrics.get('total_return_pct', 0):.2f}%")
            print(f"📈 Total Trades: {metrics.get('total_trades', 0)}")
            print(f"🎯 Win Rate: {metrics.get('win_rate', 0):.2f}%")
            print(f"💵 Profit/Loss: ${metrics.get('total_profit_loss', 0):.2f}")
            print(f"📉 Max Drawdown: {metrics.get('max_drawdown_pct', 0):.2f}%")
        
        if result_file:
            print(f"\n📁 Full results: {result_file}")
        
        print("=" * 60)


def main():
    """Main function for command line usage"""
    parser = argparse.ArgumentParser(description='Enhanced Backtest Runner')
    parser.add_argument('--strategy', '-s', required=True, help='Strategy name')
    parser.add_argument('--config', '-c', required=True, help='Config file name')
    parser.add_argument('--symbols', nargs='+', help='Symbols to backtest')
    parser.add_argument('--timeframe', '-t', default='1d', help='Data timeframe')
    parser.add_argument('--timerange', help='Time range (YYYYMMDD-YYYYMMDD)')
    parser.add_argument('--no-update', action='store_true', help='Skip data update')
    
    args = parser.parse_args()
    
    try:
        runner = EnhancedBacktestRunner()
        result = runner.run_enhanced_backtest(
            strategy=args.strategy,
            config=args.config,
            symbols=args.symbols,
            timeframe=args.timeframe,
            timerange=args.timerange,
            auto_update_data=not args.no_update
        )
        
        print(f"\n🎉 Enhanced backtest completed successfully!")
        print(f"Container: {result['container_name']}")
        
    except Exception as e:
        print(f"❌ Enhanced backtest failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
