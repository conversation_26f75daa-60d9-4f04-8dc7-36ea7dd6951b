#!/bin/bash

# RapidTrader Docker Control Script
# This script provides easy Docker management for RapidTrader with Fyers integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
PROFILE=""
DETACH=false
BUILD=false
LOGS=false
FOLLOW_LOGS=false

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to print usage
print_usage() {
    cat << EOF
RapidTrader Docker Control Script

Usage: $0 [COMMAND] [OPTIONS]

COMMANDS:
    web                     Start web interface only
    fyers-dryrun           Start Fyers dry-run trading
    fyers-live             Start Fyers live trading
    fyers-web-trading      Start Fyers trading with web interface
    backtest               Run backtesting
    shell                  Start interactive shell
    stop                   Stop all services
    logs                   Show logs
    status                 Show container status
    build                  Build Docker images
    clean                  Clean up containers and images

FYERS SPECIFIC COMMANDS:
    fyers-setup            Setup Fyers credentials and generate access token
    fyers-test             Test Fyers connection
    fyers-profile          Show Fyers profile information
    fyers-websocket        Test Fyers WebSocket connection

OPTIONS:
    -d, --detach           Run in detached mode
    -b, --build            Build images before starting
    -f, --follow           Follow logs (for logs command)
    -h, --help             Show this help message

EXAMPLES:
    # Start Fyers dry-run trading with web interface
    $0 fyers-web-trading

    # Start web interface only
    $0 web -d

    # Test Fyers connection
    $0 fyers-test

    # Setup Fyers credentials
    $0 fyers-setup

    # View logs
    $0 logs -f

    # Stop all services
    $0 stop

FREQTRADE COMPARISON:
    This script provides similar functionality to FreqTrade's docker commands:
    
    FreqTrade:                  RapidTrader:
    freqtrade trade             $0 fyers-dryrun
    freqtrade trade --live      $0 fyers-live
    freqtrade backtesting       $0 backtest
    freqtrade plot-dataframe    $0 web (includes charts)
    freqtrade webserver         $0 web

EOF
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_color $RED "Error: Docker is not running or not accessible"
        exit 1
    fi
}

# Function to check if .env file exists
check_env_file() {
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        print_color $YELLOW "Warning: .env file not found"
        print_color $YELLOW "Creating a template .env file..."
        
        cat > "$PROJECT_ROOT/.env" << 'EOF'
# Fyers API Credentials
FYERS_CLIENT_ID=your_client_id_here
FYERS_SECRET_KEY=your_secret_key_here
FYERS_REDIRECT_URI=https://127.0.0.1/
FYERS_FY_ID=your_fy_id_here
FYERS_TOTP_KEY=your_totp_key_here
FYERS_PIN=your_pin_here
FYERS_AUTH_CODE=your_auth_code_here
FYERS_ACCESS_TOKEN=
FYERS_REFRESH_TOKEN=

# Trading Configuration
DRY_RUN_ENABLED=true
LIVE_DATA_ENABLED=true

# Logging
LOG_LEVEL=INFO
EOF
        
        print_color $GREEN "Template .env file created. Please update it with your credentials."
        print_color $CYAN "Run '$0 fyers-setup' to configure Fyers credentials."
    fi
}

# Function to build Docker images
build_images() {
    print_color $BLUE "Building RapidTrader Docker images..."
    cd "$PROJECT_ROOT"
    docker-compose build
    print_color $GREEN "Docker images built successfully"
}

# Function to start services
start_service() {
    local profile=$1
    local detach_flag=""
    
    if [[ "$DETACH" == "true" ]]; then
        detach_flag="-d"
    fi
    
    if [[ "$BUILD" == "true" ]]; then
        build_images
    fi
    
    print_color $BLUE "Starting RapidTrader with profile: $profile"
    cd "$PROJECT_ROOT"
    docker-compose --profile "$profile" up $detach_flag
}

# Function to stop services
stop_services() {
    print_color $BLUE "Stopping all RapidTrader services..."
    cd "$PROJECT_ROOT"
    docker-compose down
    print_color $GREEN "All services stopped"
}

# Function to show logs
show_logs() {
    local follow_flag=""
    
    if [[ "$FOLLOW_LOGS" == "true" ]]; then
        follow_flag="-f"
    fi
    
    cd "$PROJECT_ROOT"
    docker-compose logs $follow_flag
}

# Function to show status
show_status() {
    print_color $BLUE "RapidTrader Container Status:"
    cd "$PROJECT_ROOT"
    docker-compose ps
    
    print_color $BLUE "\nDocker Images:"
    docker images | grep -E "(rapidtrader|REPOSITORY)"
    
    print_color $BLUE "\nDocker Networks:"
    docker network ls | grep -E "(rapidtrader|NETWORK)"
}

# Function to clean up
cleanup() {
    print_color $BLUE "Cleaning up RapidTrader containers and images..."
    cd "$PROJECT_ROOT"
    
    # Stop and remove containers
    docker-compose down --remove-orphans
    
    # Remove images
    docker images | grep rapidtrader | awk '{print $3}' | xargs -r docker rmi -f
    
    # Remove unused volumes
    docker volume prune -f
    
    print_color $GREEN "Cleanup completed"
}

# Function to setup Fyers credentials
setup_fyers() {
    print_color $BLUE "Setting up Fyers credentials..."
    
    if [[ ! -f "$PROJECT_ROOT/.env" ]]; then
        check_env_file
    fi
    
    print_color $CYAN "Please ensure your .env file has the correct Fyers credentials."
    print_color $CYAN "Then run the authentication script to generate access token:"
    
    cd "$PROJECT_ROOT"
    if [[ "$BUILD" == "true" ]]; then
        build_images
    fi
    
    docker-compose run --rm shell python scripts/fyers_auth.py --generate-token
}

# Function to test Fyers connection
test_fyers() {
    print_color $BLUE "Testing Fyers connection..."
    cd "$PROJECT_ROOT"
    
    if [[ "$BUILD" == "true" ]]; then
        build_images
    fi
    
    docker-compose run --rm shell ./rapidtrader profile test --broker fyers
}

# Function to show Fyers profile
show_fyers_profile() {
    print_color $BLUE "Fetching Fyers profile information..."
    cd "$PROJECT_ROOT"
    
    if [[ "$BUILD" == "true" ]]; then
        build_images
    fi
    
    docker-compose run --rm shell ./rapidtrader profile show --broker fyers
}

# Function to test Fyers WebSocket
test_fyers_websocket() {
    print_color $BLUE "Testing Fyers WebSocket connection..."
    cd "$PROJECT_ROOT"
    
    if [[ "$BUILD" == "true" ]]; then
        build_images
    fi
    
    docker-compose run --rm shell python test_fyers_websocket.py
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        web)
            PROFILE="web"
            shift
            ;;
        fyers-dryrun)
            PROFILE="fyers-dryrun"
            shift
            ;;
        fyers-live)
            PROFILE="fyers-live"
            shift
            ;;
        fyers-web-trading)
            PROFILE="fyers-web-trading"
            shift
            ;;
        backtest)
            PROFILE="backtest"
            shift
            ;;
        shell)
            PROFILE="shell"
            shift
            ;;
        stop)
            stop_services
            exit 0
            ;;
        logs)
            LOGS=true
            shift
            ;;
        status)
            show_status
            exit 0
            ;;
        build)
            build_images
            exit 0
            ;;
        clean)
            cleanup
            exit 0
            ;;
        fyers-setup)
            setup_fyers
            exit 0
            ;;
        fyers-test)
            test_fyers
            exit 0
            ;;
        fyers-profile)
            show_fyers_profile
            exit 0
            ;;
        fyers-websocket)
            test_fyers_websocket
            exit 0
            ;;
        -d|--detach)
            DETACH=true
            shift
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -f|--follow)
            FOLLOW_LOGS=true
            shift
            ;;
        -h|--help)
            print_usage
            exit 0
            ;;
        *)
            print_color $RED "Unknown option: $1"
            print_usage
            exit 1
            ;;
    esac
done

# Main execution
check_docker
check_env_file

if [[ "$LOGS" == "true" ]]; then
    show_logs
elif [[ -n "$PROFILE" ]]; then
    start_service "$PROFILE"
else
    print_color $YELLOW "No command specified. Showing help:"
    print_usage
fi
