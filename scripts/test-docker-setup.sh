#!/bin/bash
#
# Test script for RapidTrader Docker setup
# This script validates the Docker configuration and setup
#

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -n "Testing $test_name... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        print_success "PASSED"
        ((TESTS_PASSED++))
        return 0
    else
        print_error "FAILED"
        ((TESTS_FAILED++))
        return 1
    fi
}

print_header "RapidTrader Docker Setup Validation"

# Test 1: Check if Docker files exist
print_header "File Structure Tests"

run_test "Dockerfile exists" "[ -f Dockerfile ]"
run_test "docker-compose.yml exists" "[ -f docker-compose.yml ]"
run_test "docker-compose.override.yml exists" "[ -f docker-compose.override.yml ]"
run_test "entrypoint.sh exists" "[ -f scripts/entrypoint.sh ]"
run_test "docker-run.sh exists" "[ -f scripts/docker-run.sh ]"
run_test "entrypoint.sh is executable" "[ -x scripts/entrypoint.sh ]"
run_test "docker-run.sh is executable" "[ -x scripts/docker-run.sh ]"

# Test 2: Check configuration files
print_header "Configuration Files Tests"

run_test "dry_run_config.json exists" "[ -f userdata/config/dry_run_config.json ]"
run_test "dryrun-config.json exists" "[ -f userdata/config/dryrun-config.json ]"
run_test "backtest-config.json exists" "[ -f userdata/config/backtest-config.json ]"
run_test "live-config.json exists" "[ -f userdata/config/live-config.json ]"
run_test "optimize-config.json exists" "[ -f userdata/config/optimize-config.json ]"

# Test 3: Validate JSON configuration files
print_header "JSON Validation Tests"

for config_file in userdata/config/*.json; do
    if [ -f "$config_file" ]; then
        filename=$(basename "$config_file")
        run_test "$filename is valid JSON" "python -m json.tool '$config_file'"
    fi
done

# Test 4: Check required directories
print_header "Directory Structure Tests"

run_test "userdata directory exists" "[ -d userdata ]"
run_test "userdata/config directory exists" "[ -d userdata/config ]"
run_test "userdata/strategies directory exists" "[ -d userdata/strategies ]"
run_test "scripts directory exists" "[ -d scripts ]"
run_test "core directory exists" "[ -d core ]"
run_test "broker directory exists" "[ -d broker ]"

# Test 5: Check Python requirements
print_header "Python Dependencies Tests"

run_test "requirements.txt exists" "[ -f requirements.txt ]"
run_test "requirements.txt is not empty" "[ -s requirements.txt ]"

# Test 6: Check environment file
print_header "Environment Configuration Tests"

run_test ".env.example exists" "[ -f .env.example ]"

if [ -f .env ]; then
    print_success ".env file exists"
    ((TESTS_PASSED++))
else
    print_warning ".env file not found (copy from .env.example)"
fi

# Test 7: Docker Compose validation (if docker-compose is available)
print_header "Docker Compose Tests"

if command -v docker-compose >/dev/null 2>&1; then
    run_test "docker-compose.yml is valid" "docker-compose config >/dev/null"
    run_test "all services defined" "docker-compose config --services | grep -q paper-trade"
else
    print_warning "docker-compose not available, skipping validation"
fi

# Test 8: Check core Python modules
print_header "Core Module Tests"

run_test "core.rapidtrader module exists" "[ -f core/rapidtrader.py ]"
run_test "core.independent_dry_run module exists" "[ -f core/independent_dry_run.py ]"
run_test "broker.dhan_wrapper module exists" "[ -f broker/dhan_wrapper.py ]"

# Test 9: Check strategy files
print_header "Strategy Tests"

run_test "base strategy exists" "[ -f userdata/strategies/base_strategy.py ]"
run_test "unified strategy template exists" "[ -f userdata/strategies/UnifiedStrategyTemplate.py ]"

# Test 10: Documentation
print_header "Documentation Tests"

run_test "README.md exists" "[ -f README.md ]"
run_test "Docker quickstart guide exists" "[ -f docs/DOCKER_QUICKSTART.md ]"

# Summary
print_header "Test Results Summary"

TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
SUCCESS_RATE=$((TESTS_PASSED * 100 / TOTAL_TESTS))

echo "Tests Passed: $TESTS_PASSED/$TOTAL_TESTS"
echo "Success Rate: $SUCCESS_RATE%"

if [ $TESTS_FAILED -eq 0 ]; then
    print_success "All tests passed! Docker setup is ready."
    echo ""
    print_info "Next steps:"
    echo "1. Copy .env.example to .env and configure your API credentials"
    echo "2. Build the Docker image: ./scripts/docker-run.sh build"
    echo "3. Start paper trading: ./scripts/docker-run.sh paper-trade"
    echo ""
    exit 0
else
    print_error "$TESTS_FAILED tests failed. Please fix the issues above."
    echo ""
    print_info "Common fixes:"
    echo "1. Make sure all required files are present"
    echo "2. Check JSON syntax in configuration files"
    echo "3. Ensure scripts have execute permissions"
    echo ""
    exit 1
fi
