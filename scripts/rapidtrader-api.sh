#!/bin/bash

# RapidTrader API Management Script
# Inspired by OpenAlgo's architecture for unified API access

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
API_URL="http://localhost:8000"
COMPOSE_FILE="docker-compose.api.yml"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
RapidTrader Unified API Management Script v2.0
Inspired by OpenAlgo & FreqTrade, Built for RapidTrader

Usage: $0 <command> [options]

CORE COMMANDS:
  start                    - Start the Unified API Gateway
  stop                     - Stop the API Gateway
  restart                  - Restart the API Gateway
  status                   - Show API Gateway status
  logs                     - Show API Gateway logs

SYMBOL MANAGEMENT (RapidTrader Standard Symbols):
  search-symbol <symbol>   - Search for symbol across brokers
  list-symbols [exchange]  - List available symbols
  add-symbol <symbol>      - Add new symbol mapping

BROKER MANAGEMENT:
  add-fyers               - Add Fyers broker configuration
  add-dhan                - Add Dhan broker configuration
  list-brokers           - List configured brokers

TRADING OPERATIONS (Using RapidTrader Symbols):
  place-order <symbol>    - Place order using standard symbol
  get-positions          - Get current positions
  get-quotes <symbol>    - Get market quotes

CONTAINER MANAGEMENT:
  start-dryrun <symbol>  - Start dry run for symbol
  start-backtest <symbol> - Start backtest for symbol
  list-containers        - List trading containers

UTILITIES:
  build                  - Build Docker images
  clean                  - Clean up containers and images
  setup                  - Initial setup and configuration
  health                 - Check API Gateway health
  demo                   - Run demo with sample data

Examples:
  $0 start                           # Start Unified API
  $0 search-symbol SBIN              # Search for SBIN symbol
  $0 add-fyers                       # Add Fyers broker
  $0 place-order RELIANCE            # Place order for RELIANCE

Note: Uses RapidTrader's unified symbol system (e.g., SBIN, RELIANCE)
      Automatically maps to broker-specific formats (Fyers symbols, Dhan IDs)

EOF
}

check_dependencies() {
    log_info "Checking dependencies..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi

    if ! command -v curl &> /dev/null; then
        log_error "curl is not installed"
        exit 1
    fi

    log_success "Dependencies check passed"
}

wait_for_api() {
    log_info "Waiting for API Gateway to be ready..."

    for i in {1..30}; do
        if curl -s "$API_URL/health" > /dev/null 2>&1; then
            log_success "API Gateway is ready"
            return 0
        fi
        sleep 2
    done

    log_error "API Gateway failed to start"
    return 1
}

start_services() {
    log_info "Starting RapidTrader Unified Platform..."

    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" up -d

    if wait_for_api; then
        log_success "RapidTrader Platform started successfully"
        echo ""
        log_info "🌐 Frontend: http://localhost:3000"
        log_info "🔧 API: $API_URL"
        log_info "📚 API Documentation: $API_URL/docs"
        log_info "🔍 Health Check: $API_URL/health"
        echo ""
        log_info "🔑 Demo API Key: rt_demo_key_12345"
        log_info "Use this key in the frontend or for API testing"
        echo ""
        log_success "Open http://localhost:3000 in your browser to start trading!"
    else
        log_error "Failed to start RapidTrader Platform"
        exit 1
    fi
}

stop_services() {
    log_info "Stopping RapidTrader services..."

    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" down

    log_success "Services stopped"
}

restart_services() {
    log_info "Restarting RapidTrader API Gateway..."
    stop_services
    sleep 2
    start_services
}

show_status() {
    log_info "RapidTrader Service Status:"

    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" ps

    echo ""
    log_info "API Gateway Health:"
    if curl -s "$API_URL/health" | jq . 2>/dev/null; then
        log_success "API Gateway is healthy"
    else
        log_warning "API Gateway is not responding"
    fi
}

show_logs() {
    log_info "Showing API Gateway logs..."

    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" logs -f rapidtrader-api
}

search_symbol() {
    local symbol="$1"
    local exchange="${2:-NSE_EQ}"

    if [ -z "$symbol" ]; then
        log_error "Symbol is required"
        echo "Usage: $0 search-symbol <symbol> [exchange]"
        exit 1
    fi

    log_info "Searching for symbol: $symbol on $exchange"

    response=$(curl -s -H "Authorization: Bearer rt_demo_key_12345" \
        "$API_URL/symbols/search/$symbol?exchange=$exchange")

    if echo "$response" | jq -e '.rapidtrader_symbol' > /dev/null 2>&1; then
        log_success "Symbol found!"
        echo "$response" | jq .
    else
        log_error "Symbol not found or API error"
        echo "$response"
    fi
}

list_symbols() {
    local exchange="${1:-NSE_EQ}"
    local limit="${2:-50}"

    log_info "Listing symbols for $exchange (limit: $limit)"

    response=$(curl -s -H "Authorization: Bearer rt_demo_key_12345" \
        "$API_URL/symbols/list?exchange=$exchange&limit=$limit")

    if echo "$response" | jq -e '.symbols' > /dev/null 2>&1; then
        log_success "Available symbols:"
        echo "$response" | jq -r '.symbols[]' | head -20
        total=$(echo "$response" | jq -r '.total_available')
        echo ""
        log_info "Total available: $total symbols"
    else
        log_error "Failed to list symbols"
        echo "$response"
    fi
}

add_fyers_broker() {
    log_info "Adding Fyers broker configuration..."

    read -p "Enter Fyers Client ID: " client_id
    read -p "Enter Fyers Access Token: " access_token
    read -p "Enter Fyers Refresh Token (optional): " refresh_token
    read -p "Dry run mode? (y/n) [y]: " dry_run

    dry_run=${dry_run:-y}
    dry_run_bool=$([ "$dry_run" = "y" ] && echo "true" || echo "false")

    payload=$(cat << EOF
{
    "broker_name": "fyers",
    "credentials": {
        "client_id": "$client_id",
        "access_token": "$access_token",
        "refresh_token": "$refresh_token"
    },
    "dry_run": $dry_run_bool,
    "live_data": true
}
EOF
)

    response=$(curl -s -X POST "$API_URL/brokers/add" \
        -H "Authorization: Bearer rt_demo_key_12345" \
        -H "Content-Type: application/json" \
        -d "$payload")

    if echo "$response" | jq -e '.broker_id' > /dev/null 2>&1; then
        broker_id=$(echo "$response" | jq -r '.broker_id')
        log_success "Fyers broker added successfully!"
        log_info "Broker ID: $broker_id"
    else
        log_error "Failed to add Fyers broker"
        echo "$response"
    fi
}

list_brokers() {
    log_info "Listing configured brokers..."

    response=$(curl -s -H "Authorization: Bearer rt_demo_key_12345" \
        "$API_URL/brokers/list")

    if echo "$response" | jq -e '.brokers' > /dev/null 2>&1; then
        log_success "Configured brokers:"
        echo "$response" | jq .
    else
        log_error "Failed to list brokers"
        echo "$response"
    fi
}

list_api_keys() {
    log_info "Listing API keys..."

    if [ -z "$RAPIDTRADER_API_KEY" ]; then
        log_error "RAPIDTRADER_API_KEY environment variable is required"
        exit 1
    fi

    curl -s -H "Authorization: Bearer $RAPIDTRADER_API_KEY" \
        "$API_URL/auth/api-keys" | jq .
}

build_images() {
    log_info "Building Docker images..."

    cd "$PROJECT_ROOT"
    docker-compose -f "$COMPOSE_FILE" build

    log_success "Images built successfully"
}

clean_up() {
    log_info "Cleaning up containers and images..."

    cd "$PROJECT_ROOT"

    # Stop and remove containers
    docker-compose -f "$COMPOSE_FILE" down -v

    # Remove unused images
    docker image prune -f

    # Remove unused volumes
    docker volume prune -f

    log_success "Cleanup completed"
}

setup_environment() {
    log_info "Setting up RapidTrader environment..."

    # Create necessary directories
    mkdir -p "$PROJECT_ROOT/userdata/logs"
    mkdir -p "$PROJECT_ROOT/userdata/data"
    mkdir -p "$PROJECT_ROOT/userdata/results"
    mkdir -p "$PROJECT_ROOT/logs"

    # Generate secret key if not exists
    if [ ! -f "$PROJECT_ROOT/.env" ] || ! grep -q "RAPIDTRADER_SECRET_KEY" "$PROJECT_ROOT/.env"; then
        secret_key=$(openssl rand -base64 32)
        echo "RAPIDTRADER_SECRET_KEY=$secret_key" >> "$PROJECT_ROOT/.env"
        log_success "Generated secret key"
    fi

    # Build images
    build_images

    log_success "Environment setup completed"
}

check_health() {
    log_info "Checking API Gateway health..."

    response=$(curl -s "$API_URL/health")

    if echo "$response" | jq -e '.status' > /dev/null 2>&1; then
        echo "$response" | jq .
        log_success "API Gateway is healthy"
    else
        log_error "API Gateway health check failed"
        exit 1
    fi
}

run_demo() {
    log_info "Running RapidTrader Unified API Demo..."
    echo ""

    # Check if API is running
    if ! curl -s "$API_URL/health" > /dev/null 2>&1; then
        log_error "API Gateway is not running. Please start it first with: $0 start"
        exit 1
    fi

    log_info "🚀 RapidTrader Unified Symbol System Demo"
    echo ""

    # Demo 1: Search for popular symbols
    log_info "📊 Demo 1: Searching for popular symbols..."
    echo ""

    symbols=("SBIN" "RELIANCE" "TCS" "INFY" "HDFC")

    for symbol in "${symbols[@]}"; do
        echo "Searching for: $symbol"
        response=$(curl -s -H "Authorization: Bearer rt_demo_key_12345" \
            "$API_URL/symbols/search/$symbol?exchange=NSE_EQ")

        if echo "$response" | jq -e '.rapidtrader_symbol' > /dev/null 2>&1; then
            rt_symbol=$(echo "$response" | jq -r '.rapidtrader_symbol')
            fyers_symbol=$(echo "$response" | jq -r '.fyers_symbol // "Not mapped"')
            dhan_id=$(echo "$response" | jq -r '.dhan_security_id // "Not mapped"')

            echo "  ✅ RapidTrader: $rt_symbol"
            echo "  📈 Fyers: $fyers_symbol"
            echo "  🏦 Dhan ID: $dhan_id"
        else
            echo "  ❌ Not found"
        fi
        echo ""
    done

    # Demo 2: List available symbols
    log_info "📋 Demo 2: Listing available symbols (first 10)..."
    echo ""

    response=$(curl -s -H "Authorization: Bearer rt_demo_key_12345" \
        "$API_URL/symbols/list?exchange=NSE_EQ&limit=10")

    if echo "$response" | jq -e '.symbols' > /dev/null 2>&1; then
        echo "$response" | jq -r '.symbols[]' | head -10 | while read symbol; do
            echo "  📊 $symbol"
        done
        total=$(echo "$response" | jq -r '.total_available')
        echo ""
        log_info "Total symbols available: $total"
    else
        log_error "Failed to list symbols"
    fi

    echo ""
    log_info "🎯 Key Benefits of RapidTrader's Unified Symbol System:"
    echo "  ✅ Use standard symbols (SBIN, RELIANCE) across all brokers"
    echo "  ✅ Automatic mapping to broker-specific formats"
    echo "  ✅ Fyers: NSE:SBIN-EQ, Dhan: Security ID 3045"
    echo "  ✅ No need to remember different symbol formats"
    echo "  ✅ Easy strategy portability between brokers"
    echo ""

    log_success "Demo completed! Your unified symbol system is working perfectly."
    echo ""
    log_info "Next steps:"
    echo "  1. Add your broker: $0 add-fyers"
    echo "  2. Search symbols: $0 search-symbol YOURSTOCK"
    echo "  3. Start trading with standard symbols!"
}

# Main script logic
case "$1" in
    start)
        check_dependencies
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    search-symbol)
        search_symbol "$2" "$3"
        ;;
    list-symbols)
        list_symbols "$2" "$3"
        ;;
    add-fyers)
        add_fyers_broker
        ;;
    list-brokers)
        list_brokers
        ;;
    build)
        build_images
        ;;
    clean)
        clean_up
        ;;
    setup)
        setup_environment
        ;;
    health)
        check_health
        ;;
    demo)
        run_demo
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
