#!/usr/bin/env python3
"""
Start API Gateway for Demo

This script starts the RapidTrader API Gateway for demonstration purposes.
"""

import os
import sys
import time
import subprocess
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("api_demo_starter")

def start_api_gateway():
    """Start the API Gateway"""
    try:
        logger.info("Starting RapidTrader API Gateway...")
        
        # Change to project directory
        os.chdir(project_root)
        
        # Start the API Gateway using uvicorn
        cmd = [
            sys.executable, "-m", "uvicorn",
            "api_gateway.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        logger.info(f"Running command: {' '.join(cmd)}")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        logger.info("API Gateway started successfully!")
        logger.info("Access the API at: http://localhost:8000")
        logger.info("API Documentation: http://localhost:8000/docs")
        logger.info("Press Ctrl+C to stop")
        
        # Stream output
        try:
            for line in process.stdout:
                print(line.strip())
        except KeyboardInterrupt:
            logger.info("Stopping API Gateway...")
            process.terminate()
            process.wait()
            logger.info("API Gateway stopped")
            
    except Exception as e:
        logger.error(f"Error starting API Gateway: {e}")
        return False
    
    return True

def main():
    """Main function"""
    logger.info("🚀 RapidTrader API Gateway Demo Starter")
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        logger.info("✅ Running in virtual environment")
    else:
        logger.warning("⚠️ Not running in virtual environment")
        logger.warning("Consider activating virtual environment: source venv/bin/activate")
    
    # Check dependencies
    try:
        import uvicorn
        import fastapi
        logger.info("✅ Required dependencies available")
    except ImportError as e:
        logger.error(f"❌ Missing dependencies: {e}")
        logger.error("Install with: pip install fastapi uvicorn")
        return 1
    
    # Start API Gateway
    if start_api_gateway():
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
