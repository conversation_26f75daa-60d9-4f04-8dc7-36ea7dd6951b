import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'

// RapidTrader API types
export interface SymbolInfo {
  rapidtrader_symbol: string
  exchange: string
  fyers_symbol?: string
  dhan_security_id?: string
  is_available: boolean
}

export interface BrokerConfig {
  broker_name: string
  credentials: Record<string, string>
  dry_run: boolean
  live_data: boolean
}

export interface OrderRequest {
  symbol: string
  exchange: string
  quantity: number
  side: 'BUY' | 'SELL'
  order_type: 'MARKET' | 'LIMIT' | 'SL' | 'SL-M'
  price?: number
  trigger_price?: number
  product_type: string
  validity: string
}

export interface ApiResponse<T> {
  status: 'success' | 'error'
  data?: T
  message?: string
}

class RapidTraderAPI {
  private api: AxiosInstance
  private apiKey: string | null = null

  constructor(baseURL: string = 'http://localhost:8000') {
    this.api = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Load API key from localStorage
    this.apiKey = localStorage.getItem('rapidtrader_api_key') || 'rt_demo_key_12345'

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        if (this.apiKey) {
          config.headers.Authorization = `Bearer ${this.apiKey}`
        }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.logout()
        }
        return Promise.reject(error)
      }
    )
  }

  // Authentication
  setApiKey(apiKey: string) {
    this.apiKey = apiKey
    localStorage.setItem('rapidtrader_api_key', apiKey)
  }

  logout() {
    this.apiKey = null
    localStorage.removeItem('rapidtrader_api_key')
  }

  // Health check
  async healthCheck() {
    const response = await this.api.get('/health')
    return response.data
  }

  // Symbol management
  async searchSymbol(symbol: string, exchange: string = 'NSE_EQ'): Promise<SymbolInfo> {
    const response = await this.api.get(`/symbols/search/${symbol}`, {
      params: { exchange }
    })
    return response.data
  }

  async listSymbols(exchange: string = 'NSE_EQ', limit: number = 100) {
    const response = await this.api.get('/symbols/list', {
      params: { exchange, limit }
    })
    return response.data
  }

  async addSymbolMapping(
    symbol: string,
    dhanSecurityId?: string,
    fyersSymbol?: string,
    exchange: string = 'NSE_EQ'
  ) {
    const response = await this.api.post('/symbols/add', {
      symbol,
      dhan_security_id: dhanSecurityId,
      fyers_symbol: fyersSymbol,
      exchange
    })
    return response.data
  }

  // Broker management
  async addBroker(config: BrokerConfig) {
    const response = await this.api.post('/brokers/add', config)
    return response.data
  }

  async listBrokers() {
    const response = await this.api.get('/brokers/list')
    return response.data
  }

  async removeBroker(brokerName: string) {
    const response = await this.api.delete(`/brokers/${brokerName}`)
    return response.data
  }

  // Trading operations
  async placeOrder(order: OrderRequest, brokerId: string) {
    const response = await this.api.post(`/api/v1/placeorder`, order, {
      params: { broker_id: brokerId }
    })
    return response.data
  }

  async getPositions(brokerId: string) {
    const response = await this.api.get('/api/v1/positions', {
      params: { broker_id: brokerId }
    })
    return response.data
  }

  async getOrderbook(brokerId: string) {
    const response = await this.api.get('/api/v1/orderbook', {
      params: { broker_id: brokerId }
    })
    return response.data
  }

  async getTradebook(brokerId: string) {
    const response = await this.api.get('/api/v1/tradebook', {
      params: { broker_id: brokerId }
    })
    return response.data
  }

  async getFunds(brokerId: string) {
    const response = await this.api.get('/api/v1/funds', {
      params: { broker_id: brokerId }
    })
    return response.data
  }

  async getQuotes(symbols: string[], brokerId: string) {
    const response = await this.api.get('/api/v1/quotes', {
      params: { 
        broker_id: brokerId,
        symbols: symbols.join(',')
      }
    })
    return response.data
  }

  // Container management
  async listContainers() {
    const response = await this.api.get('/containers')
    return response.data
  }

  async manageContainer(action: string, containerType: string, config?: any) {
    const response = await this.api.post('/containers/manage', {
      action,
      container_type: containerType,
      config
    })
    return response.data
  }

  // Logs and monitoring
  async getAggregatedLogs(limit: number = 100) {
    const response = await this.api.get('/logs/aggregated', {
      params: { limit }
    })
    return response.data
  }

  async getAggregatedPnL() {
    const response = await this.api.get('/pnl/aggregated')
    return response.data
  }

  // WebSocket connection for real-time updates
  connectWebSocket(onMessage: (data: any) => void, onError?: (error: any) => void) {
    const wsUrl = this.api.defaults.baseURL?.replace('http', 'ws') + '/ws'
    const ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      console.log('WebSocket connected to RapidTrader')
      // Send authentication
      if (this.apiKey) {
        ws.send(JSON.stringify({ type: 'auth', token: this.apiKey }))
      }
    }

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        onMessage(data)
      } catch (error) {
        console.error('WebSocket message parse error:', error)
      }
    }

    ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      if (onError) onError(error)
    }

    ws.onclose = () => {
      console.log('WebSocket disconnected from RapidTrader')
    }

    return ws
  }
}

// Create singleton instance
export const rapidtraderApi = new RapidTraderAPI()
export default rapidtraderApi
