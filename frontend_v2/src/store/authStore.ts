import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import rapidtraderApi from '../services/rapidtraderApi'

interface User {
  id: string
  name: string
  email?: string
  apiKey: string
  permissions: string[]
}

interface AuthState {
  user: User | null
  isLoading: boolean
  error: string | null
  
  // Actions
  login: (apiKey: string, name?: string) => Promise<boolean>
  logout: () => void
  setUser: (user: User | null) => void
  checkAuth: () => Promise<boolean>
  clearError: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: false,
      error: null,

      login: async (apiKey: string, name: string = 'RapidTrader User') => {
        set({ isLoading: true, error: null })
        
        try {
          // Set API key in the service
          rapidtraderApi.setApiKey(apiKey)
          
          // Test the API key by making a health check
          const healthResponse = await rapidtraderApi.healthCheck()
          
          if (healthResponse.status === 'healthy') {
            const user: User = {
              id: 'rt_user_' + Date.now(),
              name,
              apiKey,
              permissions: ['read', 'write'] // Default permissions
            }
            
            set({ user, isLoading: false, error: null })
            return true
          } else {
            throw new Error('API health check failed')
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || error.message || 'Login failed'
          set({ 
            user: null, 
            isLoading: false, 
            error: errorMessage 
          })
          rapidtraderApi.logout()
          return false
        }
      },

      logout: () => {
        rapidtraderApi.logout()
        set({ user: null, error: null })
      },

      setUser: (user: User | null) => {
        set({ user })
      },

      checkAuth: async () => {
        const { user } = get()
        
        if (!user?.apiKey) {
          return false
        }

        set({ isLoading: true })
        
        try {
          // Test if the stored API key is still valid
          rapidtraderApi.setApiKey(user.apiKey)
          const healthResponse = await rapidtraderApi.healthCheck()
          
          if (healthResponse.status === 'healthy') {
            set({ isLoading: false })
            return true
          } else {
            throw new Error('API key invalid')
          }
        } catch (error) {
          // API key is invalid, logout
          get().logout()
          set({ isLoading: false })
          return false
        }
      },

      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'rapidtrader-auth',
      partialize: (state) => ({ 
        user: state.user 
      }),
    }
  )
)
