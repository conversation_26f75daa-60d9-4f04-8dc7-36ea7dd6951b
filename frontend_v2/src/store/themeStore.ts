import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface ThemeState {
  darkMode: boolean
  sidebarCollapsed: boolean
  
  // Actions
  toggleDarkMode: () => void
  setDarkMode: (darkMode: boolean) => void
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      darkMode: true, // Default to dark mode for trading
      sidebarCollapsed: false,

      toggleDarkMode: () => {
        set((state) => ({ darkMode: !state.darkMode }))
      },

      setDarkMode: (darkMode: boolean) => {
        set({ darkMode })
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }))
      },

      setSidebarCollapsed: (collapsed: boolean) => {
        set({ sidebarCollapsed: collapsed })
      },
    }),
    {
      name: 'rapidtrader-theme',
    }
  )
)
