import { useState } from 'react'
import { Navigate } from 'react-router-dom'
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  CircularProgress,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  Visibility,
  VisibilityOff,
  TrendingUp,
  DarkMode,
  LightMode,
} from '@mui/icons-material'
import { useAuthStore } from '../store/authStore'
import { useThemeStore } from '../store/themeStore'

const Login = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { user, login, isLoading, error, clearError } = useAuthStore()
  const { darkMode, toggleDarkMode } = useThemeStore()
  
  const [formData, setFormData] = useState({
    apiKey: 'rt_demo_key_12345', // Pre-filled demo key
    name: 'RapidTrader User',
  })
  const [showApi<PERSON><PERSON>, setShowApi<PERSON><PERSON>] = useState(false)

  // Redirect if already logged in
  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()
    
    if (!formData.apiKey.trim()) {
      return
    }

    await login(formData.apiKey.trim(), formData.name.trim() || 'RapidTrader User')
  }

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }))
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: darkMode 
          ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)'
          : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: isMobile ? 2 : 3,
      }}
    >
      {/* Theme toggle button */}
      <IconButton
        onClick={toggleDarkMode}
        sx={{
          position: 'fixed',
          top: 16,
          right: 16,
          zIndex: 1000,
          bgcolor: 'background.paper',
          '&:hover': {
            bgcolor: 'action.hover',
          },
        }}
      >
        {darkMode ? <LightMode /> : <DarkMode />}
      </IconButton>

      <Container maxWidth="sm">
        <Paper
          elevation={darkMode ? 8 : 2}
          sx={{
            p: isMobile ? 3 : 4,
            borderRadius: 2,
            background: darkMode 
              ? 'rgba(30, 41, 59, 0.8)'
              : 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            border: darkMode ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
          }}
        >
          {/* Logo and title */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 64,
                height: 64,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #14b8a6 0%, #0d9488 100%)',
                mb: 2,
              }}
            >
              <TrendingUp sx={{ fontSize: 32, color: 'white' }} />
            </Box>
            
            <Typography
              variant={isMobile ? 'h4' : 'h3'}
              component="h1"
              fontWeight={700}
              sx={{
                background: 'linear-gradient(135deg, #14b8a6 0%, #0d9488 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1,
              }}
            >
              RapidTrader
            </Typography>
            
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontSize: isMobile ? '0.9rem' : '1rem' }}
            >
              Unified Trading Platform v2.0
            </Typography>
          </Box>

          {/* Login form */}
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
            {error && (
              <Alert 
                severity="error" 
                sx={{ mb: 3 }}
                onClose={clearError}
              >
                {error}
              </Alert>
            )}

            <TextField
              fullWidth
              label="Display Name"
              value={formData.name}
              onChange={handleInputChange('name')}
              margin="normal"
              variant="outlined"
              placeholder="Enter your display name"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="API Key"
              type={showApiKey ? 'text' : 'password'}
              value={formData.apiKey}
              onChange={handleInputChange('apiKey')}
              margin="normal"
              variant="outlined"
              placeholder="Enter your RapidTrader API key"
              required
              InputProps={{
                endAdornment: (
                  <IconButton
                    onClick={() => setShowApiKey(!showApiKey)}
                    edge="end"
                  >
                    {showApiKey ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                ),
              }}
              sx={{ mb: 3 }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading || !formData.apiKey.trim()}
              sx={{
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
                background: 'linear-gradient(135deg, #14b8a6 0%, #0d9488 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #0d9488 0%, #0f766e 100%)',
                },
                '&:disabled': {
                  background: 'rgba(148, 163, 184, 0.3)',
                },
              }}
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Connect to RapidTrader'
              )}
            </Button>
          </Box>

          {/* Demo info */}
          <Box
            sx={{
              mt: 4,
              p: 2,
              borderRadius: 1,
              bgcolor: darkMode ? 'rgba(20, 184, 166, 0.1)' : 'rgba(20, 184, 166, 0.05)',
              border: '1px solid rgba(20, 184, 166, 0.2)',
            }}
          >
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              <strong>Demo Mode:</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
              Use the pre-filled demo API key to explore RapidTrader's unified symbol system.
              The demo connects to your local RapidTrader API at localhost:8000.
            </Typography>
          </Box>

          {/* Features */}
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Features:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
              {[
                'Unified Symbols',
                'Multi-Broker Support',
                'Real-time Data',
                'API Authentication',
              ].map((feature) => (
                <Box
                  key={feature}
                  sx={{
                    px: 2,
                    py: 0.5,
                    borderRadius: 1,
                    bgcolor: 'action.hover',
                    fontSize: '0.75rem',
                    fontWeight: 500,
                  }}
                >
                  {feature}
                </Box>
              ))}
            </Box>
          </Box>
        </Paper>
      </Container>
    </Box>
  )
}

export default Login
