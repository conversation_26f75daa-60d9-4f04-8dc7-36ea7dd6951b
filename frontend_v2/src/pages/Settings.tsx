import { Box, Typography, Paper, Switch, FormControlLabel, Divider } from '@mui/material'
import { useThemeStore } from '../store/themeStore'

const Settings = () => {
  const { darkMode, toggleDarkMode, sidebarCollapsed, toggleSidebar } = useThemeStore()

  return (
    <Box>
      <Typography variant="h3" fontWeight={700} gutterBottom>
        Settings
      </Typography>
      
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Appearance
        </Typography>
        
        <FormControlLabel
          control={<Switch checked={darkMode} onChange={toggleDarkMode} />}
          label="Dark Mode"
        />
        
        <Divider sx={{ my: 2 }} />
        
        <FormControlLabel
          control={<Switch checked={sidebarCollapsed} onChange={toggleSidebar} />}
          label="Collapse Sidebar"
        />
      </Paper>
    </Box>
  )
}

export default Settings
