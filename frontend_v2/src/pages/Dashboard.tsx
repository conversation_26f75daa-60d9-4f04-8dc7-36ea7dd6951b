import { useState, useEffect } from 'react'
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  IconButton,
  Chip,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Alert,
} from '@mui/material'
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  ShowChart,
  Receipt,
  Refresh,
} from '@mui/icons-material'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import rapidtraderApi from '../services/rapidtraderApi'
import { useAuthStore } from '../store/authStore'

// Sample data for demo
const generatePortfolioData = () => {
  const data = []
  const now = new Date()
  
  for (let i = 30; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(now.getDate() - i)
    
    data.push({
      date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      value: Math.round(100000 + Math.random() * 20000 - 10000),
    })
  }
  
  return data
}

interface DashboardStats {
  totalBalance: number
  totalPnL: number
  totalPnLPercent: number
  activePositions: number
  openOrders: number
  connectedBrokers: number
}

const Dashboard = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'))
  const { user } = useAuthStore()
  
  const [stats, setStats] = useState<DashboardStats>({
    totalBalance: 100000,
    totalPnL: 2500,
    totalPnLPercent: 2.5,
    activePositions: 5,
    openOrders: 3,
    connectedBrokers: 0,
  })
  const [portfolioData] = useState(generatePortfolioData)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [apiHealth, setApiHealth] = useState<any>(null)

  // Fetch dashboard data
  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check API health
      const health = await rapidtraderApi.healthCheck()
      setApiHealth(health)

      // Get brokers
      const brokersResponse = await rapidtraderApi.listBrokers()
      const brokerCount = brokersResponse.brokers?.length || 0

      setStats(prev => ({
        ...prev,
        connectedBrokers: brokerCount,
      }))

    } catch (err: any) {
      setError(err.message || 'Failed to fetch dashboard data')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const StatCard = ({ 
    title, 
    value, 
    subtitle, 
    icon, 
    trend, 
    trendValue, 
    color = theme.palette.primary.main 
  }: {
    title: string
    value: string | number
    subtitle?: string
    icon: React.ReactNode
    trend?: 'up' | 'down' | 'neutral'
    trendValue?: string
    color?: string
  }) => (
    <Card
      sx={{
        height: '100%',
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${color}30`,
      }}
    >
      <CardContent sx={{ p: isMobile ? 2 : 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant={isMobile ? 'h5' : 'h4'} fontWeight={700} sx={{ mb: 1 }}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
            {trend && trendValue && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {trend === 'up' ? (
                  <TrendingUp sx={{ fontSize: 16, color: 'success.main', mr: 0.5 }} />
                ) : trend === 'down' ? (
                  <TrendingDown sx={{ fontSize: 16, color: 'error.main', mr: 0.5 }} />
                ) : null}
                <Typography
                  variant="body2"
                  color={trend === 'up' ? 'success.main' : trend === 'down' ? 'error.main' : 'text.secondary'}
                  fontWeight={500}
                >
                  {trendValue}
                </Typography>
              </Box>
            )}
          </Box>
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: 2,
              bgcolor: `${color}20`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: color,
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant={isMobile ? 'h4' : 'h3'} fontWeight={700} gutterBottom>
            Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Welcome back, {user?.name || 'Trader'}! Here's your trading overview.
          </Typography>
        </Box>
        <IconButton onClick={fetchDashboardData} disabled={loading}>
          <Refresh />
        </IconButton>
      </Box>

      {/* API Status */}
      {apiHealth && (
        <Alert 
          severity="success" 
          sx={{ mb: 3 }}
          action={
            <Chip 
              label={`${apiHealth.symbol_mappers?.dhan_symbols || 0} symbols`} 
              size="small" 
              color="success"
            />
          }
        >
          RapidTrader API is healthy and connected
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={isMobile ? 2 : 3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Account Balance"
            value={`$${stats.totalBalance.toLocaleString()}`}
            subtitle="Available for trading"
            icon={<AccountBalance />}
            trend="up"
            trendValue="****% today"
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total P&L"
            value={`$${stats.totalPnL.toLocaleString()}`}
            subtitle="Unrealized gains"
            icon={<TrendingUp />}
            trend={stats.totalPnL >= 0 ? 'up' : 'down'}
            trendValue={`${stats.totalPnLPercent.toFixed(2)}%`}
            color={stats.totalPnL >= 0 ? theme.palette.success.main : theme.palette.error.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Active Positions"
            value={stats.activePositions}
            subtitle="Open positions"
            icon={<ShowChart />}
            color={theme.palette.info.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Connected Brokers"
            value={stats.connectedBrokers}
            subtitle="Active connections"
            icon={<Receipt />}
            color={theme.palette.warning.main}
          />
        </Grid>
      </Grid>

      {/* Charts and Data */}
      <Grid container spacing={isMobile ? 2 : 3}>
        {/* Portfolio Chart */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: isMobile ? 2 : 3, height: isMobile ? 300 : 400 }}>
            <Typography variant="h6" gutterBottom>
              Portfolio Value (30 Days)
            </Typography>
            <Box sx={{ height: isMobile ? 240 : 320, mt: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={portfolioData}>
                  <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                  <XAxis 
                    dataKey="date" 
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <YAxis 
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: theme.palette.background.paper,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 8,
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke={theme.palette.primary.main}
                    strokeWidth={2}
                    dot={{ r: 1 }}
                    activeDot={{ r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: isMobile ? 2 : 3, height: isMobile ? 300 : 400 }}>
            <Typography variant="h6" gutterBottom>
              RapidTrader Status
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              {apiHealth && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    API Health
                  </Typography>
                  <Chip 
                    label={apiHealth.status} 
                    color="success" 
                    size="small"
                    sx={{ mb: 2 }}
                  />
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Symbol Mappings
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                    <Chip 
                      label={`Dhan: ${apiHealth.symbol_mappers?.dhan_symbols || 0}`} 
                      size="small" 
                      variant="outlined"
                    />
                    <Chip 
                      label={`Fyers: ${apiHealth.symbol_mappers?.fyers_symbols || 0}`} 
                      size="small" 
                      variant="outlined"
                    />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Connected Brokers
                  </Typography>
                  <Chip 
                    label={`${stats.connectedBrokers} brokers`} 
                    size="small" 
                    color={stats.connectedBrokers > 0 ? 'success' : 'default'}
                  />
                </Box>
              )}
              
              <Typography variant="body2" color="text.secondary" sx={{ mt: 3 }}>
                Unified Symbol System Active
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Use standard symbols like SBIN, RELIANCE across all brokers.
                RapidTrader automatically maps to broker-specific formats.
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Dashboard
