import { useState, useEffect } from 'react'
import {
  Box,
  Paper,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  Add as AddIcon,
  Hub as BrokerIcon,
  CheckCircle as ConnectedIcon,
  Cancel as DisconnectedIcon,
} from '@mui/icons-material'
import rapidtraderApi from '../services/rapidtraderApi'

interface Broker {
  id: string
  type: string
  dry_run: boolean
}

const Brokers = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  const [brokers, setBrokers] = useState<Broker[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [brokerForm, setBrokerForm] = useState({
    broker_name: 'fyers',
    client_id: '',
    access_token: '',
    refresh_token: '',
    dry_run: true,
  })

  const fetchBrokers = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await rapidtraderApi.listBrokers()
      setBrokers(response.brokers || [])
    } catch (err: any) {
      setError(err.message || 'Failed to fetch brokers')
    } finally {
      setLoading(false)
    }
  }

  const handleAddBroker = async () => {
    try {
      await rapidtraderApi.addBroker({
        broker_name: brokerForm.broker_name,
        credentials: {
          client_id: brokerForm.client_id,
          access_token: brokerForm.access_token,
          refresh_token: brokerForm.refresh_token,
        },
        dry_run: brokerForm.dry_run,
        live_data: true,
      })
      
      setAddDialogOpen(false)
      setBrokerForm({
        broker_name: 'fyers',
        client_id: '',
        access_token: '',
        refresh_token: '',
        dry_run: true,
      })
      fetchBrokers()
    } catch (err: any) {
      setError(err.message || 'Failed to add broker')
    }
  }

  useEffect(() => {
    fetchBrokers()
  }, [])

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant={isMobile ? 'h4' : 'h3'} fontWeight={700} gutterBottom>
            Brokers
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage your broker connections
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
        >
          Add Broker
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          {brokers.length === 0 ? (
            <Grid item xs={12}>
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <BrokerIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No Brokers Connected
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Add your first broker to start trading with RapidTrader
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => setAddDialogOpen(true)}
                >
                  Add Broker
                </Button>
              </Paper>
            </Grid>
          ) : (
            brokers.map((broker) => (
              <Grid item xs={12} sm={6} md={4} key={broker.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <BrokerIcon sx={{ mr: 2, color: 'primary.main' }} />
                      <Typography variant="h6" sx={{ textTransform: 'capitalize' }}>
                        {broker.type}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip
                        icon={<ConnectedIcon />}
                        label="Connected"
                        color="success"
                        size="small"
                      />
                      {broker.dry_run && (
                        <Chip
                          label="Dry Run"
                          color="warning"
                          size="small"
                        />
                      )}
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary">
                      ID: {broker.id}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))
          )}
        </Grid>
      )}

      {/* Add Broker Dialog */}
      <Dialog 
        open={addDialogOpen} 
        onClose={() => setAddDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add New Broker</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Broker</InputLabel>
                <Select
                  value={brokerForm.broker_name}
                  onChange={(e) => setBrokerForm(prev => ({ ...prev, broker_name: e.target.value }))}
                  label="Broker"
                >
                  <MenuItem value="fyers">Fyers</MenuItem>
                  <MenuItem value="dhan">Dhan</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Client ID"
                value={brokerForm.client_id}
                onChange={(e) => setBrokerForm(prev => ({ ...prev, client_id: e.target.value }))}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Access Token"
                value={brokerForm.access_token}
                onChange={(e) => setBrokerForm(prev => ({ ...prev, access_token: e.target.value }))}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Refresh Token (Optional)"
                value={brokerForm.refresh_token}
                onChange={(e) => setBrokerForm(prev => ({ ...prev, refresh_token: e.target.value }))}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Mode</InputLabel>
                <Select
                  value={brokerForm.dry_run ? 'dry_run' : 'live'}
                  onChange={(e) => setBrokerForm(prev => ({ ...prev, dry_run: e.target.value === 'dry_run' }))}
                  label="Mode"
                >
                  <MenuItem value="dry_run">Dry Run (Safe)</MenuItem>
                  <MenuItem value="live">Live Trading</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddBroker}
            variant="contained"
            disabled={!brokerForm.client_id || !brokerForm.access_token}
          >
            Add Broker
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Brokers
