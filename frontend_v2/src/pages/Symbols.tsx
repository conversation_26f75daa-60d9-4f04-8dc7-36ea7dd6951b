import { useState, useEffect } from 'react'
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  InputAdornment,
  Grid,
  Card,
  CardContent,
} from '@mui/material'
import {
  Search as SearchIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material'
import rapidtraderApi, { SymbolInfo } from '../services/rapidtraderApi'

const Symbols = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  const [searchTerm, setSearchTerm] = useState('')
  const [searchResult, setSearchResult] = useState<SymbolInfo | null>(null)
  const [symbols, setSymbols] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchLoading, setSearchLoading] = useState(false)

  // Fetch symbols list
  const fetchSymbols = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await rapidtraderApi.listSymbols('NSE_EQ', 50)
      setSymbols(response.symbols || [])
    } catch (err: any) {
      setError(err.message || 'Failed to fetch symbols')
    } finally {
      setLoading(false)
    }
  }

  // Search for a symbol
  const handleSearch = async () => {
    if (!searchTerm.trim()) return

    try {
      setSearchLoading(true)
      setError(null)
      const result = await rapidtraderApi.searchSymbol(searchTerm.trim().toUpperCase())
      setSearchResult(result)
    } catch (err: any) {
      setError(err.message || 'Symbol not found')
      setSearchResult(null)
    } finally {
      setSearchLoading(false)
    }
  }

  // Handle search on Enter key
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  useEffect(() => {
    fetchSymbols()
  }, [])

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant={isMobile ? 'h4' : 'h3'} fontWeight={700} gutterBottom>
          Symbol Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Search and manage RapidTrader's unified symbol system
        </Typography>
      </Box>

      {/* Search Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Symbol Search
        </Typography>
        
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              label="Search Symbol"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter symbol (e.g., SBIN, RELIANCE, TCS)"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Button
              fullWidth={isMobile}
              variant="contained"
              onClick={handleSearch}
              disabled={!searchTerm.trim() || searchLoading}
              startIcon={searchLoading ? <CircularProgress size={20} /> : <SearchIcon />}
            >
              Search
            </Button>
          </Grid>
        </Grid>

        {/* Search Result */}
        {searchResult && (
          <Card sx={{ mt: 3, bgcolor: 'success.main', color: 'success.contrastText' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Symbol Found: {searchResult.rapidtrader_symbol}
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Exchange
                  </Typography>
                  <Typography variant="body1" fontWeight={600}>
                    {searchResult.exchange}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Fyers Symbol
                  </Typography>
                  <Typography variant="body1" fontWeight={600}>
                    {searchResult.fyers_symbol || 'Not mapped'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Dhan Security ID
                  </Typography>
                  <Typography variant="body1" fontWeight={600}>
                    {searchResult.dhan_security_id || 'Not mapped'}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" sx={{ opacity: 0.9 }}>
                    Status
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {searchResult.is_available ? (
                      <CheckIcon sx={{ fontSize: 20 }} />
                    ) : (
                      <CancelIcon sx={{ fontSize: 20 }} />
                    )}
                    <Typography variant="body1" fontWeight={600}>
                      {searchResult.is_available ? 'Available' : 'Not Available'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        )}
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Symbols List */}
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Available Symbols ({symbols.length})
          </Typography>
          <IconButton onClick={fetchSymbols} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {isMobile ? (
              // Mobile view - Cards
              <Grid container spacing={2}>
                {symbols.map((symbol) => (
                  <Grid item xs={12} sm={6} key={symbol}>
                    <Card variant="outlined">
                      <CardContent sx={{ p: 2 }}>
                        <Typography variant="h6" gutterBottom>
                          {symbol}
                        </Typography>
                        <Chip 
                          label="NSE_EQ" 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              // Desktop view - Table
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Symbol</TableCell>
                      <TableCell>Exchange</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {symbols.map((symbol) => (
                      <TableRow key={symbol} hover>
                        <TableCell>
                          <Typography variant="body1" fontWeight={600}>
                            {symbol}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip label="NSE_EQ" size="small" color="primary" variant="outlined" />
                        </TableCell>
                        <TableCell>
                          <Chip label="Available" size="small" color="success" />
                        </TableCell>
                        <TableCell align="right">
                          <Button
                            size="small"
                            onClick={() => {
                              setSearchTerm(symbol)
                              handleSearch()
                            }}
                          >
                            View Details
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {symbols.length === 0 && !loading && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  No symbols found. Make sure RapidTrader API is running.
                </Typography>
              </Box>
            )}
          </>
        )}
      </Paper>

      {/* Info Box */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          🎯 RapidTrader Unified Symbol System
        </Typography>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          Use standard symbols like SBIN, RELIANCE, TCS across all brokers. 
          RapidTrader automatically maps them to broker-specific formats:
        </Typography>
        <Box sx={{ mt: 2, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Chip 
            label="Fyers: NSE:SBIN-EQ" 
            size="small" 
            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'inherit' }}
          />
          <Chip 
            label="Dhan: Security ID 3045" 
            size="small" 
            sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'inherit' }}
          />
        </Box>
      </Paper>
    </Box>
  )
}

export default Symbols
