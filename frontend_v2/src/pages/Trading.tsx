import { useState } from 'react'
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
} from '@mui/material'
import {
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material'

const Trading = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  const [orderForm, setOrderForm] = useState({
    symbol: '',
    quantity: '',
    side: 'BUY',
    orderType: 'MARKET',
    price: '',
    exchange: 'NSE_EQ',
  })

  const handleInputChange = (field: string) => (e: any) => {
    setOrderForm(prev => ({
      ...prev,
      [field]: e.target.value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement order placement
    console.log('Order:', orderForm)
  }

  return (
    <Box>
      <Typography variant={isMobile ? 'h4' : 'h3'} fontWeight={700} gutterBottom>
        Trading
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Place orders using RapidTrader's unified symbol system
      </Typography>

      <Grid container spacing={3}>
        {/* Order Form */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Place Order
            </Typography>
            
            <Box component="form" onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Symbol"
                    value={orderForm.symbol}
                    onChange={handleInputChange('symbol')}
                    placeholder="e.g., SBIN, RELIANCE"
                    helperText="Use RapidTrader standard symbols"
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Exchange</InputLabel>
                    <Select
                      value={orderForm.exchange}
                      onChange={handleInputChange('exchange')}
                      label="Exchange"
                    >
                      <MenuItem value="NSE_EQ">NSE Equity</MenuItem>
                      <MenuItem value="BSE_EQ">BSE Equity</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Quantity"
                    type="number"
                    value={orderForm.quantity}
                    onChange={handleInputChange('quantity')}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Side</InputLabel>
                    <Select
                      value={orderForm.side}
                      onChange={handleInputChange('side')}
                      label="Side"
                    >
                      <MenuItem value="BUY">Buy</MenuItem>
                      <MenuItem value="SELL">Sell</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Order Type</InputLabel>
                    <Select
                      value={orderForm.orderType}
                      onChange={handleInputChange('orderType')}
                      label="Order Type"
                    >
                      <MenuItem value="MARKET">Market</MenuItem>
                      <MenuItem value="LIMIT">Limit</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                {orderForm.orderType === 'LIMIT' && (
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Price"
                      type="number"
                      value={orderForm.price}
                      onChange={handleInputChange('price')}
                    />
                  </Grid>
                )}
                
                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    fullWidth
                    sx={{
                      bgcolor: orderForm.side === 'BUY' ? 'success.main' : 'error.main',
                      '&:hover': {
                        bgcolor: orderForm.side === 'BUY' ? 'success.dark' : 'error.dark',
                      },
                    }}
                    startIcon={orderForm.side === 'BUY' ? <TrendingUp /> : <TrendingDown />}
                  >
                    {orderForm.side === 'BUY' ? 'Buy' : 'Sell'} {orderForm.symbol || 'Symbol'}
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        </Grid>

        {/* Market Data */}
        <Grid item xs={12} lg={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Market Data
            </Typography>
            <Alert severity="info">
              Real-time market data will be displayed here when connected to brokers.
            </Alert>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}

export default Trading
