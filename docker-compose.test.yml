# Docker Compose for Testing All RapidTrader Modes
# This file provides easy testing of all trading modes with proper lifecycle management

version: '3.8'

services:
  # Base service configuration
  rapidtrader-base: &rapidtrader-base
    build:
      context: .
      dockerfile: Dockerfile
    image: rapidtrader:latest
    volumes:
      - ./userdata:/rapidtrader/userdata
      - ./logs:/rapidtrader/logs
    env_file:
      - .env
    networks:
      - rapidtrader-test-network

  # Paper Trading Test (Independent)
  test-paper-trade:
    <<: *rapidtrader-base
    container_name: rapidtrader-test-paper-trade
    command: paper-trade --duration 300 --capital 50000
    profiles:
      - test-paper
      - test-all
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dry_run_config.json
      - LOG_LEVEL=INFO
    restart: "no"
    ports:
      - "8081:8081"

  # Dry Run Test (Broker-based)
  test-dryrun:
    <<: *rapidtrader-base
    container_name: rapidtrader-test-dryrun
    command: dryrun
    profiles:
      - test-dryrun
      - test-all
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dryrun-config.json
      - LOG_LEVEL=INFO
    restart: "no"

  # Backtesting Test (Auto-stops when complete)
  test-backtest:
    <<: *rapidtrader-base
    container_name: rapidtrader-test-backtest
    command: backtest
    profiles:
      - test-backtest
      - test-all
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/backtest-config.json
      - LOG_LEVEL=INFO
    restart: "no"

  # Optimization Test (Auto-stops when complete)
  test-optimize:
    <<: *rapidtrader-base
    container_name: rapidtrader-test-optimize
    command: optimize
    profiles:
      - test-optimize
      - test-all
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/optimize-config.json
      - LOG_LEVEL=INFO
    restart: "no"

  # Live Trading Demo (Safe demo mode)
  test-live:
    <<: *rapidtrader-base
    container_name: rapidtrader-test-live
    command: live
    profiles:
      - test-live
      - test-all
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/live-config.json
      - LOG_LEVEL=INFO
      - DEMO_MODE=true  # Safety flag
    restart: "no"
    ports:
      - "8080:8080"

  # Container Monitor (for monitoring all test containers)
  monitor:
    <<: *rapidtrader-base
    container_name: rapidtrader-monitor
    command: shell
    profiles:
      - monitor
    environment:
      - LOG_LEVEL=DEBUG
    restart: "no"
    stdin_open: true
    tty: true
    depends_on:
      - test-paper-trade
      - test-dryrun
      - test-backtest
      - test-optimize

networks:
  rapidtrader-test-network:
    driver: bridge
    name: rapidtrader-test-network
