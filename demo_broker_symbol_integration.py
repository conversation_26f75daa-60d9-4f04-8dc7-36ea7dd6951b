#!/usr/bin/env python3
"""
Demo: Broker Symbol Manager Integration

This script demonstrates how to integrate the new broker-specific symbol manager
with existing broker configurations and show real-world usage scenarios.
"""

import os
import sys
import json
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from data.symbol_manager import BrokerSymbolManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("broker_integration_demo")

def demo_fyers_integration():
    """Demonstrate Fyers broker integration"""
    print("🔥 Fyers Broker Integration Demo")
    print("=" * 50)
    
    manager = BrokerSymbolManager()
    
    # Simulate Fyers configuration (from .env or config file)
    fyers_config = {
        "client_id": os.getenv("FYERS_CLIENT_ID", "KQL8MIGYUG-100"),
        "access_token": os.getenv("FYERS_ACCESS_TOKEN", "demo_token"),
        "refresh_token": os.getenv("FYERS_REFRESH_TOKEN", "demo_refresh"),
        "dry_run": True,
        "live_data": False
    }
    
    print(f"📝 Registering Fyers broker...")
    success = manager.register_broker_config("fyers", fyers_config)
    
    if success:
        print(f"✅ Fyers registered successfully")
        
        # Show downloaded symbols
        symbols = manager.load_broker_symbols("fyers")
        print(f"📊 Downloaded {len(symbols)} Fyers symbols")
        
        if symbols:
            print(f"📋 Sample Fyers symbols:")
            for symbol in symbols[:5]:
                fyers_symbol = symbol.get('broker_symbol', 'N/A')
                name = symbol.get('name', 'N/A')
                print(f"  • {symbol['symbol']} → {fyers_symbol} ({name})")
    else:
        print(f"❌ Fyers registration failed")
    
    return success

def demo_dhan_integration():
    """Demonstrate DhanHQ broker integration"""
    print("\n🏦 DhanHQ Broker Integration Demo")
    print("=" * 50)
    
    manager = BrokerSymbolManager()
    
    # Simulate DhanHQ configuration
    dhan_config = {
        "client_id": os.getenv("DHAN_CLIENT_ID", "demo_dhan_client"),
        "access_token": os.getenv("DHAN_ACCESS_TOKEN", "demo_dhan_token"),
        "dry_run": True,
        "live_data": False
    }
    
    print(f"📝 Registering DhanHQ broker...")
    success = manager.register_broker_config("dhan", dhan_config)
    
    if success:
        print(f"✅ DhanHQ registered successfully")
        
        # Show downloaded symbols
        symbols = manager.load_broker_symbols("dhan")
        print(f"📊 Downloaded {len(symbols)} DhanHQ symbols")
        
        if symbols:
            print(f"📋 Sample DhanHQ symbols:")
            for symbol in symbols[:5]:
                security_id = symbol.get('broker_symbol', 'N/A')
                name = symbol.get('name', 'N/A')
                print(f"  • {symbol['symbol']} → {security_id} ({name})")
    else:
        print(f"❌ DhanHQ registration failed")
    
    return success

def demo_symbol_mapping():
    """Demonstrate unified symbol mapping"""
    print("\n🔗 Unified Symbol Mapping Demo")
    print("=" * 50)
    
    manager = BrokerSymbolManager()
    
    # Popular symbols to test
    test_symbols = ["RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "INFY"]
    
    print(f"🔍 Testing symbol mappings for popular stocks:")
    
    for symbol in test_symbols:
        print(f"\n📈 {symbol}:")
        
        # Get mappings for all registered brokers
        for broker in manager.broker_configs.keys():
            mapping = manager.get_broker_symbol_mapping(symbol, broker)
            if mapping:
                print(f"  • {broker}: {symbol} → {mapping}")
            else:
                print(f"  • {broker}: No mapping found")

def demo_cross_broker_search():
    """Demonstrate cross-broker symbol search"""
    print("\n🔍 Cross-Broker Search Demo")
    print("=" * 50)
    
    manager = BrokerSymbolManager()
    
    search_queries = ["RELIANCE", "BANK", "TECH"]
    
    for query in search_queries:
        print(f"\n🔎 Searching for '{query}' across all brokers:")
        
        results = manager.search_broker_symbols(query, limit=10)
        
        if results:
            print(f"  ✅ Found {len(results)} matches:")
            
            # Group by broker
            broker_results = {}
            for result in results:
                broker = result.get('broker', 'unknown')
                if broker not in broker_results:
                    broker_results[broker] = []
                broker_results[broker].append(result)
            
            for broker, symbols in broker_results.items():
                print(f"    📊 {broker} ({len(symbols)} matches):")
                for symbol in symbols[:3]:  # Show first 3
                    name = symbol.get('name', 'N/A')
                    broker_symbol = symbol.get('broker_symbol', 'N/A')
                    print(f"      • {symbol['symbol']} → {broker_symbol} ({name})")
        else:
            print(f"  ❌ No matches found")

def demo_master_contract():
    """Demonstrate master contract functionality"""
    print("\n📋 Master Contract Demo")
    print("=" * 50)
    
    manager = BrokerSymbolManager()
    
    print(f"📝 Creating unified master contract...")
    success = manager.create_broker_master_contract()
    
    if success:
        print(f"✅ Master contract created successfully")
        
        # Load and analyze master contract
        if manager.master_contract_file.exists():
            with open(manager.master_contract_file, 'r') as f:
                master_contract = json.load(f)
            
            print(f"\n📊 Master Contract Summary:")
            print(f"  Total Symbols: {master_contract.get('total_symbols', 0)}")
            print(f"  Total Brokers: {master_contract.get('total_brokers', 0)}")
            print(f"  Created: {master_contract.get('created_at', 'N/A')}")
            
            print(f"\n📋 Broker Breakdown:")
            for broker, info in master_contract.get('brokers', {}).items():
                count = info.get('count', 0)
                status = info.get('status', 'unknown')
                last_updated = info.get('last_updated', 'N/A')
                print(f"  • {broker}: {count} symbols ({status})")
                print(f"    Last Updated: {last_updated}")
    else:
        print(f"❌ Master contract creation failed")

def demo_real_world_scenario():
    """Demonstrate real-world usage scenario"""
    print("\n🌍 Real-World Scenario Demo")
    print("=" * 50)
    
    manager = BrokerSymbolManager()
    
    print(f"📈 Scenario: Trading RELIANCE across multiple brokers")
    
    symbol = "RELIANCE"
    
    # 1. Search for the symbol
    print(f"\n1️⃣ Searching for {symbol}...")
    results = manager.search_broker_symbols(symbol, limit=5)
    
    if results:
        print(f"   ✅ Found {symbol} in {len(results)} broker(s)")
    else:
        print(f"   ❌ {symbol} not found")
        return
    
    # 2. Get broker-specific mappings
    print(f"\n2️⃣ Getting broker-specific symbols for {symbol}:")
    
    broker_mappings = {}
    for broker in manager.broker_configs.keys():
        mapping = manager.get_broker_symbol_mapping(symbol, broker)
        if mapping:
            broker_mappings[broker] = mapping
            print(f"   • {broker}: {symbol} → {mapping}")
    
    # 3. Show how to use in trading
    print(f"\n3️⃣ Trading implementation example:")
    print(f"   ```python")
    print(f"   # Get broker-specific symbol")
    print(f"   fyers_symbol = manager.get_broker_symbol_mapping('{symbol}', 'fyers')")
    print(f"   dhan_symbol = manager.get_broker_symbol_mapping('{symbol}', 'dhan')")
    print(f"   ")
    print(f"   # Use in broker API calls")
    print(f"   if fyers_symbol:")
    print(f"       fyers_broker.place_order(symbol=fyers_symbol, ...)")
    print(f"   if dhan_symbol:")
    print(f"       dhan_broker.place_order(security_id=dhan_symbol, ...)")
    print(f"   ```")

def demo_cli_usage():
    """Show CLI usage examples"""
    print("\n💻 CLI Usage Examples")
    print("=" * 50)
    
    print(f"📖 Available CLI commands:")
    print(f"")
    print(f"🔧 Broker Management:")
    print(f"  python data/symbol_manager.py --register-broker fyers")
    print(f"  python data/symbol_manager.py --update-broker fyers")
    print(f"  python data/symbol_manager.py --list-brokers")
    print(f"  python data/symbol_manager.py --broker-status")
    print(f"")
    print(f"🔍 Symbol Operations:")
    print(f"  python data/symbol_manager.py --search RELIANCE")
    print(f"  python data/symbol_manager.py --search BANK --broker fyers")
    print(f"  python data/symbol_manager.py --mapping RELIANCE --broker fyers")
    print(f"")
    print(f"📋 Utilities:")
    print(f"  python data/symbol_manager.py --master-contract")
    print(f"  python data/symbol_manager.py --schedule")

def main():
    """Run the complete integration demo"""
    print("🚀 Broker Symbol Manager Integration Demo")
    print("=" * 60)
    
    try:
        # Run integration demos
        demo_fyers_integration()
        demo_dhan_integration()
        demo_symbol_mapping()
        demo_cross_broker_search()
        demo_master_contract()
        demo_real_world_scenario()
        demo_cli_usage()
        
        print(f"\n🎉 Integration demo completed successfully!")
        print(f"\n📁 Check these files:")
        print(f"  • data/symbols/broker_configs.json - Broker configurations")
        print(f"  • data/symbols/unified_symbol_mapping.json - Unified mappings")
        print(f"  • data/symbols/master_contract.json - Master contract")
        print(f"  • data/symbols/brokers/ - Broker-specific symbol files")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        logger.error(f"Demo error: {e}", exc_info=True)

if __name__ == "__main__":
    main()
