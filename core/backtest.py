"""
Backtest Module for RapidTrader CLI Integration

This module provides the interface between the CLI and the backtesting engine,
handling strategy loading, configuration, and result presentation.
"""

import logging
import json
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import importlib.util

from core.backtest_engine import Backtest<PERSON>ngine
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

logger = logging.getLogger(__name__)
console = Console()

def load_strategy(strategy_name: str, strategy_path: str, config: Dict[str, Any]):
    """
    Load a strategy class from file.

    Args:
        strategy_name: Name of the strategy
        strategy_path: Path to strategies directory
        config: Strategy configuration

    Returns:
        Strategy instance or None if loading failed
    """
    try:
        # Construct strategy file path
        strategy_file = Path(strategy_path) / f"{strategy_name}.py"

        if not strategy_file.exists():
            logger.error(f"Strategy file not found: {strategy_file}")
            return None

        # Load the strategy module
        spec = importlib.util.spec_from_file_location(strategy_name, strategy_file)
        if spec is None or spec.loader is None:
            logger.error(f"Could not load strategy spec from {strategy_file}")
            return None

        strategy_module = importlib.util.module_from_spec(spec)

        # Add strategy path to sys.path temporarily
        original_path = sys.path.copy()
        sys.path.insert(0, str(Path(strategy_path).parent))

        try:
            spec.loader.exec_module(strategy_module)
        finally:
            sys.path = original_path

        # Find strategy class (should match filename or be a common name)
        strategy_class = None
        for attr_name in dir(strategy_module):
            attr = getattr(strategy_module, attr_name)
            if (isinstance(attr, type) and
                hasattr(attr, 'analyze') and
                attr_name.lower() != 'basestrategy'):
                strategy_class = attr
                break

        if strategy_class is None:
            logger.error(f"No valid strategy class found in {strategy_file}")
            return None

        # Initialize strategy with config
        logger.debug(f"Initializing strategy {strategy_name} with config type: {type(config)}")
        strategy_instance = strategy_class(config)
        logger.info(f"Successfully loaded strategy: {strategy_name}")
        return strategy_instance

    except Exception as e:
        import traceback
        logger.error(f"Error loading strategy {strategy_name}: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None

def run_backtest(strategies: List[Dict[str, Any]], config_path: str,
                timerange: Optional[str] = None, timeframe: Optional[str] = None,
                optimized: bool = False) -> bool:
    """
    Run backtest for given strategies.

    Args:
        strategies: List of strategy configurations
        config_path: Path to configuration file
        timerange: Optional timerange override (YYYYMMDD-YYYYMMDD)
        timeframe: Optional timeframe override

    Returns:
        True if backtest completed successfully, False otherwise
    """
    try:
        # Load configuration
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Override timeframe if provided
        if timeframe:
            config['timeframe'] = timeframe

        # Parse timerange
        start_date, end_date = _parse_timerange(timerange, config)

        # Get symbols to backtest
        symbols = config.get('exchange', {}).get('pair_whitelist', [])
        if not symbols:
            console.print("[bold red]Error:[/bold red] No symbols specified in pair_whitelist")
            return False

        # Initialize backtest engine (optimized or standard)
        if optimized:
            from core.optimized_backtest_engine import OptimizedBacktestEngine
            backtest_engine = OptimizedBacktestEngine(config)
        else:
            backtest_engine = BacktestEngine(config)

        console.print(f"\n[bold green]Starting Backtest[/bold green]")
        console.print(f"Period: {start_date} to {end_date}")
        console.print(f"Timeframe: {config.get('timeframe', '1d')}")
        console.print(f"Symbols: {', '.join(symbols)}")
        console.print(f"Strategies: {len(strategies)}")

        all_results = {}

        # Run backtest for each strategy
        for strategy_config in strategies:
            strategy_name = strategy_config['name']

            with Progress(
                SpinnerColumn(),
                TextColumn(f"[progress.description]Running backtest for {strategy_name}..."),
                console=console
            ) as progress:
                task = progress.add_task("backtest", total=None)

                # Load strategy
                strategy_path = config.get('strategy_path', 'userdata/strategies/')
                strategy = load_strategy(strategy_name, strategy_path, config)

                if strategy is None:
                    console.print(f"[bold red]Error:[/bold red] Failed to load strategy {strategy_name}")
                    continue

                # Run backtest (use optimized method if available)
                if optimized and hasattr(backtest_engine, 'run_backtest_optimized'):
                    results = backtest_engine.run_backtest_optimized(
                        strategy=strategy,
                        symbols=symbols,
                        timeframe=config.get('timeframe', '1d'),
                        start_date=start_date,
                        end_date=end_date
                    )
                else:
                    results = backtest_engine.run_backtest(
                        strategy=strategy,
                        symbols=symbols,
                        timeframe=config.get('timeframe', '1d'),
                        start_date=start_date,
                        end_date=end_date
                    )

                all_results[strategy_name] = results
                progress.update(task, completed=True)

        # Display results
        _display_results(all_results)

        # Save results
        _save_results(all_results, config)

        return True

    except Exception as e:
        logger.error(f"Error running backtest: {e}")
        console.print(f"[bold red]Error:[/bold red] {e}")
        return False

def _parse_timerange(timerange: Optional[str], config: Dict[str, Any]) -> tuple:
    """
    Parse timerange from command line or config.

    Args:
        timerange: Timerange string (YYYYMMDD-YYYYMMDD)
        config: Configuration dictionary

    Returns:
        Tuple of (start_date, end_date)
    """
    if timerange:
        # Parse command line timerange
        if '-' in timerange:
            start_str, end_str = timerange.split('-')
            start_date = f"{start_str[:4]}-{start_str[4:6]}-{start_str[6:8]}"
            end_date = f"{end_str[:4]}-{end_str[4:6]}-{end_str[6:8]}"
        else:
            raise ValueError("Invalid timerange format. Use YYYYMMDD-YYYYMMDD")
    else:
        # Use config timerange
        backtest_config = config.get('backtest', {})
        start_date = backtest_config.get('start_date', '2024-01-01')
        end_date = backtest_config.get('end_date', '2024-12-31')

    return start_date, end_date

def _display_results(results: Dict[str, Dict[str, Any]]):
    """
    Display backtest results in a formatted table.

    Args:
        results: Dictionary of strategy results
    """
    if not results:
        console.print("[bold yellow]No results to display[/bold yellow]")
        return

    console.print("\n[bold blue]BACKTEST RESULTS[/bold blue]")

    # Create summary table
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Strategy")
    table.add_column("Total Trades")
    table.add_column("Win Rate %")
    table.add_column("Total Return %")
    table.add_column("Max Drawdown %")
    table.add_column("Profit Factor")

    for strategy_name, strategy_results in results.items():
        table.add_row(
            strategy_name,
            str(strategy_results.get('total_trades', 0)),
            f"{strategy_results.get('win_rate', 0):.1f}",
            f"{strategy_results.get('total_return_pct', 0):.2f}",
            f"{strategy_results.get('max_drawdown_pct', 0):.2f}",
            str(strategy_results.get('profit_factor', 'N/A'))
        )

    console.print(table)

    # Display detailed results for each strategy
    for strategy_name, strategy_results in results.items():
        console.print(f"\n[bold cyan]Detailed Results for {strategy_name}:[/bold cyan]")

        details_table = Table(show_header=True, header_style="bold green")
        details_table.add_column("Metric")
        details_table.add_column("Value")

        # Key metrics to display
        key_metrics = [
            ('Starting Balance', f"₹{strategy_results.get('starting_balance', 0):,.2f}"),
            ('Ending Balance', f"₹{strategy_results.get('ending_balance', 0):,.2f}"),
            ('Total P&L', f"₹{strategy_results.get('total_profit_loss', 0):,.2f}"),
            ('Best Trade', f"₹{strategy_results.get('best_trade', 0):,.2f}"),
            ('Worst Trade', f"₹{strategy_results.get('worst_trade', 0):,.2f}"),
            ('Average Trade', f"₹{strategy_results.get('avg_profit_loss', 0):,.2f}"),
            ('Total Fees', f"₹{strategy_results.get('total_fees', 0):,.2f}"),
            ('Avg Trade Duration', f"{strategy_results.get('avg_trade_duration_hours', 0):.1f} hours")
        ]

        for metric, value in key_metrics:
            details_table.add_row(metric, value)

        console.print(details_table)

def _save_results(results: Dict[str, Dict[str, Any]], config: Dict[str, Any]):
    """
    Save backtest results to file.

    Args:
        results: Dictionary of strategy results
        config: Configuration dictionary
    """
    try:
        # Get results file path from config
        results_file = config.get('backtest', {}).get('results_file', 'userdata/results/backtest_results.json')

        # Ensure results directory exists
        results_path = Path(results_file)
        results_path.parent.mkdir(parents=True, exist_ok=True)

        # Add timestamp to results
        timestamp = datetime.now().isoformat()
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'results': results
        }

        # Save results
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2, default=str)

        console.print(f"\n[bold green]Results saved to:[/bold green] {results_file}")

    except Exception as e:
        logger.error(f"Error saving results: {e}")
        console.print(f"[bold red]Error saving results:[/bold red] {e}")

def show_backtest_results(strategy: Optional[str] = None):
    """
    Show saved backtest results.

    Args:
        strategy: Optional strategy name to filter results
    """
    try:
        results_file = "userdata/results/backtest_results.json"

        if not Path(results_file).exists():
            console.print("[bold yellow]No backtest results found[/bold yellow]")
            return

        with open(results_file, 'r') as f:
            saved_results = json.load(f)

        results = saved_results.get('results', {})

        if strategy and strategy in results:
            results = {strategy: results[strategy]}

        _display_results(results)

    except Exception as e:
        logger.error(f"Error loading results: {e}")
        console.print(f"[bold red]Error loading results:[/bold red] {e}")