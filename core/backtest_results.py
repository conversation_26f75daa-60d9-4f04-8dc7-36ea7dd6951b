"""
Backtest Results Analysis for RapidTrader

This module provides comprehensive analysis and metrics calculation
for backtesting results, similar to FreqTrade's approach.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class BacktestResults:
    """
    Analyzes and calculates comprehensive metrics for backtesting results.
    
    Provides detailed performance metrics, risk analysis, and trade statistics
    similar to FreqTrade's backtesting results.
    """
    
    def __init__(self):
        """Initialize the results analyzer."""
        self.trades_df = None
        self.metrics = {}
    
    def reset(self):
        """Reset results for new backtest."""
        self.trades_df = None
        self.metrics = {}
    
    def calculate_metrics(self, trades: List[Dict[str, Any]], starting_balance: float,
                         start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Calculate comprehensive backtest metrics.
        
        Args:
            trades: List of completed trades
            starting_balance: Starting balance
            start_date: Backtest start date
            end_date: Backtest end date
            
        Returns:
            Dictionary with all calculated metrics
        """
        if not trades:
            return self._empty_results(starting_balance, start_date, end_date)
        
        # Convert trades to DataFrame for analysis
        self.trades_df = pd.DataFrame(trades)
        
        # Calculate basic metrics
        total_trades = len(trades)
        winning_trades = len(self.trades_df[self.trades_df['profit_loss'] > 0])
        losing_trades = len(self.trades_df[self.trades_df['profit_loss'] < 0])
        
        # Calculate returns
        total_profit_loss = self.trades_df['profit_loss'].sum()
        total_return_pct = (total_profit_loss / starting_balance) * 100
        
        # Win rate
        win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
        
        # Average trade metrics
        avg_profit_loss = self.trades_df['profit_loss'].mean()
        avg_profit_loss_pct = self.trades_df['profit_loss_pct'].mean()
        
        # Best and worst trades
        best_trade = self.trades_df['profit_loss'].max()
        worst_trade = self.trades_df['profit_loss'].min()
        best_trade_pct = self.trades_df['profit_loss_pct'].max()
        worst_trade_pct = self.trades_df['profit_loss_pct'].min()
        
        # Winning vs losing trade averages
        winning_trades_df = self.trades_df[self.trades_df['profit_loss'] > 0]
        losing_trades_df = self.trades_df[self.trades_df['profit_loss'] < 0]
        
        avg_winning_trade = winning_trades_df['profit_loss'].mean() if not winning_trades_df.empty else 0
        avg_losing_trade = losing_trades_df['profit_loss'].mean() if not losing_trades_df.empty else 0
        
        # Risk metrics
        profit_factor = abs(winning_trades_df['profit_loss'].sum() / losing_trades_df['profit_loss'].sum()) if not losing_trades_df.empty and losing_trades_df['profit_loss'].sum() != 0 else float('inf')
        
        # Drawdown analysis
        drawdown_metrics = self._calculate_drawdown(starting_balance)
        
        # Time-based metrics
        time_metrics = self._calculate_time_metrics(start_date, end_date)
        
        # Trade duration analysis
        duration_metrics = self._calculate_duration_metrics()
        
        # Compile all metrics
        self.metrics = {
            # Basic metrics
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': round(win_rate, 2),
            
            # Return metrics
            'starting_balance': starting_balance,
            'ending_balance': starting_balance + total_profit_loss,
            'total_profit_loss': round(total_profit_loss, 2),
            'total_return_pct': round(total_return_pct, 2),
            
            # Trade performance
            'avg_profit_loss': round(avg_profit_loss, 2),
            'avg_profit_loss_pct': round(avg_profit_loss_pct, 2),
            'best_trade': round(best_trade, 2),
            'worst_trade': round(worst_trade, 2),
            'best_trade_pct': round(best_trade_pct, 2),
            'worst_trade_pct': round(worst_trade_pct, 2),
            
            # Winning vs losing
            'avg_winning_trade': round(avg_winning_trade, 2),
            'avg_losing_trade': round(avg_losing_trade, 2),
            
            # Risk metrics
            'profit_factor': round(profit_factor, 2) if profit_factor != float('inf') else 'N/A',
            'expectancy': round(avg_profit_loss, 2),
            
            # Fees
            'total_fees': round(self.trades_df['total_fees'].sum(), 2),
            
            # Time and duration
            **time_metrics,
            **duration_metrics,
            **drawdown_metrics
        }
        
        return self.metrics
    
    def _empty_results(self, starting_balance: float, start_date: str, end_date: str) -> Dict[str, Any]:
        """Return empty results when no trades were made."""
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0,
            'starting_balance': starting_balance,
            'ending_balance': starting_balance,
            'total_profit_loss': 0,
            'total_return_pct': 0,
            'avg_profit_loss': 0,
            'avg_profit_loss_pct': 0,
            'best_trade': 0,
            'worst_trade': 0,
            'best_trade_pct': 0,
            'worst_trade_pct': 0,
            'avg_winning_trade': 0,
            'avg_losing_trade': 0,
            'profit_factor': 'N/A',
            'expectancy': 0,
            'total_fees': 0,
            'backtest_start': start_date,
            'backtest_end': end_date,
            'avg_trade_duration_hours': 0,
            'max_drawdown': 0,
            'max_drawdown_pct': 0
        }
    
    def _calculate_drawdown(self, starting_balance: float) -> Dict[str, Any]:
        """Calculate drawdown metrics."""
        if self.trades_df is None or self.trades_df.empty:
            return {'max_drawdown': 0, 'max_drawdown_pct': 0}
        
        # Calculate running balance
        self.trades_df['running_balance'] = starting_balance + self.trades_df['profit_loss'].cumsum()
        
        # Calculate running maximum (peak)
        self.trades_df['running_max'] = self.trades_df['running_balance'].expanding().max()
        
        # Calculate drawdown
        self.trades_df['drawdown'] = self.trades_df['running_balance'] - self.trades_df['running_max']
        self.trades_df['drawdown_pct'] = (self.trades_df['drawdown'] / self.trades_df['running_max']) * 100
        
        max_drawdown = self.trades_df['drawdown'].min()
        max_drawdown_pct = self.trades_df['drawdown_pct'].min()
        
        return {
            'max_drawdown': round(max_drawdown, 2),
            'max_drawdown_pct': round(max_drawdown_pct, 2)
        }
    
    def _calculate_time_metrics(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """Calculate time-based metrics."""
        return {
            'backtest_start': start_date,
            'backtest_end': end_date
        }
    
    def _calculate_duration_metrics(self) -> Dict[str, Any]:
        """Calculate trade duration metrics."""
        if self.trades_df is None or self.trades_df.empty:
            return {'avg_trade_duration_hours': 0}
        
        avg_duration_hours = self.trades_df['duration_hours'].mean()
        
        return {
            'avg_trade_duration_hours': round(avg_duration_hours, 2)
        }
    
    def save_results(self, filepath: str):
        """
        Save backtest results to file.
        
        Args:
            filepath: Path to save results
        """
        try:
            with open(filepath, 'w') as f:
                json.dump(self.metrics, f, indent=2, default=str)
            logger.info(f"Backtest results saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving results to {filepath}: {e}")
    
    def get_trades_dataframe(self) -> Optional[pd.DataFrame]:
        """
        Get trades as DataFrame for further analysis.
        
        Returns:
            DataFrame with all trades or None if no trades
        """
        return self.trades_df
    
    def print_summary(self):
        """Print a summary of backtest results."""
        if not self.metrics:
            print("No backtest results available")
            return
        
        print("\n" + "="*50)
        print("BACKTEST RESULTS SUMMARY")
        print("="*50)
        
        print(f"Total Trades: {self.metrics['total_trades']}")
        print(f"Winning Trades: {self.metrics['winning_trades']}")
        print(f"Losing Trades: {self.metrics['losing_trades']}")
        print(f"Win Rate: {self.metrics['win_rate']}%")
        
        print(f"\nStarting Balance: ₹{self.metrics['starting_balance']:,.2f}")
        print(f"Ending Balance: ₹{self.metrics['ending_balance']:,.2f}")
        print(f"Total P&L: ₹{self.metrics['total_profit_loss']:,.2f}")
        print(f"Total Return: {self.metrics['total_return_pct']}%")
        
        print(f"\nBest Trade: ₹{self.metrics['best_trade']:,.2f} ({self.metrics['best_trade_pct']}%)")
        print(f"Worst Trade: ₹{self.metrics['worst_trade']:,.2f} ({self.metrics['worst_trade_pct']}%)")
        print(f"Average Trade: ₹{self.metrics['avg_profit_loss']:,.2f} ({self.metrics['avg_profit_loss_pct']}%)")
        
        print(f"\nMax Drawdown: ₹{self.metrics['max_drawdown']:,.2f} ({self.metrics['max_drawdown_pct']}%)")
        print(f"Profit Factor: {self.metrics['profit_factor']}")
        print(f"Total Fees: ₹{self.metrics['total_fees']:,.2f}")
        
        print("="*50)
