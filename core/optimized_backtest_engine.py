"""
Optimized RapidTrader Backtesting Engine

This module provides a high-performance backtesting engine optimized for speed and memory usage.
Key optimizations:
- Vectorized operations throughout
- Memory-efficient data handling
- Minimal data copying
- Optimized trade simulation
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import json
import gc

from core.trade_simulator import TradeSimulator
from core.backtest_results import BacktestResults
from core.data_manager import DataManager

logger = logging.getLogger(__name__)

class OptimizedBacktestEngine:
    """
    High-performance backtesting engine for RapidTrader.
    
    Optimizations:
    - Vectorized operations for all calculations
    - Memory-efficient data handling
    - Minimal DataFrame copying
    - Batch processing of trades
    - Optimized data types
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the optimized backtesting engine.
        
        Args:
            config: Backtesting configuration
        """
        self.config = config
        self.trade_simulator = TradeSimulator(config)
        self.results = BacktestResults()
        self.data_manager = DataManager(config)
        
        # Backtesting parameters
        self.stake_amount = config.get("stake_amount", 1000)
        self.stake_currency = config.get("stake_currency", "INR")
        self.max_open_trades = config.get("max_open_trades", 3)
        self.starting_balance = config.get("dry_run_wallet", 100000)
        
        # Fee configuration
        self.fee = config.get("fee", 0.001)  # 0.1% default fee
        self.slippage = config.get("slippage", 0.0005)  # 0.05% default slippage
        
        # Data directory
        self.data_dir = Path(config.get("datadir", "userdata/historical_data"))
        
        # Performance optimizations
        self._data_cache = {}  # Cache loaded data
        self._indicator_cache = {}  # Cache calculated indicators
        
        logger.info(f"OptimizedBacktestEngine initialized with starting balance: {self.starting_balance} {self.stake_currency}")
    
    def load_data_optimized(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        Load historical data with optimizations for memory and speed.
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe (1m, 5m, 1h, 1d)
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            DataFrame with OHLCV data or None if not found
        """
        # Check cache first
        cache_key = f"{symbol}_{timeframe}_{start_date}_{end_date}"
        if cache_key in self._data_cache:
            return self._data_cache[cache_key]
        
        # Construct file path
        data_file = self.data_dir / f"{symbol}_{timeframe}.csv"
        
        if not data_file.exists():
            logger.error(f"Data file not found: {data_file}")
            return None
        
        try:
            # Load data with optimized dtypes
            dtype_dict = {
                'open': 'float32',
                'high': 'float32', 
                'low': 'float32',
                'close': 'float32',
                'volume': 'int32'
            }
            
            df = pd.read_csv(data_file, dtype=dtype_dict, parse_dates=['date'])
            
            # Set index efficiently
            df.set_index('date', inplace=True)
            
            # Filter by date range efficiently
            if start_date:
                start_dt = pd.to_datetime(start_date)
                df = df.loc[df.index >= start_dt]
            
            if end_date:
                end_dt = pd.to_datetime(end_date)
                df = df.loc[df.index <= end_dt]
            
            if df.empty:
                logger.warning(f"No data found for {symbol} in date range {start_date} to {end_date}")
                return None
            
            # Cache the data
            self._data_cache[cache_key] = df
            
            logger.info(f"Loaded {len(df)} candles for {symbol} from {df.index[0]} to {df.index[-1]}")
            return df
            
        except Exception as e:
            logger.error(f"Error loading data from {data_file}: {e}")
            return None
    
    def run_backtest_optimized(self, strategy, symbols: List[str], timeframe: str, 
                              start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Run optimized backtest for given strategy and symbols.
        
        Args:
            strategy: Strategy instance
            symbols: List of symbols to backtest
            timeframe: Data timeframe
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            Backtest results dictionary
        """
        logger.info(f"Starting optimized backtest for {len(symbols)} symbols from {start_date} to {end_date}")
        
        # Ensure data is available for all symbols
        logger.info("Checking data availability...")
        if not self.data_manager.ensure_data_available(symbols, timeframe, start_date, end_date):
            logger.warning("Some data could not be downloaded, proceeding with available data")
        
        # Initialize results
        self.results.reset()
        self.trade_simulator.reset(self.starting_balance)
        
        # Load all data at once for better memory management
        all_data = {}
        for symbol in symbols:
            data = self.load_data_optimized(symbol, timeframe, start_date, end_date)
            if data is not None:
                all_data[symbol] = data
            else:
                logger.warning(f"Skipping {symbol} - no data available")
        
        if not all_data:
            logger.error("No data available for any symbols")
            return self._calculate_results([], start_date, end_date)
        
        # Run vectorized analysis for all symbols
        all_trades = self._run_vectorized_backtest(strategy, all_data)
        
        # Calculate final results
        final_results = self._calculate_results(all_trades, start_date, end_date)
        
        # Clear caches to free memory
        self._clear_caches()
        
        logger.info(f"Optimized backtest completed. Total trades: {len(all_trades)}")
        return final_results
    
    def _run_vectorized_backtest(self, strategy, all_data: Dict[str, pd.DataFrame]) -> List[Dict[str, Any]]:
        """
        Run vectorized backtest across all symbols.
        
        Args:
            strategy: Strategy instance
            all_data: Dictionary of symbol -> DataFrame
            
        Returns:
            List of executed trades
        """
        all_trades = []
        
        # Process each symbol with vectorized operations
        for symbol, data in all_data.items():
            logger.info(f"Processing {symbol} with vectorized operations...")
            
            try:
                # Analyze data in-place to avoid copying
                analyzed_data = strategy.analyze(data)
                
                # Run vectorized trade simulation
                symbol_trades = self._simulate_trades_vectorized(symbol, analyzed_data)
                all_trades.extend(symbol_trades)
                
                # Force garbage collection after each symbol
                del analyzed_data
                gc.collect()
                
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                continue
        
        return all_trades
    
    def _simulate_trades_vectorized(self, symbol: str, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Vectorized trade simulation for maximum performance.
        
        Args:
            symbol: Trading symbol
            data: Analyzed dataframe with signals
            
        Returns:
            List of executed trades
        """
        trades = []
        
        # Check if required signal columns exist
        if 'buy_signal' not in data.columns or 'sell_signal' not in data.columns:
            logger.warning(f"No buy/sell signals found for {symbol}")
            return trades
        
        # Convert to numpy arrays for faster processing
        timestamps = data.index.values
        prices = data['close'].values
        buy_signals = data['buy_signal'].values
        sell_signals = data['sell_signal'].values
        
        # Vectorized signal processing
        buy_indices = np.where(buy_signals == 1)[0]
        sell_indices = np.where(sell_signals == 1)[0]
        
        if len(buy_indices) == 0:
            logger.info(f"No buy signals found for {symbol}")
            return trades
        
        # Simulate trades using vectorized operations
        open_position = None
        
        # Create combined signal array for efficient processing
        signal_indices = np.concatenate([buy_indices, sell_indices])
        signal_types = np.concatenate([np.ones(len(buy_indices)), np.zeros(len(sell_indices))])
        
        # Sort by timestamp
        sort_order = np.argsort(signal_indices)
        signal_indices = signal_indices[sort_order]
        signal_types = signal_types[sort_order]
        
        for i, signal_type in zip(signal_indices, signal_types):
            timestamp = pd.to_datetime(timestamps[i])
            price = float(prices[i])
            
            if signal_type == 1 and open_position is None:  # Buy signal
                if self.trade_simulator.can_open_trade():
                    open_position = self._create_trade_fast(symbol, timestamp, price)
            
            elif signal_type == 0 and open_position is not None:  # Sell signal
                closed_trade = self._close_trade_fast(open_position, timestamp, price)
                if closed_trade:
                    trades.append(closed_trade)
                open_position = None
        
        # Close any remaining open position
        if open_position is not None:
            final_timestamp = pd.to_datetime(timestamps[-1])
            final_price = float(prices[-1])
            closed_trade = self._close_trade_fast(open_position, final_timestamp, final_price)
            if closed_trade:
                trades.append(closed_trade)
        
        return trades
    
    def _create_trade_fast(self, symbol: str, timestamp: pd.Timestamp, price: float) -> Optional[Dict[str, Any]]:
        """
        Fast trade creation with minimal overhead.
        
        Args:
            symbol: Trading symbol
            timestamp: Entry timestamp
            price: Entry price
            
        Returns:
            Trade dictionary or None
        """
        quantity = max(1, int(self.stake_amount / price))
        
        # Apply slippage
        entry_price = price * (1 + self.slippage)
        
        # Calculate costs
        trade_value = entry_price * quantity
        entry_fee = trade_value * self.fee
        total_cost = trade_value + entry_fee
        
        # Check balance
        if total_cost > self.trade_simulator.balance:
            return None
        
        # Create trade record
        trade = {
            'symbol': symbol,
            'entry_timestamp': timestamp,
            'entry_price': entry_price,
            'quantity': quantity,
            'entry_value': trade_value,
            'entry_fee': entry_fee,
            'status': 'open'
        }
        
        # Update balance
        self.trade_simulator.balance -= total_cost
        self.trade_simulator.total_fees_paid += entry_fee
        
        return trade
    
    def _close_trade_fast(self, trade: Dict[str, Any], timestamp: pd.Timestamp, price: float) -> Optional[Dict[str, Any]]:
        """
        Fast trade closing with minimal overhead.
        
        Args:
            trade: Open trade dictionary
            timestamp: Exit timestamp
            price: Exit price
            
        Returns:
            Completed trade dictionary
        """
        # Apply slippage
        exit_price = price * (1 - self.slippage)
        
        # Calculate exit values
        exit_value = exit_price * trade['quantity']
        exit_fee = exit_value * self.fee
        net_proceeds = exit_value - exit_fee
        
        # Calculate P&L
        total_cost = trade['entry_value'] + trade['entry_fee']
        profit_loss = net_proceeds - total_cost
        profit_loss_pct = (profit_loss / total_cost) * 100 if total_cost > 0 else 0
        
        # Update trade record
        trade.update({
            'exit_timestamp': timestamp,
            'exit_price': exit_price,
            'exit_value': exit_value,
            'exit_fee': exit_fee,
            'total_fees': trade['entry_fee'] + exit_fee,
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct,
            'status': 'closed',
            'duration_hours': (timestamp - trade['entry_timestamp']).total_seconds() / 3600
        })
        
        # Update balance
        self.trade_simulator.balance += net_proceeds
        self.trade_simulator.total_fees_paid += exit_fee
        
        return trade
    
    def _calculate_results(self, trades: List[Dict[str, Any]], 
                          start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Calculate comprehensive backtest results.
        
        Args:
            trades: List of executed trades
            start_date: Backtest start date
            end_date: Backtest end date
            
        Returns:
            Results dictionary
        """
        return self.results.calculate_metrics(
            trades=trades,
            starting_balance=self.starting_balance,
            start_date=start_date,
            end_date=end_date
        )
    
    def _clear_caches(self):
        """Clear all caches to free memory."""
        self._data_cache.clear()
        self._indicator_cache.clear()
        gc.collect()
        logger.debug("Cleared all caches and forced garbage collection")
