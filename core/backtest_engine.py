"""
RapidTrader Backtesting Engine

This module provides a fast, vectorized backtesting engine similar to FreqTrade's approach.
It uses pandas for vectorized operations to achieve high performance while maintaining accuracy.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import json

from core.trade_simulator import TradeSimulator
from core.backtest_results import BacktestResults
from core.data_manager import DataManager

logger = logging.getLogger(__name__)

class BacktestEngine:
    """
    Vectorized backtesting engine for RapidTrader.

    This engine processes historical data using pandas vectorized operations
    for maximum performance while ensuring realistic trade execution.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the backtesting engine.

        Args:
            config: Backtesting configuration
        """
        self.config = config
        self.trade_simulator = TradeSimulator(config)
        self.results = BacktestResults()
        self.data_manager = DataManager(config)

        # Backtesting parameters
        self.stake_amount = config.get("stake_amount", 1000)
        self.stake_currency = config.get("stake_currency", "INR")
        self.max_open_trades = config.get("max_open_trades", 3)
        self.starting_balance = config.get("dry_run_wallet", 100000)

        # Fee configuration
        self.fee = config.get("fee", 0.001)  # 0.1% default fee

        # Data directory
        self.data_dir = Path(config.get("datadir", "userdata/historical_data"))

        logger.info(f"BacktestEngine initialized with starting balance: {self.starting_balance} {self.stake_currency}")

    def load_data(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """
        Load historical data for backtesting.

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe (1m, 5m, 1h, 1d)
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)

        Returns:
            DataFrame with OHLCV data or None if not found
        """
        # Construct file path based on RapidTrader's data structure
        data_file = self.data_dir / f"{symbol}_{timeframe}.csv"

        if not data_file.exists():
            logger.error(f"Data file not found: {data_file}")
            return None

        try:
            # Load data
            df = pd.read_csv(data_file)

            # Ensure proper column names
            expected_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in expected_columns):
                logger.error(f"Missing required columns in {data_file}. Expected: {expected_columns}")
                return None

            # Convert date column to datetime
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)

            # Filter by date range
            if start_date:
                start_dt = pd.to_datetime(start_date)
                df = df[df.index >= start_dt]

            if end_date:
                end_dt = pd.to_datetime(end_date)
                df = df[df.index <= end_dt]

            if df.empty:
                logger.warning(f"No data found for {symbol} in date range {start_date} to {end_date}")
                return None

            logger.info(f"Loaded {len(df)} candles for {symbol} from {df.index[0]} to {df.index[-1]}")
            return df

        except Exception as e:
            logger.error(f"Error loading data from {data_file}: {e}")
            return None

    def run_backtest(self, strategy, symbols: List[str], timeframe: str,
                    start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Run backtest for given strategy and symbols.

        Args:
            strategy: Strategy instance
            symbols: List of symbols to backtest
            timeframe: Data timeframe
            start_date: Start date for backtesting
            end_date: End date for backtesting

        Returns:
            Backtest results dictionary
        """
        logger.info(f"Starting backtest for {len(symbols)} symbols from {start_date} to {end_date}")

        # Ensure data is available for all symbols
        logger.info("Checking data availability...")
        if not self.data_manager.ensure_data_available(symbols, timeframe, start_date, end_date):
            logger.warning("Some data could not be downloaded, proceeding with available data")

        # Initialize results
        self.results.reset()
        self.trade_simulator.reset(self.starting_balance)

        all_trades = []

        for symbol in symbols:
            logger.info(f"Backtesting {symbol}...")

            # Load data for this symbol
            data = self.load_data(symbol, timeframe, start_date, end_date)
            if data is None:
                logger.warning(f"Skipping {symbol} - no data available")
                continue

            # Run strategy analysis on the data
            try:
                analyzed_data = strategy.analyze(data.copy())

                # Simulate trades for this symbol
                symbol_trades = self._simulate_trades(symbol, analyzed_data)
                all_trades.extend(symbol_trades)

            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue

        # Calculate final results
        final_results = self._calculate_results(all_trades, start_date, end_date)

        logger.info(f"Backtest completed. Total trades: {len(all_trades)}")
        return final_results

    def _simulate_trades(self, symbol: str, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Simulate trades for a single symbol using vectorized operations.

        Args:
            symbol: Trading symbol
            data: Analyzed dataframe with signals

        Returns:
            List of executed trades
        """
        trades = []

        # Check if required signal columns exist
        if 'buy_signal' not in data.columns or 'sell_signal' not in data.columns:
            logger.warning(f"No buy/sell signals found for {symbol}")
            return trades

        # Get buy and sell signals
        buy_signals = data[data['buy_signal'] == 1].copy()
        sell_signals = data[data['sell_signal'] == 1].copy()

        if buy_signals.empty:
            logger.info(f"No buy signals found for {symbol}")
            return trades

        # Simulate trade execution
        open_position = None

        for timestamp, row in data.iterrows():
            # Check for buy signal
            if row.get('buy_signal', 0) == 1 and open_position is None:
                # Check if we can open a new trade
                if self.trade_simulator.can_open_trade():
                    open_position = self.trade_simulator.open_trade(
                        symbol=symbol,
                        timestamp=timestamp,
                        price=row['close'],  # Use close price for entry
                        quantity=self._calculate_quantity(row['close'])
                    )

            # Check for sell signal or stop loss
            elif row.get('sell_signal', 0) == 1 and open_position is not None:
                # Close the position
                closed_trade = self.trade_simulator.close_trade(
                    open_position,
                    timestamp=timestamp,
                    price=row['close']  # Use close price for exit
                )
                if closed_trade:
                    trades.append(closed_trade)
                open_position = None

        # Close any remaining open position at the end
        if open_position is not None:
            final_timestamp = data.index[-1]
            final_price = data.iloc[-1]['close']
            closed_trade = self.trade_simulator.close_trade(
                open_position,
                timestamp=final_timestamp,
                price=final_price
            )
            if closed_trade:
                trades.append(closed_trade)

        return trades

    def _calculate_quantity(self, price: float) -> int:
        """
        Calculate quantity to buy based on stake amount.

        Args:
            price: Entry price

        Returns:
            Quantity to buy
        """
        if price <= 0:
            return 0

        quantity = int(self.stake_amount / price)
        return max(1, quantity)  # Minimum 1 share

    def _calculate_results(self, trades: List[Dict[str, Any]],
                          start_date: str, end_date: str) -> Dict[str, Any]:
        """
        Calculate comprehensive backtest results.

        Args:
            trades: List of executed trades
            start_date: Backtest start date
            end_date: Backtest end date

        Returns:
            Results dictionary
        """
        return self.results.calculate_metrics(
            trades=trades,
            starting_balance=self.starting_balance,
            start_date=start_date,
            end_date=end_date
        )
