"""
Market Hours Manager for RapidTrader

This module manages trading hours for different exchanges and provides
automatic pause/resume functionality for trading containers.

Features:
- Exchange-specific market hours
- Automatic pause/resume of trading
- Market status checking
- Holiday calendar support
- User interaction for after-hours trading
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Tuple
import pytz
from enum import Enum

logger = logging.getLogger(__name__)

class MarketStatus(Enum):
    """Market status enumeration."""
    OPEN = "open"
    CLOSED = "closed"
    PRE_MARKET = "pre_market"
    POST_MARKET = "post_market"
    HOLIDAY = "holiday"

class ExchangeConfig:
    """Configuration for exchange trading hours."""
    
    def __init__(self, 
                 name: str,
                 timezone: str,
                 market_open: str,
                 market_close: str,
                 pre_market_start: Optional[str] = None,
                 post_market_end: Optional[str] = None,
                 trading_days: List[int] = None):
        """
        Initialize exchange configuration.
        
        Args:
            name: Exchange name (e.g., "NSE", "BSE")
            timezone: Exchange timezone (e.g., "Asia/Kolkata")
            market_open: Market open time (HH:MM format)
            market_close: Market close time (HH:MM format)
            pre_market_start: Pre-market start time
            post_market_end: Post-market end time
            trading_days: List of trading days (0=Monday, 6=Sunday)
        """
        self.name = name
        self.timezone = pytz.timezone(timezone)
        self.market_open = market_open
        self.market_close = market_close
        self.pre_market_start = pre_market_start
        self.post_market_end = post_market_end
        self.trading_days = trading_days or [0, 1, 2, 3, 4]  # Monday to Friday

# Predefined exchange configurations
EXCHANGE_CONFIGS = {
    "NSE": ExchangeConfig(
        name="NSE",
        timezone="Asia/Kolkata",
        market_open="09:15",
        market_close="15:30",
        pre_market_start="09:00",
        post_market_end="16:00",
        trading_days=[0, 1, 2, 3, 4]  # Monday to Friday
    ),
    "BSE": ExchangeConfig(
        name="BSE", 
        timezone="Asia/Kolkata",
        market_open="09:15",
        market_close="15:30",
        pre_market_start="09:00",
        post_market_end="16:00",
        trading_days=[0, 1, 2, 3, 4]
    ),
    "NYSE": ExchangeConfig(
        name="NYSE",
        timezone="America/New_York", 
        market_open="09:30",
        market_close="16:00",
        pre_market_start="04:00",
        post_market_end="20:00",
        trading_days=[0, 1, 2, 3, 4]
    ),
    "NASDAQ": ExchangeConfig(
        name="NASDAQ",
        timezone="America/New_York",
        market_open="09:30", 
        market_close="16:00",
        pre_market_start="04:00",
        post_market_end="20:00",
        trading_days=[0, 1, 2, 3, 4]
    )
}

class MarketHoursManager:
    """Manages market hours and trading session control."""
    
    def __init__(self, exchange: str = "NSE"):
        """
        Initialize market hours manager.
        
        Args:
            exchange: Exchange name (default: NSE)
        """
        self.exchange = exchange.upper()
        self.config = EXCHANGE_CONFIGS.get(self.exchange)
        
        if not self.config:
            raise ValueError(f"Unsupported exchange: {exchange}")
        
        self.is_monitoring = False
        self.monitor_thread = None
        self.pause_callbacks = []
        self.resume_callbacks = []
        self.status_callbacks = []
        
        # Current status
        self.current_status = MarketStatus.CLOSED
        self.last_status_check = None
        
        logger.info(f"MarketHoursManager initialized for {self.exchange}")
    
    def get_current_status(self) -> Tuple[MarketStatus, Dict[str, any]]:
        """
        Get current market status.
        
        Returns:
            Tuple of (status, details)
        """
        now = datetime.now(self.config.timezone)
        
        # Check if it's a trading day
        if now.weekday() not in self.config.trading_days:
            return MarketStatus.HOLIDAY, {
                "reason": "Weekend",
                "next_open": self._get_next_trading_day(now)
            }
        
        # Parse time strings
        market_open = self._parse_time(now, self.config.market_open)
        market_close = self._parse_time(now, self.config.market_close)
        
        # Check market status
        if market_open <= now <= market_close:
            return MarketStatus.OPEN, {
                "opened_at": market_open,
                "closes_at": market_close,
                "time_to_close": market_close - now
            }
        
        # Check pre-market
        if self.config.pre_market_start:
            pre_market_start = self._parse_time(now, self.config.pre_market_start)
            if pre_market_start <= now < market_open:
                return MarketStatus.PRE_MARKET, {
                    "opens_at": market_open,
                    "time_to_open": market_open - now
                }
        
        # Check post-market
        if self.config.post_market_end:
            post_market_end = self._parse_time(now, self.config.post_market_end)
            if market_close < now <= post_market_end:
                return MarketStatus.POST_MARKET, {
                    "closed_at": market_close,
                    "post_market_ends": post_market_end
                }
        
        # Market is closed
        next_open = self._get_next_market_open(now)
        return MarketStatus.CLOSED, {
            "closed_at": market_close if now.date() == market_close.date() else None,
            "next_open": next_open,
            "time_to_open": next_open - now if next_open else None
        }
    
    def _parse_time(self, base_date: datetime, time_str: str) -> datetime:
        """Parse time string and create datetime object."""
        hour, minute = map(int, time_str.split(':'))
        return base_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
    
    def _get_next_trading_day(self, current_time: datetime) -> datetime:
        """Get next trading day."""
        next_day = current_time + timedelta(days=1)
        while next_day.weekday() not in self.config.trading_days:
            next_day += timedelta(days=1)
        return self._parse_time(next_day, self.config.market_open)
    
    def _get_next_market_open(self, current_time: datetime) -> Optional[datetime]:
        """Get next market open time."""
        # If today is a trading day and market hasn't opened yet
        if current_time.weekday() in self.config.trading_days:
            market_open_today = self._parse_time(current_time, self.config.market_open)
            if current_time < market_open_today:
                return market_open_today
        
        # Otherwise, get next trading day
        return self._get_next_trading_day(current_time)
    
    def is_market_open(self) -> bool:
        """Check if market is currently open."""
        status, _ = self.get_current_status()
        return status == MarketStatus.OPEN
    
    def add_pause_callback(self, callback: Callable[[], None]):
        """Add callback to be called when market closes."""
        self.pause_callbacks.append(callback)
    
    def add_resume_callback(self, callback: Callable[[], None]):
        """Add callback to be called when market opens."""
        self.resume_callbacks.append(callback)
    
    def add_status_callback(self, callback: Callable[[MarketStatus, Dict], None]):
        """Add callback to be called on status changes."""
        self.status_callbacks.append(callback)
    
    def start_monitoring(self, check_interval: int = 60):
        """
        Start monitoring market hours.
        
        Args:
            check_interval: Check interval in seconds
        """
        if self.is_monitoring:
            logger.warning("Market hours monitoring already started")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(check_interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info(f"Started market hours monitoring for {self.exchange}")
    
    def stop_monitoring(self):
        """Stop monitoring market hours."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Stopped market hours monitoring")
    
    def _monitor_loop(self, check_interval: int):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                status, details = self.get_current_status()
                
                # Check for status change
                if status != self.current_status:
                    logger.info(f"Market status changed: {self.current_status.value} -> {status.value}")
                    
                    # Call status callbacks
                    for callback in self.status_callbacks:
                        try:
                            callback(status, details)
                        except Exception as e:
                            logger.error(f"Error in status callback: {e}")
                    
                    # Handle pause/resume
                    if self.current_status == MarketStatus.OPEN and status != MarketStatus.OPEN:
                        # Market just closed
                        logger.info("Market closed - triggering pause callbacks")
                        for callback in self.pause_callbacks:
                            try:
                                callback()
                            except Exception as e:
                                logger.error(f"Error in pause callback: {e}")
                    
                    elif self.current_status != MarketStatus.OPEN and status == MarketStatus.OPEN:
                        # Market just opened
                        logger.info("Market opened - triggering resume callbacks")
                        for callback in self.resume_callbacks:
                            try:
                                callback()
                            except Exception as e:
                                logger.error(f"Error in resume callback: {e}")
                    
                    self.current_status = status
                
                self.last_status_check = datetime.now(self.config.timezone)
                time.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"Error in market hours monitoring: {e}")
                time.sleep(check_interval)
    
    def get_market_info(self) -> Dict[str, any]:
        """Get comprehensive market information."""
        status, details = self.get_current_status()
        now = datetime.now(self.config.timezone)
        
        return {
            "exchange": self.exchange,
            "current_time": now.isoformat(),
            "status": status.value,
            "details": details,
            "config": {
                "market_open": self.config.market_open,
                "market_close": self.config.market_close,
                "pre_market_start": self.config.pre_market_start,
                "post_market_end": self.config.post_market_end,
                "timezone": str(self.config.timezone),
                "trading_days": self.config.trading_days
            }
        }
