"""
Dry Run Trading Engine for RapidTrader

This module implements paper trading functionality that simulates live trading
without using real money. It uses real market data and executes strategies
in real-time while tracking performance.
"""

import json
import logging
import signal
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
from pathlib import Path

# Import RapidTrader components
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from broker.dhan_wrapper import DhanBroker
from broker.fyers_wrapper import FyersBroker
from core.market_hours_manager import MarketHoursManager, MarketStatus

logger = logging.getLogger("dryrun")

class DryRunEngine:
    """
    Dry run trading engine that simulates live trading without real money.

    Features:
    - Real-time strategy execution
    - Simulated order placement and tracking
    - Performance monitoring and logging
    - Risk management
    - Multi-strategy support
    """

    def __init__(self, config_path: str):
        """
        Initialize the dry run engine.

        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.running = False
        self.paused = False
        self.strategies = {}
        self.positions = {}
        self.orders = {}
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'start_balance': 0.0,
            'current_balance': 0.0,
            'start_time': None,
            'last_update': None
        }

        # Market hours management
        exchange = self.config.get('broker', {}).get('exchange', 'NSE')
        self.market_manager = MarketHoursManager(exchange)
        self.market_manager.add_pause_callback(self._on_market_close)
        self.market_manager.add_resume_callback(self._on_market_open)
        self.market_manager.add_status_callback(self._on_market_status_change)

        # User preferences for after-hours trading
        self.allow_after_hours = False
        self.user_confirmed_after_hours = False

        # Initialize components
        self.broker = None
        self.data_manager = None

        # Setup logging
        self._setup_logging()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info("DryRunEngine initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)

            # Ensure dry_run is enabled
            if not config.get('dry_run', False):
                logger.warning("Configuration is not set for dry run mode")
                config['dry_run'] = True

            return config
        except Exception as e:
            logger.error(f"Failed to load config from {self.config_path}: {e}")
            raise

    def _setup_logging(self):
        """Setup logging for dry run mode."""
        # Create logs directory if it doesn't exist
        log_dir = Path("userdata/logs")
        log_dir.mkdir(parents=True, exist_ok=True)

        # Setup file handler for dry run logs
        log_file = log_dir / f"dryrun_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)

        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)

        # Add handler to logger
        logger.addHandler(file_handler)
        logger.info(f"Dry run logging initialized: {log_file}")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()

    def _on_market_close(self):
        """Callback when market closes - pause trading."""
        logger.info("🔴 Market closed - Pausing trading operations")
        self.paused = True

    def _on_market_open(self):
        """Callback when market opens - resume trading."""
        logger.info("🟢 Market opened - Resuming trading operations")
        self.paused = False

    def _on_market_status_change(self, status: MarketStatus, details: Dict):
        """Callback for market status changes."""
        logger.info(f"📊 Market status: {status.value.upper()}")
        if details.get('time_to_open'):
            logger.info(f"   Next market open in: {details['time_to_open']}")
        if details.get('time_to_close'):
            logger.info(f"   Market closes in: {details['time_to_close']}")

    def _check_after_hours_permission(self) -> bool:
        """Check if user wants to continue trading after hours."""
        if self.user_confirmed_after_hours:
            return self.allow_after_hours

        status, details = self.market_manager.get_current_status()

        if status != MarketStatus.OPEN:
            print("\n" + "="*60)
            print("🔴 MARKET IS CURRENTLY CLOSED")
            print("="*60)
            print(f"Market Status: {status.value.upper()}")

            if details.get('next_open'):
                print(f"Next Market Open: {details['next_open'].strftime('%Y-%m-%d %H:%M:%S %Z')}")

            print("\nOptions:")
            print("1. Continue with last traded prices (simulated trading)")
            print("2. Stop and wait for market to open")

            while True:
                try:
                    choice = input("\nEnter your choice (1 or 2): ").strip()
                    if choice == "1":
                        self.allow_after_hours = True
                        self.user_confirmed_after_hours = True
                        print("✅ Continuing with simulated data...")
                        return True
                    elif choice == "2":
                        self.allow_after_hours = False
                        self.user_confirmed_after_hours = True
                        print("⏸️  Stopping until market opens...")
                        return False
                    else:
                        print("❌ Invalid choice. Please enter 1 or 2.")
                except KeyboardInterrupt:
                    print("\n⏸️  Stopping...")
                    return False

        return True

    def _initialize_broker(self):
        """Initialize broker in dry run mode."""
        try:
            broker_config = self.config.get('broker', {})
            broker_name = broker_config.get('name', 'dhan').lower()

            if broker_name == 'dhan':
                self.broker = DhanBroker(dry_run=True)
                logger.info("DhanBroker initialized in dry run mode")
            elif broker_name == 'fyers':
                self.broker = FyersBroker(dry_run=True)
                logger.info("FyersBroker initialized in dry run mode")
            else:
                raise ValueError(f"Unsupported broker: {broker_name}")

        except Exception as e:
            logger.error(f"Failed to initialize broker: {e}")
            raise

    def _initialize_data_manager(self):
        """Initialize data manager for market data."""
        try:
            # For dry run, we only use live data from DhanHQ
            # No need for DataManager or YFinance fetcher
            self.data_manager = None
            logger.info("Dry run mode: Using live data only (no DataManager needed)")
        except Exception as e:
            logger.error(f"Failed to initialize data manager: {e}")
            raise

    def _load_strategies(self, strategies_config: List[Dict[str, Any]]):
        """
        Load and initialize trading strategies.

        Args:
            strategies_config: List of strategy configurations
        """
        for strategy_config in strategies_config:
            try:
                strategy_name = strategy_config['name']
                strategy_enabled = strategy_config.get('enabled', True)

                if not strategy_enabled:
                    logger.info(f"Strategy {strategy_name} is disabled, skipping")
                    continue

                # Import strategy class
                strategy_module = __import__(
                    f"userdata.strategies.{strategy_name}",
                    fromlist=[strategy_name]
                )
                strategy_class = getattr(strategy_module, strategy_name)

                # Initialize strategy with config
                strategy_instance = strategy_class(self.config)
                self.strategies[strategy_name] = {
                    'instance': strategy_instance,
                    'config': strategy_config,
                    'last_signal_time': None,
                    'active_positions': {}
                }

                logger.info(f"Strategy {strategy_name} loaded successfully")

            except Exception as e:
                logger.error(f"Failed to load strategy {strategy_name}: {e}")
                continue

    def _get_live_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get live market quote for a symbol using DhanHQ API.

        Args:
            symbol: Trading symbol (without .NS suffix)

        Returns:
            Dictionary with live quote data or None if failed
        """
        try:
            # Get live quote from DhanHQ
            quote = self.broker.get_quote(symbol, "NSE_EQ")

            if quote and quote.get('ltp', 0) > 0:
                logger.debug(f"Live quote for {symbol}: LTP=₹{quote['ltp']:.2f}")
                return quote
            else:
                logger.warning(f"No live quote available for {symbol}")
                return None

        except Exception as e:
            logger.error(f"Failed to get live quote for {symbol}: {e}")
            return None



    def _get_market_data(self, symbol: str, timeframe: str = '1d') -> Optional[pd.DataFrame]:
        """
        Get live market data for a symbol using DhanHQ ticker API only.

        Args:
            symbol: Trading symbol
            timeframe: Data timeframe (not used for live data)

        Returns:
            DataFrame with live market data or None if failed
        """
        try:
            # Get live quote from DhanHQ
            live_quote = self._get_live_quote(symbol)

            if not live_quote or live_quote.get('ltp', 0) <= 0:
                logger.warning(f"No live quote available for {symbol}")
                return None

            # Create DataFrame with live data
            current_time = datetime.now()
            live_data = {
                'timestamp': [current_time],
                'open': [live_quote.get('open', live_quote['ltp'])],
                'high': [live_quote.get('high', live_quote['ltp'])],
                'low': [live_quote.get('low', live_quote['ltp'])],
                'close': [live_quote['ltp']],
                'volume': [live_quote.get('volume', 0)]
            }

            # Create DataFrame
            data = pd.DataFrame(live_data)
            data.set_index('timestamp', inplace=True)

            logger.info(f"Live data for {symbol}: LTP=₹{live_quote['ltp']:.2f}, Volume={live_quote.get('volume', 0)}")
            return data

        except Exception as e:
            logger.error(f"Failed to get live market data for {symbol}: {e}")
            return None

    def _execute_strategy_signals(self, strategy_name: str, symbol: str, data: pd.DataFrame):
        """
        Execute strategy signals for a symbol.

        Args:
            strategy_name: Name of the strategy
            symbol: Trading symbol
            data: Market data
        """
        try:
            strategy_info = self.strategies[strategy_name]
            strategy = strategy_info['instance']

            # Analyze data and get signals
            analyzed_data = strategy.analyze(data)

            if analyzed_data.empty:
                return

            # Get latest signals
            latest_row = analyzed_data.iloc[-1]
            current_price = latest_row['close']

            buy_signal = latest_row.get('buy_signal', 0)
            sell_signal = latest_row.get('sell_signal', 0)

            # Check for buy signals
            if buy_signal and not self._has_open_position(strategy_name, symbol):
                self._execute_buy_order(strategy_name, symbol, current_price, strategy)

            # Check for sell signals
            elif sell_signal and self._has_open_position(strategy_name, symbol):
                self._execute_sell_order(strategy_name, symbol, current_price, strategy)

        except Exception as e:
            logger.error(f"Failed to execute strategy signals for {strategy_name}/{symbol}: {e}")

    def _has_open_position(self, strategy_name: str, symbol: str) -> bool:
        """Check if there's an open position for strategy/symbol."""
        position_key = f"{strategy_name}_{symbol}"
        return position_key in self.positions

    def _execute_buy_order(self, strategy_name: str, symbol: str, price: float, strategy):
        """
        Execute a buy order in dry run mode.

        Args:
            strategy_name: Name of the strategy
            symbol: Trading symbol
            price: Current price
            strategy: Strategy instance
        """
        try:
            # Calculate position size
            position_size = strategy.calculate_position_size(price)

            if position_size <= 0:
                logger.warning(f"Invalid position size for {symbol}: {position_size}")
                return

            # Check available balance
            required_amount = price * position_size
            if required_amount > self.performance_stats['current_balance']:
                logger.warning(f"Insufficient balance for {symbol}. Required: {required_amount}, Available: {self.performance_stats['current_balance']}")
                return

            # Place order through broker
            order_result = self.broker.place_order(
                symbol=symbol,
                quantity=position_size,
                price=price,
                order_type="LIMIT",
                side="BUY"
            )

            if order_result and order_result.get('status') == 'success':
                order_id = order_result['data']['order_id']

                # Create position record
                position_key = f"{strategy_name}_{symbol}"
                self.positions[position_key] = {
                    'strategy': strategy_name,
                    'symbol': symbol,
                    'quantity': position_size,
                    'entry_price': price,
                    'entry_time': datetime.now(),
                    'order_id': order_id,
                    'status': 'open'
                }

                # Update balance
                self.performance_stats['current_balance'] -= required_amount

                logger.info(f"BUY order placed: {symbol} x{position_size} @ ₹{price:.2f} (Order ID: {order_id})")

            else:
                logger.error(f"Failed to place BUY order for {symbol}")

        except Exception as e:
            logger.error(f"Failed to execute buy order for {symbol}: {e}")

    def _execute_sell_order(self, strategy_name: str, symbol: str, price: float, strategy):
        """
        Execute a sell order in dry run mode.

        Args:
            strategy_name: Name of the strategy
            symbol: Trading symbol
            price: Current price
            strategy: Strategy instance
        """
        try:
            position_key = f"{strategy_name}_{symbol}"
            position = self.positions.get(position_key)

            if not position:
                logger.warning(f"No open position found for {symbol}")
                return

            quantity = position['quantity']
            entry_price = position['entry_price']

            # Place sell order through broker
            order_result = self.broker.place_order(
                symbol=symbol,
                quantity=quantity,
                price=price,
                order_type="LIMIT",
                side="SELL"
            )

            if order_result and order_result.get('status') == 'success':
                order_id = order_result['data']['order_id']

                # Calculate P&L
                pnl = (price - entry_price) * quantity
                pnl_pct = (pnl / (entry_price * quantity)) * 100

                # Update performance stats
                self.performance_stats['total_trades'] += 1
                self.performance_stats['total_pnl'] += pnl
                self.performance_stats['current_balance'] += price * quantity

                if pnl > 0:
                    self.performance_stats['winning_trades'] += 1
                else:
                    self.performance_stats['losing_trades'] += 1

                # Update position
                position.update({
                    'exit_price': price,
                    'exit_time': datetime.now(),
                    'pnl': pnl,
                    'pnl_pct': pnl_pct,
                    'status': 'closed',
                    'exit_order_id': order_id
                })

                logger.info(f"SELL order placed: {symbol} x{quantity} @ ₹{price:.2f} | P&L: ₹{pnl:.2f} ({pnl_pct:.2f}%) (Order ID: {order_id})")

                # Remove from active positions
                del self.positions[position_key]

            else:
                logger.error(f"Failed to place SELL order for {symbol}")

        except Exception as e:
            logger.error(f"Failed to execute sell order for {symbol}: {e}")

    def _update_performance_stats(self):
        """Update and log performance statistics."""
        try:
            current_time = datetime.now()
            self.performance_stats['last_update'] = current_time

            # Calculate additional metrics
            total_trades = self.performance_stats['total_trades']
            winning_trades = self.performance_stats['winning_trades']

            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

            # Calculate unrealized P&L for open positions
            unrealized_pnl = 0.0
            for position_key, position in self.positions.items():
                symbol = position['symbol']
                # For simplicity, we'll skip real-time unrealized P&L calculation
                # In a real implementation, you'd fetch current prices

            # Log performance summary
            if total_trades > 0:
                logger.info(f"Performance Update - Trades: {total_trades}, Win Rate: {win_rate:.1f}%, "
                           f"Total P&L: ₹{self.performance_stats['total_pnl']:.2f}, "
                           f"Balance: ₹{self.performance_stats['current_balance']:.2f}")

        except Exception as e:
            logger.error(f"Failed to update performance stats: {e}")

    def _save_results(self):
        """Save dry run results to file."""
        try:
            results_dir = Path("userdata/results")
            results_dir.mkdir(parents=True, exist_ok=True)

            results_file = results_dir / f"dryrun_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            results = {
                'config_path': self.config_path,
                'performance_stats': self.performance_stats,
                'strategies': {name: info['config'] for name, info in self.strategies.items()},
                'closed_positions': [],
                'open_positions': list(self.positions.values())
            }

            # Add closed positions from logs (simplified approach)
            # In a real implementation, you'd maintain a complete trade history

            with open(results_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"Dry run results saved to: {results_file}")

        except Exception as e:
            logger.error(f"Failed to save results: {e}")

    def start(self, strategies_config: List[Dict[str, Any]]):
        """
        Start the dry run trading engine.

        Args:
            strategies_config: List of strategy configurations
        """
        try:
            logger.info("Starting dry run trading engine...")

            # Initialize components
            self._initialize_broker()
            self._initialize_data_manager()

            # Load strategies
            self._load_strategies(strategies_config)

            if not self.strategies:
                logger.error("No strategies loaded, cannot start dry run")
                return

            # Initialize performance stats
            self.performance_stats['start_balance'] = self.config.get('dry_run_wallet', 100000)
            self.performance_stats['current_balance'] = self.performance_stats['start_balance']
            self.performance_stats['start_time'] = datetime.now()

            logger.info(f"Dry run started with balance: ₹{self.performance_stats['start_balance']}")

            # Check if user wants to continue after hours
            if not self._check_after_hours_permission():
                logger.info("User chose to stop - waiting for market to open")
                return

            # Start market hours monitoring
            self.market_manager.start_monitoring()

            # Get symbols to trade
            symbols = self.config.get('exchange', {}).get('pair_whitelist', [])
            if not symbols:
                logger.error("No symbols configured for trading")
                return

            self.running = True

            # Main trading loop
            while self.running:
                try:
                    # Check if trading is paused due to market hours
                    if self.paused:
                        if not self.allow_after_hours:
                            logger.info("⏸️  Trading paused - Market is closed")
                            import time
                            time.sleep(300)  # Check every 5 minutes when paused
                            continue

                    # Execute trading logic
                    for strategy_name in self.strategies:
                        for symbol in symbols:
                            # Skip if paused and not allowing after hours
                            if self.paused and not self.allow_after_hours:
                                continue

                            # Get market data
                            data = self._get_market_data(symbol, self.config.get('timeframe', '1d'))

                            if data is not None:
                                # Execute strategy signals
                                self._execute_strategy_signals(strategy_name, symbol, data)

                    # Update performance stats
                    self._update_performance_stats()

                    # Sleep for a short interval (simulate real-time trading)
                    # In production, this would be based on timeframe
                    import time
                    sleep_interval = 60 if not self.paused else 300  # Longer sleep when paused
                    time.sleep(sleep_interval)

                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt, stopping...")
                    break
                except Exception as e:
                    logger.error(f"Error in main trading loop: {e}")
                    time.sleep(10)  # Wait before retrying

        except Exception as e:
            logger.error(f"Failed to start dry run engine: {e}")
            raise
        finally:
            self.stop()

    def stop(self):
        """Stop the dry run trading engine."""
        if self.running:
            logger.info("Stopping dry run trading engine...")
            self.running = False

            # Stop market hours monitoring
            if hasattr(self, 'market_manager'):
                self.market_manager.stop_monitoring()

            # Save results
            self._save_results()

            # Close any open positions (optional in dry run)
            if self.positions:
                logger.info(f"Dry run stopped with {len(self.positions)} open positions")

            logger.info("Dry run trading engine stopped")


def start_dry_run(strategies_config: List[Dict[str, Any]], config_path: str):
    """
    Start dry run trading with given strategies and configuration.

    Args:
        strategies_config: List of strategy configurations
        config_path: Path to configuration file
    """
    try:
        engine = DryRunEngine(config_path)
        engine.start(strategies_config)
    except Exception as e:
        logger.error(f"Failed to start dry run: {e}")
        raise