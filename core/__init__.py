"""
Core Module for RapidTrader

This module provides core functionality for RapidTrader.
"""

# Import only modules that exist
try:
    from core.symbol_manager import SymbolManager
    _has_symbol_manager = True
except ImportError:
    _has_symbol_manager = False

try:
    from core.symbol_scheduler import SymbolScheduler, get_scheduler
    _has_symbol_scheduler = True
except ImportError:
    _has_symbol_scheduler = False

try:
    from core.startup import init_all, run_startup_in_thread
    _has_startup = True
except ImportError:
    _has_startup = False

# Import backtesting modules
from core.backtest_engine import BacktestEngine
from core.backtest_results import BacktestResults
from core.trade_simulator import TradeSimulator

__all__ = [
    'BacktestEngine',
    'BacktestResults',
    'TradeSimulator'
]

# Add optional imports to __all__ if they exist
if _has_symbol_manager:
    __all__.append('SymbolManager')

if _has_symbol_scheduler:
    __all__.extend(['SymbolScheduler', 'get_scheduler'])

if _has_startup:
    __all__.extend(['init_all', 'run_startup_in_thread'])
