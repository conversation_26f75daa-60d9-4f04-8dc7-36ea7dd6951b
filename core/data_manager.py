"""
Data Manager for RapidTrader Backtesting

This module automatically downloads and converts data for backtesting if it doesn't exist.
It integrates with the existing data download modules and ensures data is in the correct format.
"""

import logging
import os
import sys
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add data directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'data'))

try:
    from data.download_data import download_data, validate_timeframe, parse_date
    from data.yfinance_fetcher import YFinanceFetcher
    _has_data_modules = True
except ImportError as e:
    logging.warning(f"Could not import data modules: {e}")
    _has_data_modules = False

logger = logging.getLogger(__name__)

class DataManager:
    """
    Manages data downloading and conversion for backtesting.
    
    Automatically downloads missing data and converts it to the format
    expected by the backtesting engine.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize the data manager.
        
        Args:
            config: Backtesting configuration
        """
        self.config = config
        self.data_dir = Path(config.get("datadir", "userdata/historical_data"))
        self.yfinance_dir = self.data_dir / "yfinance"
        
        # Ensure directories exist
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.yfinance_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize yfinance fetcher if available
        if _has_data_modules:
            self.fetcher = YFinanceFetcher(cache_dir=str(self.data_dir))
        else:
            self.fetcher = None
            
        logger.info(f"DataManager initialized with data directory: {self.data_dir}")
    
    def ensure_data_available(self, symbols: List[str], timeframe: str, 
                            start_date: str, end_date: str) -> bool:
        """
        Ensure data is available for all symbols in the required format.
        
        Args:
            symbols: List of symbols to check
            timeframe: Required timeframe
            start_date: Start date for data
            end_date: End date for data
            
        Returns:
            True if all data is available, False otherwise
        """
        missing_symbols = []
        
        for symbol in symbols:
            if not self._is_data_available(symbol, timeframe, start_date, end_date):
                missing_symbols.append(symbol)
        
        if missing_symbols:
            logger.info(f"Missing data for {len(missing_symbols)} symbols: {missing_symbols}")
            return self._download_missing_data(missing_symbols, timeframe, start_date, end_date)
        
        logger.info("All required data is available")
        return True
    
    def _is_data_available(self, symbol: str, timeframe: str, 
                          start_date: str, end_date: str) -> bool:
        """
        Check if data is available for a symbol in the correct format.
        
        Args:
            symbol: Symbol to check
            timeframe: Required timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            True if data is available, False otherwise
        """
        # Check for data in backtesting format
        data_file = self.data_dir / f"{symbol}_{timeframe}.csv"
        
        if not data_file.exists():
            return False
        
        try:
            # Load and check data
            df = pd.read_csv(data_file)
            
            # Check required columns
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.warning(f"Data file {data_file} missing required columns")
                return False
            
            # Check date range
            df['date'] = pd.to_datetime(df['date'])
            data_start = df['date'].min()
            data_end = df['date'].max()
            
            required_start = pd.to_datetime(start_date)
            required_end = pd.to_datetime(end_date)
            
            # Check if we have sufficient data coverage
            if data_start > required_start or data_end < required_end:
                logger.info(f"Insufficient date coverage for {symbol}. "
                           f"Available: {data_start.date()} to {data_end.date()}, "
                           f"Required: {required_start.date()} to {required_end.date()}")
                return False
            
            # Check if we have enough data points
            if len(df) < 10:  # Minimum 10 data points
                logger.warning(f"Insufficient data points for {symbol}: {len(df)}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking data for {symbol}: {e}")
            return False
    
    def _download_missing_data(self, symbols: List[str], timeframe: str,
                              start_date: str, end_date: str) -> bool:
        """
        Download missing data for symbols.
        
        Args:
            symbols: List of symbols to download
            timeframe: Required timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            True if download successful, False otherwise
        """
        logger.info(f"Downloading data for {len(symbols)} symbols...")
        
        success_count = 0
        
        for symbol in symbols:
            try:
                # Try multiple download methods
                if self._download_with_yfinance_fetcher(symbol, timeframe, start_date, end_date):
                    success_count += 1
                elif self._download_with_download_data(symbol, timeframe, start_date, end_date):
                    success_count += 1
                else:
                    logger.error(f"Failed to download data for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error downloading data for {symbol}: {e}")
        
        logger.info(f"Successfully downloaded data for {success_count}/{len(symbols)} symbols")
        return success_count > 0
    
    def _download_with_yfinance_fetcher(self, symbol: str, timeframe: str,
                                       start_date: str, end_date: str) -> bool:
        """
        Download data using YFinanceFetcher.
        
        Args:
            symbol: Symbol to download
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            True if successful, False otherwise
        """
        if not self.fetcher:
            return False
        
        try:
            logger.info(f"Downloading {symbol} using YFinanceFetcher...")
            
            # Convert dates
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            
            # Download data
            df = self.fetcher.fetch_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_dt,
                end_date=end_dt,
                exchange="NSE"
            )
            
            if df.empty:
                logger.warning(f"No data returned for {symbol}")
                return False
            
            # Convert to backtesting format
            return self._convert_to_backtest_format(df, symbol, timeframe)
            
        except Exception as e:
            logger.error(f"Error downloading {symbol} with YFinanceFetcher: {e}")
            return False
    
    def _download_with_download_data(self, symbol: str, timeframe: str,
                                    start_date: str, end_date: str) -> bool:
        """
        Download data using download_data module.
        
        Args:
            symbol: Symbol to download
            timeframe: Timeframe
            start_date: Start date
            end_date: End date
            
        Returns:
            True if successful, False otherwise
        """
        if not _has_data_modules:
            return False
        
        try:
            logger.info(f"Downloading {symbol} using download_data module...")
            
            # Download to yfinance directory
            symbol_name, success, rows = download_data(
                symbol=symbol,
                exchange="nse",
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date,
                output_dir=str(self.yfinance_dir)
            )
            
            if not success or rows == 0:
                logger.warning(f"Download failed for {symbol}")
                return False
            
            # Convert from yfinance format to backtest format
            yfinance_file = self.yfinance_dir / f"NSE_{symbol}_{timeframe}.csv"
            if yfinance_file.exists():
                return self._convert_yfinance_file(yfinance_file, symbol, timeframe)
            
            return False
            
        except Exception as e:
            logger.error(f"Error downloading {symbol} with download_data: {e}")
            return False
    
    def _convert_yfinance_file(self, yfinance_file: Path, symbol: str, timeframe: str) -> bool:
        """
        Convert yfinance format file to backtest format.
        
        Args:
            yfinance_file: Path to yfinance format file
            symbol: Symbol name
            timeframe: Timeframe
            
        Returns:
            True if conversion successful, False otherwise
        """
        try:
            # Read yfinance data
            df = pd.read_csv(yfinance_file)
            
            if df.empty:
                logger.warning(f"Empty yfinance file: {yfinance_file}")
                return False
            
            # Convert to backtest format
            return self._convert_to_backtest_format(df, symbol, timeframe)
            
        except Exception as e:
            logger.error(f"Error converting yfinance file {yfinance_file}: {e}")
            return False
    
    def _convert_to_backtest_format(self, df: pd.DataFrame, symbol: str, timeframe: str) -> bool:
        """
        Convert DataFrame to backtest format and save.
        
        Args:
            df: DataFrame to convert
            symbol: Symbol name
            timeframe: Timeframe
            
        Returns:
            True if conversion successful, False otherwise
        """
        try:
            # Standardize column names
            column_mapping = {
                'Date': 'date',
                'Open': 'open',
                'open': 'open',
                'High': 'high',
                'high': 'high',
                'Low': 'low',
                'low': 'low',
                'Close': 'close',
                'close': 'close',
                'Volume': 'volume',
                'volume': 'volume'
            }
            
            # Rename columns
            df = df.rename(columns=column_mapping)
            
            # Ensure required columns exist
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"Missing columns for {symbol}: {missing_columns}")
                return False
            
            # Select only required columns
            df = df[required_columns].copy()
            
            # Convert date column
            if df.index.name == 'date' or df.index.name == 'Date':
                # Date is in index
                df = df.reset_index()
                df['date'] = pd.to_datetime(df['date'])
            else:
                # Date is in column
                df['date'] = pd.to_datetime(df['date'])
            
            # Format date
            df['date'] = df['date'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Round price columns
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').round(2)
            
            # Ensure volume is integer
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0).astype(int)
            
            # Remove any rows with NaN values
            df = df.dropna()
            
            # Sort by date
            df = df.sort_values('date')
            
            if df.empty:
                logger.warning(f"No valid data after conversion for {symbol}")
                return False
            
            # Save to backtest format
            output_file = self.data_dir / f"{symbol}_{timeframe}.csv"
            df.to_csv(output_file, index=False)
            
            logger.info(f"Converted {symbol}: {len(df)} rows saved to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error converting data for {symbol}: {e}")
            return False
