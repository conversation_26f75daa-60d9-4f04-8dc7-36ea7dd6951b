#!/usr/bin/env python3
"""
Independent Dry Run Engine for RapidTrader

This is a completely standalone paper trading engine that:
- Fetches delayed data from yfinance (15-20 min delay)
- Runs strategies with indicators from config
- Simulates buy/sell orders independently
- Logs all trades and follows strategy logic
- No dependency on DhanHQ or any broker module

Features:
- Real-time strategy execution simulation
- Portfolio tracking and P&L calculation
- Trade logging and performance metrics
- Strategy indicator calculations
- Risk management and position sizing
"""

import yfinance as yf
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import threading
import time
from pathlib import Path

# Try to import TA libraries, fall back to basic calculations if not available
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    logging.warning("TA-Lib not available, using basic indicator calculations")

try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False
    logging.warning("pandas_ta not available, using basic indicator calculations")

logger = logging.getLogger(__name__)

class IndependentDryRunEngine:
    """
    Standalone paper trading engine that simulates live trading using delayed yfinance data.
    """

    def __init__(self, config_path: str, initial_capital: float = 100000.0):
        """
        Initialize the independent dry run engine.

        Args:
            config_path: Path to strategy configuration file
            initial_capital: Starting capital for simulation
        """
        self.config_path = config_path
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.available_balance = initial_capital

        # Trading state
        self.positions = {}  # symbol -> position info
        self.orders = {}     # order_id -> order info
        self.trades = []     # completed trades
        self.portfolio_history = []

        # Data management
        self.market_data = {}  # symbol -> latest data
        self.price_history = {}  # symbol -> historical prices
        self.last_data_fetch = {}

        # Strategy management
        self.strategies = {}
        self.strategy_configs = {}
        self.running = False
        self.simulation_thread = None

        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_capital = initial_capital

        # Load configuration
        self._load_config()

        logger.info(f"Independent Dry Run Engine initialized with ₹{initial_capital:,.2f}")

    def _load_config(self):
        """Load strategy configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)

            self.strategy_configs = config.get('strategies', {})
            self.symbols = config.get('symbols', ['RELIANCE', 'TCS', 'HDFCBANK'])
            self.data_interval = config.get('data_interval', '1m')
            self.refresh_interval = config.get('refresh_interval', 60)  # seconds

            logger.info(f"Loaded config: {len(self.strategy_configs)} strategies, {len(self.symbols)} symbols")

        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            # Use default config
            self.strategy_configs = {}
            self.symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
            self.data_interval = '1m'
            self.refresh_interval = 60

    def _get_yfinance_symbol(self, symbol: str) -> str:
        """Convert trading symbol to yfinance format."""
        symbol_mapping = {
            'RELIANCE': 'RELIANCE.NS',
            'TCS': 'TCS.NS',
            'HDFCBANK': 'HDFCBANK.NS',
            'INFY': 'INFY.NS',
            'ICICIBANK': 'ICICIBANK.NS',
            'KOTAKBANK': 'KOTAKBANK.NS',
            'BHARTIARTL': 'BHARTIARTL.NS',
            'ITC': 'ITC.NS',
            'SBIN': 'SBIN.NS',
            'MARUTI': 'MARUTI.NS'
        }
        return symbol_mapping.get(symbol, f"{symbol}.NS")

    def _fetch_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch latest market data from yfinance."""
        try:
            yf_symbol = self._get_yfinance_symbol(symbol)
            ticker = yf.Ticker(yf_symbol)

            # Get recent data (last 2 days with 1-minute intervals)
            hist = ticker.history(period="2d", interval=self.data_interval)

            if hist.empty:
                logger.warning(f"No data available for {symbol}")
                return None

            # Get latest price data
            latest = hist.iloc[-1]
            previous = hist.iloc[-2] if len(hist) > 1 else latest

            # Calculate indicators if we have enough data
            indicators = self._calculate_indicators(hist)

            market_data = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'price': float(latest['Close']),
                'open': float(latest['Open']),
                'high': float(latest['High']),
                'low': float(latest['Low']),
                'volume': int(latest['Volume']),
                'change': float(latest['Close'] - previous['Close']),
                'change_percent': float((latest['Close'] - previous['Close']) / previous['Close'] * 100),
                'indicators': indicators,
                'history': hist
            }

            # Store in price history
            if symbol not in self.price_history:
                self.price_history[symbol] = []

            self.price_history[symbol].append({
                'timestamp': datetime.now(),
                'price': float(latest['Close']),
                'volume': int(latest['Volume'])
            })

            # Keep only last 1000 price points
            if len(self.price_history[symbol]) > 1000:
                self.price_history[symbol] = self.price_history[symbol][-1000:]

            logger.debug(f"Fetched data for {symbol}: ₹{market_data['price']:.2f}")
            return market_data

        except Exception as e:
            logger.error(f"Failed to fetch data for {symbol}: {e}")
            return None

    def _calculate_indicators(self, hist: pd.DataFrame) -> Dict[str, float]:
        """Calculate technical indicators from price history."""
        indicators = {}

        try:
            if len(hist) < 20:  # Need minimum data for indicators
                return indicators

            close = hist['Close']
            high = hist['High']
            low = hist['Low']
            volume = hist['Volume']

            if TALIB_AVAILABLE:
                # Use TA-Lib for indicators
                close_values = close.values
                high_values = high.values
                low_values = low.values
                volume_values = volume.values

                # Moving Averages
                indicators['sma_20'] = float(talib.SMA(close_values, timeperiod=20)[-1])
                indicators['sma_50'] = float(talib.SMA(close_values, timeperiod=50)[-1]) if len(close_values) >= 50 else 0
                indicators['ema_12'] = float(talib.EMA(close_values, timeperiod=12)[-1])
                indicators['ema_26'] = float(talib.EMA(close_values, timeperiod=26)[-1])

                # RSI
                indicators['rsi'] = float(talib.RSI(close_values, timeperiod=14)[-1])

                # MACD
                macd, macd_signal, macd_hist = talib.MACD(close_values)
                indicators['macd'] = float(macd[-1])
                indicators['macd_signal'] = float(macd_signal[-1])
                indicators['macd_histogram'] = float(macd_hist[-1])

                # Bollinger Bands
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close_values)
                indicators['bb_upper'] = float(bb_upper[-1])
                indicators['bb_middle'] = float(bb_middle[-1])
                indicators['bb_lower'] = float(bb_lower[-1])

                # Volume indicators
                indicators['volume_sma'] = float(talib.SMA(volume_values.astype(float), timeperiod=20)[-1])

            else:
                # Use basic pandas calculations as fallback
                # Moving Averages
                indicators['sma_20'] = float(close.rolling(window=20).mean().iloc[-1])
                indicators['sma_50'] = float(close.rolling(window=50).mean().iloc[-1]) if len(close) >= 50 else 0
                indicators['ema_12'] = float(close.ewm(span=12).mean().iloc[-1])
                indicators['ema_26'] = float(close.ewm(span=26).mean().iloc[-1])

                # Simple RSI calculation
                delta = close.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                indicators['rsi'] = float(100 - (100 / (1 + rs)).iloc[-1])

                # Simple MACD
                ema_12 = close.ewm(span=12).mean()
                ema_26 = close.ewm(span=26).mean()
                macd_line = ema_12 - ema_26
                macd_signal_line = macd_line.ewm(span=9).mean()
                indicators['macd'] = float(macd_line.iloc[-1])
                indicators['macd_signal'] = float(macd_signal_line.iloc[-1])
                indicators['macd_histogram'] = float((macd_line - macd_signal_line).iloc[-1])

                # Simple Bollinger Bands
                sma_20 = close.rolling(window=20).mean()
                std_20 = close.rolling(window=20).std()
                indicators['bb_upper'] = float((sma_20 + (std_20 * 2)).iloc[-1])
                indicators['bb_middle'] = float(sma_20.iloc[-1])
                indicators['bb_lower'] = float((sma_20 - (std_20 * 2)).iloc[-1])

                # Volume indicators
                indicators['volume_sma'] = float(volume.rolling(window=20).mean().iloc[-1])

            # Price position relative to Bollinger Bands
            current_price = float(close.iloc[-1])
            bb_range = indicators['bb_upper'] - indicators['bb_lower']
            if bb_range > 0:
                indicators['bb_position'] = (current_price - indicators['bb_lower']) / bb_range
            else:
                indicators['bb_position'] = 0.5

        except Exception as e:
            logger.warning(f"Error calculating indicators: {e}")

        return indicators

    def _evaluate_strategy_signals(self, symbol: str, market_data: Dict[str, Any]) -> Tuple[str, float]:
        """
        Evaluate strategy signals for a symbol.

        Returns:
            Tuple of (signal, confidence) where signal is 'BUY', 'SELL', or 'HOLD'
        """
        try:
            indicators = market_data.get('indicators', {})
            price = market_data['price']

            # Simple momentum strategy example
            signal = 'HOLD'
            confidence = 0.0

            # RSI-based signals
            rsi = indicators.get('rsi', 50)
            if rsi < 30:  # Oversold
                signal = 'BUY'
                confidence = (30 - rsi) / 30 * 0.7
            elif rsi > 70:  # Overbought
                signal = 'SELL'
                confidence = (rsi - 70) / 30 * 0.7

            # MACD confirmation
            macd = indicators.get('macd', 0)
            macd_signal = indicators.get('macd_signal', 0)
            if macd > macd_signal and signal == 'BUY':
                confidence += 0.2
            elif macd < macd_signal and signal == 'SELL':
                confidence += 0.2

            # Bollinger Bands confirmation
            bb_position = indicators.get('bb_position', 0.5)
            if bb_position < 0.2 and signal == 'BUY':
                confidence += 0.1
            elif bb_position > 0.8 and signal == 'SELL':
                confidence += 0.1

            # Volume confirmation
            volume_ratio = market_data['volume'] / indicators.get('volume_sma', 1)
            if volume_ratio > 1.5:  # High volume
                confidence += 0.1

            confidence = min(confidence, 1.0)  # Cap at 100%

            logger.debug(f"{symbol} signal: {signal} (confidence: {confidence:.2f})")
            return signal, confidence

        except Exception as e:
            logger.error(f"Error evaluating strategy for {symbol}: {e}")
            return 'HOLD', 0.0

    def _calculate_position_size(self, symbol: str, price: float, signal: str, confidence: float) -> int:
        """Calculate position size based on risk management rules."""
        try:
            # Risk per trade (2% of available balance)
            risk_per_trade = self.available_balance * 0.02

            # Position size based on confidence
            base_position_value = self.available_balance * 0.1  # 10% base allocation
            confidence_multiplier = confidence * 2  # Scale with confidence
            position_value = base_position_value * confidence_multiplier

            # Calculate quantity
            quantity = int(position_value / price)

            # Ensure we don't exceed available balance
            max_quantity = int(self.available_balance / price)
            quantity = min(quantity, max_quantity)

            # Minimum quantity check
            if quantity < 1:
                return 0

            logger.debug(f"Position size for {symbol}: {quantity} shares @ ₹{price:.2f}")
            return quantity

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0

    def _place_order(self, symbol: str, quantity: int, order_type: str, price: float) -> str:
        """Simulate placing an order."""
        order_id = f"DRY_{int(time.time())}_{len(self.orders)}"

        order = {
            'order_id': order_id,
            'symbol': symbol,
            'quantity': quantity,
            'order_type': order_type,  # BUY or SELL
            'price': price,
            'timestamp': datetime.now(),
            'status': 'EXECUTED',  # Simulate immediate execution
            'executed_price': price,
            'executed_quantity': quantity
        }

        self.orders[order_id] = order

        # Execute the order immediately (simulate market execution)
        self._execute_order(order)

        logger.info(f"Order placed: {order_type} {quantity} {symbol} @ ₹{price:.2f}")
        return order_id

    def _execute_order(self, order: Dict[str, Any]):
        """Execute a simulated order."""
        symbol = order['symbol']
        quantity = order['executed_quantity']
        price = order['executed_price']
        order_type = order['order_type']

        # Update positions
        if symbol not in self.positions:
            self.positions[symbol] = {
                'quantity': 0,
                'avg_price': 0.0,
                'total_cost': 0.0,
                'unrealized_pnl': 0.0
            }

        position = self.positions[symbol]

        if order_type == 'BUY':
            # Add to position
            total_cost = position['total_cost'] + (quantity * price)
            total_quantity = position['quantity'] + quantity

            position['avg_price'] = total_cost / total_quantity if total_quantity > 0 else 0
            position['quantity'] = total_quantity
            position['total_cost'] = total_cost

            # Update available balance
            self.available_balance -= quantity * price

        elif order_type == 'SELL':
            # Reduce position
            if position['quantity'] >= quantity:
                # Calculate P&L for sold quantity
                cost_basis = position['avg_price'] * quantity
                sale_value = price * quantity
                realized_pnl = sale_value - cost_basis

                # Update position
                position['quantity'] -= quantity
                position['total_cost'] -= cost_basis

                # Update balance and P&L
                self.available_balance += sale_value
                self.total_pnl += realized_pnl

                # Track trade
                trade = {
                    'symbol': symbol,
                    'quantity': quantity,
                    'buy_price': position['avg_price'],
                    'sell_price': price,
                    'pnl': realized_pnl,
                    'timestamp': datetime.now()
                }
                self.trades.append(trade)
                self.total_trades += 1

                if realized_pnl > 0:
                    self.winning_trades += 1

                logger.info(f"Trade executed: {symbol} P&L: ₹{realized_pnl:.2f}")
            else:
                logger.warning(f"Insufficient position to sell {quantity} {symbol}")

        # Clean up empty positions
        if symbol in self.positions and self.positions[symbol]['quantity'] == 0:
            del self.positions[symbol]

    def _update_portfolio_metrics(self):
        """Update portfolio performance metrics."""
        # Calculate current portfolio value
        portfolio_value = self.available_balance

        # Add unrealized P&L from open positions
        for symbol, position in self.positions.items():
            if symbol in self.market_data:
                current_price = self.market_data[symbol]['price']
                unrealized_pnl = (current_price - position['avg_price']) * position['quantity']
                position['unrealized_pnl'] = unrealized_pnl
                portfolio_value += position['total_cost'] + unrealized_pnl

        # Update peak and drawdown
        if portfolio_value > self.peak_capital:
            self.peak_capital = portfolio_value

        current_drawdown = (self.peak_capital - portfolio_value) / self.peak_capital
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown

        # Store portfolio snapshot
        self.portfolio_history.append({
            'timestamp': datetime.now(),
            'total_value': portfolio_value,
            'available_balance': self.available_balance,
            'unrealized_pnl': sum(pos.get('unrealized_pnl', 0) for pos in self.positions.values()),
            'realized_pnl': self.total_pnl
        })

        # Keep only last 1000 snapshots
        if len(self.portfolio_history) > 1000:
            self.portfolio_history = self.portfolio_history[-1000:]

    def _simulation_loop(self):
        """Main simulation loop that runs strategies."""
        logger.info("Starting simulation loop...")

        while self.running:
            try:
                # Fetch market data for all symbols
                for symbol in self.symbols:
                    market_data = self._fetch_market_data(symbol)
                    if market_data:
                        self.market_data[symbol] = market_data
                        self.last_data_fetch[symbol] = datetime.now()

                # Process strategies for each symbol
                for symbol in self.symbols:
                    if symbol in self.market_data:
                        market_data = self.market_data[symbol]

                        # Evaluate strategy signals
                        signal, confidence = self._evaluate_strategy_signals(symbol, market_data)

                        if signal != 'HOLD' and confidence > 0.5:  # Minimum confidence threshold
                            price = market_data['price']

                            if signal == 'BUY':
                                # Check if we can buy
                                quantity = self._calculate_position_size(symbol, price, signal, confidence)
                                if quantity > 0 and self.available_balance >= quantity * price:
                                    self._place_order(symbol, quantity, 'BUY', price)

                            elif signal == 'SELL':
                                # Check if we have position to sell
                                if symbol in self.positions and self.positions[symbol]['quantity'] > 0:
                                    quantity = self.positions[symbol]['quantity']
                                    self._place_order(symbol, quantity, 'SELL', price)

                # Update portfolio metrics
                self._update_portfolio_metrics()

                # Sleep until next iteration
                time.sleep(self.refresh_interval)

            except Exception as e:
                logger.error(f"Error in simulation loop: {e}")
                time.sleep(60)  # Wait longer on error

    def start_simulation(self):
        """Start the dry run simulation."""
        if not self.running:
            self.running = True
            self.simulation_thread = threading.Thread(target=self._simulation_loop, daemon=True)
            self.simulation_thread.start()
            logger.info("Dry run simulation started")
        else:
            logger.warning("Simulation is already running")

    def stop_simulation(self):
        """Stop the dry run simulation."""
        self.running = False
        if self.simulation_thread:
            self.simulation_thread.join(timeout=10)
        logger.info("Dry run simulation stopped")

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get current portfolio summary."""
        portfolio_value = self.available_balance
        unrealized_pnl = 0.0

        for position in self.positions.values():
            portfolio_value += position['total_cost'] + position.get('unrealized_pnl', 0)
            unrealized_pnl += position.get('unrealized_pnl', 0)

        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
        total_return = (portfolio_value - self.initial_capital) / self.initial_capital * 100

        return {
            'initial_capital': self.initial_capital,
            'current_value': portfolio_value,
            'available_balance': self.available_balance,
            'total_return': total_return,
            'realized_pnl': self.total_pnl,
            'unrealized_pnl': unrealized_pnl,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'win_rate': win_rate,
            'max_drawdown': self.max_drawdown * 100,
            'open_positions': len(self.positions),
            'symbols_tracked': len(self.symbols)
        }

    def get_positions(self) -> Dict[str, Any]:
        """Get current positions."""
        return self.positions.copy()

    def get_recent_trades(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recent trades."""
        return self.trades[-count:] if self.trades else []
