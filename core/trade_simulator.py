"""
Trade Simulator for RapidTrader Backtesting

This module simulates realistic trade execution including fees, slippage,
and position management for backtesting purposes.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid

logger = logging.getLogger(__name__)

class TradeSimulator:
    """
    Simulates realistic trade execution for backtesting.
    
    Handles order execution, fees, slippage, and position management
    to provide accurate backtesting results.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the trade simulator.
        
        Args:
            config: Trading configuration
        """
        self.config = config
        
        # Trading parameters
        self.max_open_trades = config.get("max_open_trades", 3)
        self.fee = config.get("fee", 0.001)  # 0.1% default fee
        self.slippage = config.get("slippage", 0.0005)  # 0.05% default slippage
        
        # State tracking
        self.open_trades = {}
        self.balance = 0.0
        self.total_fees_paid = 0.0
        
        logger.info(f"TradeSimulator initialized with max_open_trades: {self.max_open_trades}")
    
    def reset(self, starting_balance: float):
        """
        Reset simulator state for new backtest.
        
        Args:
            starting_balance: Starting balance for backtesting
        """
        self.open_trades = {}
        self.balance = starting_balance
        self.total_fees_paid = 0.0
        
        logger.info(f"TradeSimulator reset with balance: {starting_balance}")
    
    def can_open_trade(self) -> bool:
        """
        Check if we can open a new trade.
        
        Returns:
            True if we can open a new trade, False otherwise
        """
        return len(self.open_trades) < self.max_open_trades
    
    def open_trade(self, symbol: str, timestamp: datetime, price: float, quantity: int) -> Optional[Dict[str, Any]]:
        """
        Open a new trade position.
        
        Args:
            symbol: Trading symbol
            timestamp: Entry timestamp
            price: Entry price
            quantity: Quantity to buy
            
        Returns:
            Trade dictionary or None if trade couldn't be opened
        """
        if not self.can_open_trade():
            logger.warning("Cannot open trade - maximum open trades reached")
            return None
        
        if quantity <= 0:
            logger.warning("Cannot open trade - invalid quantity")
            return None
        
        # Apply slippage to entry price (assuming we pay slightly more)
        entry_price = price * (1 + self.slippage)
        
        # Calculate trade value and fees
        trade_value = entry_price * quantity
        entry_fee = trade_value * self.fee
        total_cost = trade_value + entry_fee
        
        # Check if we have enough balance
        if total_cost > self.balance:
            logger.warning(f"Insufficient balance for trade. Required: {total_cost}, Available: {self.balance}")
            return None
        
        # Create trade record
        trade_id = str(uuid.uuid4())
        trade = {
            'trade_id': trade_id,
            'symbol': symbol,
            'entry_timestamp': timestamp,
            'entry_price': entry_price,
            'quantity': quantity,
            'entry_value': trade_value,
            'entry_fee': entry_fee,
            'status': 'open'
        }
        
        # Update balance and tracking
        self.balance -= total_cost
        self.total_fees_paid += entry_fee
        self.open_trades[trade_id] = trade
        
        logger.debug(f"Opened trade {trade_id}: {symbol} x{quantity} @ {entry_price:.2f}")
        return trade
    
    def close_trade(self, trade: Dict[str, Any], timestamp: datetime, price: float) -> Optional[Dict[str, Any]]:
        """
        Close an open trade position.
        
        Args:
            trade: Open trade dictionary
            timestamp: Exit timestamp
            price: Exit price
            
        Returns:
            Completed trade dictionary or None if trade couldn't be closed
        """
        if trade['status'] != 'open':
            logger.warning("Cannot close trade - trade is not open")
            return None
        
        trade_id = trade['trade_id']
        if trade_id not in self.open_trades:
            logger.warning("Cannot close trade - trade not found in open trades")
            return None
        
        # Apply slippage to exit price (assuming we receive slightly less)
        exit_price = price * (1 - self.slippage)
        
        # Calculate exit values
        exit_value = exit_price * trade['quantity']
        exit_fee = exit_value * self.fee
        net_proceeds = exit_value - exit_fee
        
        # Calculate profit/loss
        total_cost = trade['entry_value'] + trade['entry_fee']
        profit_loss = net_proceeds - total_cost
        profit_loss_pct = (profit_loss / total_cost) * 100 if total_cost > 0 else 0
        
        # Update trade record
        trade.update({
            'exit_timestamp': timestamp,
            'exit_price': exit_price,
            'exit_value': exit_value,
            'exit_fee': exit_fee,
            'total_fees': trade['entry_fee'] + exit_fee,
            'profit_loss': profit_loss,
            'profit_loss_pct': profit_loss_pct,
            'status': 'closed'
        })
        
        # Calculate trade duration
        duration = timestamp - trade['entry_timestamp']
        trade['duration_hours'] = duration.total_seconds() / 3600
        trade['duration_days'] = duration.days
        
        # Update balance and tracking
        self.balance += net_proceeds
        self.total_fees_paid += exit_fee
        del self.open_trades[trade_id]
        
        logger.debug(f"Closed trade {trade_id}: P&L {profit_loss:.2f} ({profit_loss_pct:.2f}%)")
        return trade
    
    def get_open_trades_count(self) -> int:
        """
        Get number of currently open trades.
        
        Returns:
            Number of open trades
        """
        return len(self.open_trades)
    
    def get_current_balance(self) -> float:
        """
        Get current available balance.
        
        Returns:
            Current balance
        """
        return self.balance
    
    def get_total_fees_paid(self) -> float:
        """
        Get total fees paid during simulation.
        
        Returns:
            Total fees paid
        """
        return self.total_fees_paid
    
    def get_open_trades_value(self) -> float:
        """
        Get total value of currently open trades.
        
        Returns:
            Total value of open positions
        """
        total_value = 0.0
        for trade in self.open_trades.values():
            total_value += trade['entry_value'] + trade['entry_fee']
        return total_value
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """
        Get current portfolio summary.
        
        Returns:
            Portfolio summary dictionary
        """
        return {
            'available_balance': self.balance,
            'open_trades_count': len(self.open_trades),
            'open_trades_value': self.get_open_trades_value(),
            'total_fees_paid': self.total_fees_paid,
            'total_portfolio_value': self.balance + self.get_open_trades_value()
        }
