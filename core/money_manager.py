"""
Money Management System for RapidTrader

DEPRECATED: This module has been moved to the standalone money_management module.
This file is kept for backward compatibility.

Please use:
    from money_management import MoneyManager, get_money_manager

Instead of:
    from core.money_manager import MoneyManager, get_money_manager
"""

import logging
import warnings

# Import from the new standalone module
try:
    from money_management import <PERSON>Manager as _MoneyManager, get_money_manager as _get_money_manager

    # Create compatibility aliases
    MoneyManager = _MoneyManager
    get_money_manager = _get_money_manager

    # Issue deprecation warning
    warnings.warn(
        "core.money_manager is deprecated. Use 'from money_management import MoneyManager, get_money_manager' instead.",
        DeprecationWarning,
        stacklevel=2
    )

except ImportError:
    # Fallback to old implementation if new module not available
    import os
    import json
    from typing import Dict, List, Optional, Any, Tuple
    from datetime import datetime, timedelta
    from pathlib import Path

logger = logging.getLogger("MoneyManager")


class MoneyManager:
    """
    Comprehensive money management system for RapidTrader
    """

    def __init__(self, broker=None, config_path: str = "userdata/config/money_management.json"):
        """
        Initialize Money Manager

        Args:
            broker: Broker instance (DhanBroker)
            config_path: Path to money management configuration
        """
        self.broker = broker
        self.config_path = config_path
        self.config = self._load_config()

        # Cache for broker data
        self._last_balance_fetch = None
        self._cached_balance = None
        self._balance_cache_duration = 300  # 5 minutes

        logger.info("MoneyManager initialized")

    def _load_config(self) -> Dict[str, Any]:
        """Load money management configuration"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded money management config from {self.config_path}")
                return config
            else:
                # Create default configuration
                default_config = self._create_default_config()
                self._save_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"Error loading money management config: {e}")
            return self._create_default_config()

    def _create_default_config(self) -> Dict[str, Any]:
        """Create default money management configuration"""
        return {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "global_settings": {
                "total_capital_usage": 90.0,  # Use 90% of available capital
                "emergency_reserve": 10.0,    # Keep 10% as emergency reserve
                "max_risk_per_trade": 2.0,    # Max 2% risk per trade
                "max_total_risk": 15.0,       # Max 15% total portfolio risk
                "rebalance_frequency": "daily",  # daily, weekly, monthly
                "auto_rebalance": True,
                "stop_loss_global": 20.0      # Stop all trading if 20% loss
            },
            "allocation_model": {
                "type": "percentage",  # percentage, fixed_amount, dynamic
                "strategies": {},      # Will be populated with strategy allocations
                "default_allocation": 10.0  # Default 10% for new strategies
            },
            "risk_management": {
                "position_sizing": "percentage",  # percentage, fixed, volatility_based
                "max_positions_per_strategy": 5,
                "correlation_limit": 0.7,  # Max correlation between positions
                "sector_concentration_limit": 30.0,  # Max 30% in one sector
                "single_stock_limit": 10.0  # Max 10% in single stock
            },
            "broker_settings": {
                "balance_refresh_interval": 300,  # 5 minutes
                "margin_buffer": 5.0,  # Keep 5% margin buffer
                "auto_fetch_balance": True
            }
        }

    def _save_config(self, config: Dict[str, Any]) -> None:
        """Save money management configuration"""
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)

            # Update timestamp
            config["last_updated"] = datetime.now().isoformat()

            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=4)
            logger.info(f"Saved money management config to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving money management config: {e}")

    def get_available_balance(self, force_refresh: bool = False) -> Dict[str, float]:
        """
        Get available trading balance from broker

        Args:
            force_refresh: Force refresh from broker API

        Returns:
            Dictionary with balance information
        """
        # Check cache first
        if (not force_refresh and
            self._cached_balance and
            self._last_balance_fetch and
            (datetime.now() - self._last_balance_fetch).seconds < self._balance_cache_duration):
            return self._cached_balance

        try:
            if not self.broker:
                logger.warning("No broker instance available")
                return self._get_mock_balance()

            # Get fund information from broker
            fund_info = self.broker.get_fund_limit()

            if isinstance(fund_info, dict):
                balance_info = {
                    "total_balance": fund_info.get("totalBalance", 0.0),
                    "available_balance": fund_info.get("availableBalance", 0.0),
                    "used_margin": fund_info.get("utilizedAmount", 0.0),
                    "withdrawable_balance": fund_info.get("withdrawableBalance", 0.0),
                    "blocked_amount": fund_info.get("blockedPayoutAmount", 0.0)
                }

                # Cache the result
                self._cached_balance = balance_info
                self._last_balance_fetch = datetime.now()

                logger.info(f"Fetched balance: Available ₹{balance_info['available_balance']:,.2f}")
                return balance_info
            else:
                logger.error("Invalid fund info received from broker")
                return self._get_mock_balance()

        except Exception as e:
            logger.error(f"Error fetching balance from broker: {e}")
            return self._get_mock_balance()

    def _get_mock_balance(self) -> Dict[str, float]:
        """Get mock balance for testing/dry-run"""
        return {
            "total_balance": 200000.0,
            "available_balance": 150000.0,
            "used_margin": 50000.0,
            "withdrawable_balance": 145000.0,
            "blocked_amount": 5000.0
        }

    def calculate_strategy_allocations(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate money allocation for each strategy based on configuration

        Returns:
            Dictionary with strategy allocations
        """
        balance_info = self.get_available_balance()
        available_capital = balance_info["available_balance"]

        # Apply global capital usage limit
        total_capital_usage = self.config["global_settings"]["total_capital_usage"] / 100.0
        usable_capital = available_capital * total_capital_usage

        # Get strategy allocations
        strategy_allocations = self.config["allocation_model"]["strategies"]

        if not strategy_allocations:
            logger.warning("No strategy allocations configured")
            return {}

        # Calculate actual amounts
        allocations = {}
        total_percentage = sum(strategy_allocations.values())

        if total_percentage > 100:
            logger.warning(f"Total allocation exceeds 100%: {total_percentage}%")
            # Normalize allocations
            for strategy, percentage in strategy_allocations.items():
                normalized_percentage = (percentage / total_percentage) * 100
                strategy_allocations[strategy] = normalized_percentage

        for strategy, percentage in strategy_allocations.items():
            allocation_amount = usable_capital * (percentage / 100.0)

            allocations[strategy] = {
                "percentage": percentage,
                "amount": allocation_amount,
                "max_risk_amount": allocation_amount * (self.config["global_settings"]["max_risk_per_trade"] / 100.0),
                "max_positions": self.config["risk_management"]["max_positions_per_strategy"]
            }

        return allocations

    def add_strategy_allocation(self, strategy_name: str, percentage: float) -> bool:
        """
        Add or update strategy allocation

        Args:
            strategy_name: Name of the strategy
            percentage: Percentage allocation (0-100)

        Returns:
            True if successful
        """
        try:
            if percentage < 0 or percentage > 100:
                logger.error(f"Invalid percentage: {percentage}. Must be between 0-100")
                return False

            # Check if total allocation would exceed 100%
            current_allocations = self.config["allocation_model"]["strategies"]
            current_total = sum(current_allocations.values())

            if strategy_name in current_allocations:
                current_total -= current_allocations[strategy_name]

            if current_total + percentage > 100:
                logger.error(f"Total allocation would exceed 100%: {current_total + percentage}%")
                return False

            # Add/update allocation
            self.config["allocation_model"]["strategies"][strategy_name] = percentage
            self._save_config(self.config)

            logger.info(f"Set allocation for {strategy_name}: {percentage}%")
            return True

        except Exception as e:
            logger.error(f"Error adding strategy allocation: {e}")
            return False

    def remove_strategy_allocation(self, strategy_name: str) -> bool:
        """
        Remove strategy allocation

        Args:
            strategy_name: Name of the strategy to remove

        Returns:
            True if successful
        """
        try:
            if strategy_name in self.config["allocation_model"]["strategies"]:
                del self.config["allocation_model"]["strategies"][strategy_name]
                self._save_config(self.config)
                logger.info(f"Removed allocation for {strategy_name}")
                return True
            else:
                logger.warning(f"Strategy {strategy_name} not found in allocations")
                return False
        except Exception as e:
            logger.error(f"Error removing strategy allocation: {e}")
            return False

    def get_position_size(self, strategy_name: str, symbol: str, price: float,
                         risk_percentage: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate position size for a trade

        Args:
            strategy_name: Name of the strategy
            symbol: Trading symbol
            price: Entry price
            risk_percentage: Custom risk percentage (optional)

        Returns:
            Dictionary with position sizing information
        """
        try:
            allocations = self.calculate_strategy_allocations()

            if strategy_name not in allocations:
                logger.error(f"Strategy {strategy_name} not found in allocations")
                return {"error": "Strategy not found"}

            strategy_allocation = allocations[strategy_name]

            # Use custom risk or default
            risk_pct = risk_percentage or self.config["global_settings"]["max_risk_per_trade"]
            risk_amount = strategy_allocation["amount"] * (risk_pct / 100.0)

            # Calculate position size based on risk
            if self.config["risk_management"]["position_sizing"] == "percentage":
                # Simple percentage-based sizing
                position_value = strategy_allocation["amount"] / strategy_allocation["max_positions"]
                quantity = int(position_value / price)
            else:
                # Risk-based sizing (assuming 5% stop loss)
                stop_loss_pct = 0.05
                position_value = risk_amount / stop_loss_pct
                quantity = int(position_value / price)

            # Apply limits
            max_single_stock = self.config["risk_management"]["single_stock_limit"] / 100.0
            max_position_value = strategy_allocation["amount"] * max_single_stock
            max_quantity = int(max_position_value / price)

            quantity = min(quantity, max_quantity)

            return {
                "symbol": symbol,
                "strategy": strategy_name,
                "quantity": quantity,
                "price": price,
                "position_value": quantity * price,
                "risk_amount": risk_amount,
                "max_loss": quantity * price * 0.05,  # Assuming 5% stop loss
                "allocation_used": (quantity * price) / strategy_allocation["amount"] * 100
            }

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {"error": str(e)}

    def check_risk_limits(self, new_positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check if new positions would violate risk limits

        Args:
            new_positions: List of new position dictionaries

        Returns:
            Risk check results
        """
        try:
            allocations = self.calculate_strategy_allocations()
            total_capital = sum(alloc["amount"] for alloc in allocations.values())

            # Calculate total risk
            total_risk = sum(pos.get("risk_amount", 0) for pos in new_positions)
            total_risk_pct = (total_risk / total_capital) * 100 if total_capital > 0 else 0

            max_total_risk = self.config["global_settings"]["max_total_risk"]

            risk_check = {
                "within_limits": True,
                "total_risk_percentage": total_risk_pct,
                "max_allowed_risk": max_total_risk,
                "violations": []
            }

            # Check total risk limit
            if total_risk_pct > max_total_risk:
                risk_check["within_limits"] = False
                risk_check["violations"].append(f"Total risk {total_risk_pct:.2f}% exceeds limit {max_total_risk}%")

            # Check per-strategy position limits
            strategy_positions = {}
            for pos in new_positions:
                strategy = pos.get("strategy", "unknown")
                if strategy not in strategy_positions:
                    strategy_positions[strategy] = 0
                strategy_positions[strategy] += 1

            max_positions = self.config["risk_management"]["max_positions_per_strategy"]
            for strategy, count in strategy_positions.items():
                if count > max_positions:
                    risk_check["within_limits"] = False
                    risk_check["violations"].append(f"Strategy {strategy} has {count} positions, exceeds limit {max_positions}")

            return risk_check

        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {"within_limits": False, "error": str(e)}

    def rebalance_allocations(self) -> Dict[str, Any]:
        """
        Rebalance strategy allocations based on current market value

        Returns:
            Rebalancing results
        """
        try:
            # This would integrate with portfolio tracking
            # For now, return basic rebalancing info
            allocations = self.calculate_strategy_allocations()

            rebalance_info = {
                "timestamp": datetime.now().isoformat(),
                "required": False,
                "actions": [],
                "total_allocated": sum(alloc["amount"] for alloc in allocations.values())
            }

            # Check if rebalancing is needed (simplified logic)
            balance_info = self.get_available_balance()
            available = balance_info["available_balance"]

            if available > rebalance_info["total_allocated"] * 1.1:  # 10% threshold
                rebalance_info["required"] = True
                rebalance_info["actions"].append("Increase allocations due to available capital")

            return rebalance_info

        except Exception as e:
            logger.error(f"Error during rebalancing: {e}")
            return {"error": str(e)}

    def get_allocation_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive allocation summary

        Returns:
            Allocation summary with all details
        """
        try:
            balance_info = self.get_available_balance()
            allocations = self.calculate_strategy_allocations()

            total_allocated = sum(alloc["amount"] for alloc in allocations.values())
            total_percentage = sum(alloc["percentage"] for alloc in allocations.values())

            summary = {
                "timestamp": datetime.now().isoformat(),
                "balance_info": balance_info,
                "total_capital_available": balance_info["available_balance"],
                "total_allocated_amount": total_allocated,
                "total_allocated_percentage": total_percentage,
                "unallocated_amount": balance_info["available_balance"] - total_allocated,
                "unallocated_percentage": 100 - total_percentage,
                "emergency_reserve": balance_info["available_balance"] * (self.config["global_settings"]["emergency_reserve"] / 100.0),
                "strategies": allocations,
                "risk_limits": {
                    "max_risk_per_trade": self.config["global_settings"]["max_risk_per_trade"],
                    "max_total_risk": self.config["global_settings"]["max_total_risk"],
                    "stop_loss_global": self.config["global_settings"]["stop_loss_global"]
                }
            }

            return summary

        except Exception as e:
            logger.error(f"Error generating allocation summary: {e}")
            return {"error": str(e)}

    def update_global_settings(self, settings: Dict[str, Any]) -> bool:
        """
        Update global money management settings

        Args:
            settings: Dictionary of settings to update

        Returns:
            True if successful
        """
        try:
            valid_settings = [
                "total_capital_usage", "emergency_reserve", "max_risk_per_trade",
                "max_total_risk", "rebalance_frequency", "auto_rebalance", "stop_loss_global"
            ]

            for key, value in settings.items():
                if key in valid_settings:
                    self.config["global_settings"][key] = value
                    logger.info(f"Updated {key}: {value}")
                else:
                    logger.warning(f"Invalid setting: {key}")

            self._save_config(self.config)
            return True

        except Exception as e:
            logger.error(f"Error updating global settings: {e}")
            return False

    def export_config(self, export_path: str) -> bool:
        """
        Export money management configuration

        Args:
            export_path: Path to export configuration

        Returns:
            True if successful
        """
        try:
            with open(export_path, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Exported config to {export_path}")
            return True
        except Exception as e:
            logger.error(f"Error exporting config: {e}")
            return False

    def import_config(self, import_path: str) -> bool:
        """
        Import money management configuration

        Args:
            import_path: Path to import configuration from

        Returns:
            True if successful
        """
        try:
            with open(import_path, 'r') as f:
                imported_config = json.load(f)

            # Validate imported config
            if self._validate_config(imported_config):
                self.config = imported_config
                self._save_config(self.config)
                logger.info(f"Imported config from {import_path}")
                return True
            else:
                logger.error("Invalid configuration format")
                return False

        except Exception as e:
            logger.error(f"Error importing config: {e}")
            return False

    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate configuration structure

        Args:
            config: Configuration to validate

        Returns:
            True if valid
        """
        required_sections = ["global_settings", "allocation_model", "risk_management", "broker_settings"]

        for section in required_sections:
            if section not in config:
                logger.error(f"Missing required section: {section}")
                return False

        return True


# Global money manager instance
_money_manager = None

def get_money_manager(broker=None) -> MoneyManager:
    """Get global money manager instance"""
    global _money_manager
    if _money_manager is None:
        _money_manager = MoneyManager(broker=broker)
    return _money_manager
