#!/usr/bin/env python3
"""
Core Initialization Tests

Tests RapidTrader core initialization, health checks, and basic functionality.
"""

import os
import sys
import json
import tempfile
import shutil
import unittest
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestCoreInitialization(unittest.TestCase):
    """Test core RapidTrader initialization"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test directory structure
        self.userdata_dir = os.path.join(self.test_dir, "userdata")
        self.configs_dir = os.path.join(self.userdata_dir, "configs")
        self.strategies_dir = os.path.join(self.userdata_dir, "strategies")
        self.logs_dir = os.path.join(self.userdata_dir, "logs")
        
        for directory in [self.userdata_dir, self.configs_dir, self.strategies_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_rapidtrader_cli_import(self):
        """Test RapidTrader CLI can be imported"""
        try:
            from core.rapidtrader import cli, init_components, check_system_health
            self.assertTrue(callable(cli))
            self.assertTrue(callable(init_components))
            self.assertTrue(callable(check_system_health))
        except ImportError as e:
            self.skipTest(f"RapidTrader CLI not available: {e}")
    
    def test_initialization_function(self):
        """Test initialization function"""
        try:
            from core.rapidtrader import init_components
            
            # Should not raise any exceptions
            init_components()
            
            # Verify directories are created
            self.assertTrue(os.path.exists(self.userdata_dir))
            self.assertTrue(os.path.exists(self.configs_dir))
            self.assertTrue(os.path.exists(self.strategies_dir))
            
        except ImportError as e:
            self.skipTest(f"Initialization function not available: {e}")
    
    def test_health_check_function(self):
        """Test system health check"""
        try:
            from core.rapidtrader import check_system_health
            
            health_status = check_system_health()
            
            # Verify health status structure
            required_keys = ['python_version', 'required_dirs', 'broker_modules', 'docker_available']
            for key in required_keys:
                self.assertIn(key, health_status)
            
            # Python version should be OK (we're running the test)
            self.assertTrue(health_status['python_version'])
            
        except ImportError as e:
            self.skipTest(f"Health check function not available: {e}")
    
    def test_directory_creation(self):
        """Test automatic directory creation"""
        try:
            from core.rapidtrader import init_components, USER_DATA_DIR, CONFIGS_DIR, STRATEGIES_DIR
            
            # Remove directories
            if os.path.exists(self.userdata_dir):
                shutil.rmtree(self.userdata_dir)
            
            # Run initialization
            init_components()
            
            # Check directories were created
            self.assertTrue(os.path.exists(self.userdata_dir))
            self.assertTrue(os.path.exists(self.configs_dir))
            self.assertTrue(os.path.exists(self.strategies_dir))
            
        except ImportError as e:
            self.skipTest(f"Directory creation test failed: {e}")


class TestConfigurationManagement(unittest.TestCase):
    """Test configuration management functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        self.configs_dir = os.path.join(self.test_dir, "userdata", "configs")
        os.makedirs(self.configs_dir, exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_config_loading(self):
        """Test configuration loading"""
        try:
            from core.rapidtrader import load_config
            
            # Create test configuration
            test_config = {
                "exchange": {"name": "fyers"},
                "broker": {"name": "fyers", "dry_run": True},
                "dry_run": True,
                "stake_currency": "INR",
                "stake_amount": 1000
            }
            
            config_file = os.path.join(self.configs_dir, "test-config.json")
            with open(config_file, 'w') as f:
                json.dump(test_config, f, indent=4)
            
            # Test loading
            loaded_config = load_config(config_file)
            
            self.assertEqual(loaded_config["exchange"]["name"], "fyers")
            self.assertEqual(loaded_config["stake_currency"], "INR")
            self.assertTrue(loaded_config["dry_run"])
            
        except ImportError as e:
            self.skipTest(f"Configuration loading not available: {e}")
    
    def test_invalid_config_handling(self):
        """Test handling of invalid configurations"""
        try:
            from core.rapidtrader import load_config
            
            # Test non-existent file
            result = load_config("non_existent_file.json")
            self.assertEqual(result, {})
            
            # Test invalid JSON
            invalid_config_file = os.path.join(self.configs_dir, "invalid.json")
            with open(invalid_config_file, 'w') as f:
                f.write("{ invalid json }")
            
            result = load_config(invalid_config_file)
            self.assertEqual(result, {})
            
        except ImportError as e:
            self.skipTest(f"Invalid config handling not available: {e}")


class TestCLICommands(unittest.TestCase):
    """Test CLI command structure"""
    
    def test_cli_command_groups(self):
        """Test CLI command groups exist"""
        try:
            from core.rapidtrader import cli
            
            # Get command names
            command_names = [cmd.name for cmd in cli.commands.values()]
            
            # Check for expected command groups
            expected_commands = ['health', 'create-config', 'trade', 'backtest', 'strategy', 'profile', 'money']
            
            for expected_cmd in expected_commands:
                if expected_cmd in command_names:
                    self.assertIn(expected_cmd, command_names)
                # Some commands might not be available in all environments
            
        except ImportError as e:
            self.skipTest(f"CLI commands not available: {e}")
    
    def test_health_command_exists(self):
        """Test health command exists and is callable"""
        try:
            from core.rapidtrader import cli
            
            # Check if health command exists
            if 'health' in [cmd.name for cmd in cli.commands.values()]:
                health_cmd = None
                for cmd in cli.commands.values():
                    if cmd.name == 'health':
                        health_cmd = cmd
                        break
                
                self.assertIsNotNone(health_cmd)
                self.assertTrue(callable(health_cmd.callback))
            
        except ImportError as e:
            self.skipTest(f"Health command not available: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
