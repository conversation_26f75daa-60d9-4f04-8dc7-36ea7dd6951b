#!/usr/bin/env python3
"""
Test Setup Verification

Quick test to verify our comprehensive test setup is working correctly.
"""

import os
import sys
import unittest
import tempfile
import shutil
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestSetupVerification(unittest.TestCase):
    """Verify test setup is working"""
    
    def test_python_version(self):
        """Test Python version is adequate"""
        self.assertGreaterEqual(sys.version_info, (3, 8))
    
    def test_project_structure(self):
        """Test basic project structure exists"""
        expected_dirs = [
            "core",
            "broker", 
            "api_gateway",
            "userdata",
            "test",
            "scripts"
        ]
        
        for directory in expected_dirs:
            dir_path = project_root / directory
            if dir_path.exists():
                self.assertTrue(dir_path.exists(), f"Directory {directory} should exist")
    
    def test_core_files_exist(self):
        """Test core files exist"""
        core_files = [
            "core/rapidtrader.py",
            "README.md",
            "requirements.txt"
        ]
        
        for file_path in core_files:
            full_path = project_root / file_path
            if full_path.exists():
                self.assertTrue(full_path.exists(), f"File {file_path} should exist")
    
    def test_test_files_exist(self):
        """Test our new test files exist"""
        test_files = [
            "test/test_core_initialization.py",
            "test/test_broker_integrations.py",
            "test/test_backtesting_engine.py",
            "test/test_api_gateway_core.py",
            "test/test_money_management.py",
            "test/test_data_management.py",
            "test/test_docker_integration.py",
            "test/run_comprehensive_tests.py"
        ]
        
        for test_file in test_files:
            file_path = project_root / test_file
            self.assertTrue(file_path.exists(), f"Test file {test_file} should exist")
    
    def test_comprehensive_test_runner_import(self):
        """Test comprehensive test runner can be imported"""
        try:
            from test.run_comprehensive_tests import ComprehensiveTestRunner
            
            # Should be able to instantiate
            runner = ComprehensiveTestRunner()
            self.assertIsNotNone(runner)
            
            # Should have test modules defined
            self.assertGreater(len(runner.test_modules), 0)
            
        except ImportError as e:
            self.skipTest(f"Comprehensive test runner not available: {e}")
    
    def test_individual_test_modules_import(self):
        """Test individual test modules can be imported"""
        test_modules = [
            'test_core_initialization',
            'test_broker_integrations', 
            'test_backtesting_engine',
            'test_api_gateway_core',
            'test_money_management',
            'test_data_management',
            'test_docker_integration'
        ]
        
        for module_name in test_modules:
            try:
                test_module = __import__(module_name)
                self.assertIsNotNone(test_module)
            except ImportError as e:
                # Some modules might not be available in all environments
                print(f"Warning: Could not import {module_name}: {e}")
    
    def test_test_runner_enhanced(self):
        """Test enhanced test runner functionality"""
        try:
            from test.run_tests import run_comprehensive_tests
            
            # Should be able to import the function
            self.assertTrue(callable(run_comprehensive_tests))
            
        except ImportError as e:
            self.skipTest(f"Enhanced test runner not available: {e}")
    
    def test_readme_updated(self):
        """Test README has been updated with comprehensive information"""
        readme_path = project_root / "README.md"
        
        if readme_path.exists():
            with open(readme_path, 'r') as f:
                content = f.read()
            
            # Check for key sections
            expected_sections = [
                "RapidTrader",
                "Features",
                "Quick Start",
                "Docker",
                "API Gateway"
            ]
            
            for section in expected_sections:
                self.assertIn(section, content, f"README should contain {section} section")
        else:
            self.skipTest("README.md not found")
    
    def test_environment_setup(self):
        """Test environment is properly set up for testing"""
        # Check if we can create temporary directories
        test_dir = tempfile.mkdtemp()
        self.assertTrue(os.path.exists(test_dir))
        
        # Clean up
        shutil.rmtree(test_dir)
        
        # Check current working directory
        self.assertTrue(os.path.exists(os.getcwd()))


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality works"""
    
    def test_basic_imports(self):
        """Test basic imports work"""
        # Test standard library imports
        import json
        import os
        import sys
        import unittest
        import tempfile
        import shutil
        
        # All should work without issues
        self.assertTrue(True)
    
    def test_pathlib_functionality(self):
        """Test pathlib functionality"""
        from pathlib import Path
        
        # Test basic Path operations
        current_path = Path(__file__)
        self.assertTrue(current_path.exists())
        self.assertTrue(current_path.is_file())
        
        parent_path = current_path.parent
        self.assertTrue(parent_path.exists())
        self.assertTrue(parent_path.is_dir())
    
    def test_json_functionality(self):
        """Test JSON functionality"""
        import json
        
        test_data = {
            "test": True,
            "number": 42,
            "list": [1, 2, 3]
        }
        
        # Test serialization
        json_str = json.dumps(test_data)
        self.assertIsInstance(json_str, str)
        
        # Test deserialization
        parsed_data = json.loads(json_str)
        self.assertEqual(parsed_data, test_data)


if __name__ == '__main__':
    # Run with high verbosity to see all test details
    unittest.main(verbosity=2)
