#!/usr/bin/env python3
"""
Test script for RapidTrader API Gateway v2.0

This script demonstrates the new OpenAlgo-inspired API architecture
with API key authentication and unified broker interface.
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_API_KEY = None

class RapidTraderAPITest:
    """Test class for RapidTrader API Gateway"""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.api_key = None
        self.session = requests.Session()
        
    def test_health_check(self):
        """Test API Gateway health check"""
        print("🔍 Testing API Gateway health...")
        
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            
            health_data = response.json()
            print(f"✅ API Gateway is healthy")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Timestamp: {health_data.get('timestamp')}")
            print(f"   Containers: {health_data.get('containers', 0)}")
            print(f"   Brokers: {health_data.get('brokers', 0)}")
            return True
            
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return False
    
    def test_create_api_key(self):
        """Test API key creation"""
        print("\n🔑 Testing API key creation...")
        
        try:
            payload = {
                "name": f"test-key-{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "description": "Test API key for demonstration",
                "permissions": ["read", "write"]
            }
            
            response = self.session.post(
                f"{self.base_url}/auth/api-keys",
                json=payload
            )
            response.raise_for_status()
            
            key_data = response.json()
            self.api_key = key_data["key"]
            
            print(f"✅ API key created successfully")
            print(f"   Key ID: {key_data['id']}")
            print(f"   Key: {self.api_key[:20]}...")
            print(f"   Permissions: {key_data['permissions']}")
            
            # Set authorization header for future requests
            self.session.headers.update({
                "Authorization": f"Bearer {self.api_key}"
            })
            
            return True
            
        except Exception as e:
            print(f"❌ API key creation failed: {e}")
            return False
    
    def test_list_api_keys(self):
        """Test listing API keys"""
        print("\n📋 Testing API key listing...")
        
        if not self.api_key:
            print("❌ No API key available for authentication")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/auth/api-keys")
            response.raise_for_status()
            
            keys_data = response.json()
            api_keys = keys_data.get("api_keys", [])
            
            print(f"✅ Found {len(api_keys)} API keys")
            for key in api_keys:
                print(f"   - {key['name']} (ID: {key['id']}, Active: {key['is_active']})")
            
            return True
            
        except Exception as e:
            print(f"❌ API key listing failed: {e}")
            return False
    
    def test_add_broker(self):
        """Test adding a broker configuration"""
        print("\n🏦 Testing broker configuration...")
        
        if not self.api_key:
            print("❌ No API key available for authentication")
            return False
        
        try:
            # Test with Fyers broker configuration
            broker_config = {
                "broker_name": "fyers",
                "credentials": {
                    "client_id": "test_client_id",
                    "access_token": "test_access_token",
                    "refresh_token": "test_refresh_token"
                },
                "dry_run": True,
                "live_data": True
            }
            
            response = self.session.post(
                f"{self.base_url}/brokers",
                json=broker_config
            )
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ Broker added successfully")
            print(f"   Broker Name: {result['broker_name']}")
            print(f"   Message: {result['message']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Broker configuration failed: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"   Response: {e.response.text}")
            return False
    
    def test_list_brokers(self):
        """Test listing configured brokers"""
        print("\n📊 Testing broker listing...")
        
        if not self.api_key:
            print("❌ No API key available for authentication")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/brokers")
            response.raise_for_status()
            
            brokers_data = response.json()
            brokers = brokers_data.get("brokers", [])
            
            print(f"✅ Found {len(brokers)} configured brokers")
            for broker in brokers:
                print(f"   - {broker}")
            
            return True
            
        except Exception as e:
            print(f"❌ Broker listing failed: {e}")
            return False
    
    def test_container_listing(self):
        """Test listing containers"""
        print("\n🐳 Testing container listing...")
        
        if not self.api_key:
            print("❌ No API key available for authentication")
            return False
        
        try:
            response = self.session.get(f"{self.base_url}/containers")
            response.raise_for_status()
            
            containers_data = response.json()
            containers = containers_data.get("containers", [])
            
            print(f"✅ Found {len(containers)} containers")
            for container in containers:
                print(f"   - {container['name']} ({container['status']})")
            
            return True
            
        except Exception as e:
            print(f"❌ Container listing failed: {e}")
            return False
    
    def test_api_documentation(self):
        """Test API documentation access"""
        print("\n📚 Testing API documentation...")
        
        try:
            # Test OpenAPI docs
            response = self.session.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                print("✅ API documentation is accessible at /docs")
            else:
                print(f"❌ API documentation not accessible: {response.status_code}")
            
            # Test OpenAPI JSON
            response = self.session.get(f"{self.base_url}/openapi.json")
            if response.status_code == 200:
                openapi_spec = response.json()
                print(f"✅ OpenAPI specification available")
                print(f"   Title: {openapi_spec.get('info', {}).get('title')}")
                print(f"   Version: {openapi_spec.get('info', {}).get('version')}")
                print(f"   Endpoints: {len(openapi_spec.get('paths', {}))}")
            
            return True
            
        except Exception as e:
            print(f"❌ API documentation test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting RapidTrader API Gateway v2.0 Tests")
        print("=" * 60)
        
        tests = [
            self.test_health_check,
            self.test_create_api_key,
            self.test_list_api_keys,
            self.test_add_broker,
            self.test_list_brokers,
            self.test_container_listing,
            self.test_api_documentation
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                time.sleep(1)  # Small delay between tests
            except Exception as e:
                print(f"❌ Test {test.__name__} failed with exception: {e}")
        
        print("\n" + "=" * 60)
        print(f"🎯 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! RapidTrader API Gateway v2.0 is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the API Gateway setup.")
        
        return passed == total

def main():
    """Main test function"""
    print("RapidTrader API Gateway v2.0 Test Suite")
    print("Inspired by OpenAlgo architecture")
    print()
    
    # Check if API Gateway is running
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ API Gateway is not responding correctly")
            print("Please start the API Gateway with: ./scripts/rapidtrader-api.sh start")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to API Gateway at {API_BASE_URL}")
        print("Please start the API Gateway with: ./scripts/rapidtrader-api.sh start")
        return False
    except Exception as e:
        print(f"❌ Error checking API Gateway: {e}")
        return False
    
    # Run tests
    tester = RapidTraderAPITest()
    success = tester.run_all_tests()
    
    if success:
        print(f"\n🔑 Your API key: {tester.api_key}")
        print("💡 Save this API key for future use!")
        print(f"📖 API Documentation: {API_BASE_URL}/docs")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
