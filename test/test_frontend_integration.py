#!/usr/bin/env python3
"""
Test script for RapidTrader Frontend Integration

Tests the complete platform including frontend and API integration.
"""

import os
import sys
import time
import requests
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configuration
API_BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
DEMO_API_KEY = "rt_demo_key_12345"

class FrontendIntegrationTest:
    """Test class for complete platform integration"""
    
    def __init__(self):
        self.headers = {
            "Authorization": f"Bearer {DEMO_API_KEY}",
            "Content-Type": "application/json"
        }
        
    def test_api_health(self):
        """Test API health"""
        print("🔍 Testing API health...")
        
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=10)
            response.raise_for_status()
            
            health_data = response.json()
            print(f"✅ API is healthy")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Symbol mappers: {health_data.get('symbol_mappers', {})}")
            return True
            
        except Exception as e:
            print(f"❌ API health check failed: {e}")
            return False
    
    def test_frontend_health(self):
        """Test frontend accessibility"""
        print("\n🌐 Testing frontend accessibility...")
        
        try:
            response = requests.get(f"{FRONTEND_URL}/health", timeout=10)
            if response.status_code == 200:
                print("✅ Frontend health endpoint accessible")
            else:
                # Try main page
                response = requests.get(FRONTEND_URL, timeout=10)
                if response.status_code == 200:
                    print("✅ Frontend main page accessible")
                else:
                    raise Exception(f"Frontend returned status {response.status_code}")
            
            return True
            
        except Exception as e:
            print(f"❌ Frontend accessibility test failed: {e}")
            return False
    
    def test_api_endpoints(self):
        """Test key API endpoints"""
        print("\n🔧 Testing API endpoints...")
        
        endpoints = [
            ("/", "Root endpoint"),
            ("/health", "Health check"),
            ("/symbols/search/SBIN", "Symbol search"),
            ("/brokers/list", "Broker list"),
        ]
        
        passed = 0
        for endpoint, description in endpoints:
            try:
                response = requests.get(
                    f"{API_BASE_URL}{endpoint}",
                    headers=self.headers,
                    timeout=5
                )
                
                if response.status_code == 200:
                    print(f"   ✅ {description}")
                    passed += 1
                else:
                    print(f"   ❌ {description} (Status: {response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ {description} (Error: {e})")
        
        print(f"\n📊 API Endpoints: {passed}/{len(endpoints)} passed")
        return passed == len(endpoints)
    
    def test_symbol_system(self):
        """Test unified symbol system"""
        print("\n📊 Testing unified symbol system...")
        
        test_symbols = ["SBIN", "RELIANCE", "TCS"]
        found_symbols = 0
        
        for symbol in test_symbols:
            try:
                response = requests.get(
                    f"{API_BASE_URL}/symbols/search/{symbol}",
                    headers=self.headers,
                    params={"exchange": "NSE_EQ"},
                    timeout=5
                )
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   ✅ {symbol}: {data.get('rapidtrader_symbol')}")
                    if data.get('fyers_symbol'):
                        print(f"      Fyers: {data.get('fyers_symbol')}")
                    if data.get('dhan_security_id'):
                        print(f"      Dhan: {data.get('dhan_security_id')}")
                    found_symbols += 1
                else:
                    print(f"   ❌ {symbol}: Not found")
                    
            except Exception as e:
                print(f"   ❌ {symbol}: Error - {e}")
        
        print(f"\n📈 Symbol System: {found_symbols}/{len(test_symbols)} symbols found")
        return found_symbols > 0
    
    def test_docker_containers(self):
        """Test Docker containers status"""
        print("\n🐳 Testing Docker containers...")
        
        try:
            # Check if docker-compose is running
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.api.yml", "ps"],
                cwd=project_root,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                output = result.stdout
                if "rapidtrader-api" in output and "rapidtrader-frontend" in output:
                    print("✅ Docker containers are running")
                    print("   - rapidtrader-api")
                    print("   - rapidtrader-frontend")
                    return True
                else:
                    print("❌ Some containers are missing")
                    print(output)
                    return False
            else:
                print(f"❌ Docker compose check failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Docker container test failed: {e}")
            return False
    
    def test_responsive_design(self):
        """Test responsive design elements"""
        print("\n📱 Testing responsive design...")
        
        # This is a basic test - in a real scenario, you'd use Selenium
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            content = response.text
            
            responsive_indicators = [
                "viewport",
                "mobile-web-app-capable",
                "responsive",
                "@media",
                "flex",
                "grid"
            ]
            
            found_indicators = 0
            for indicator in responsive_indicators:
                if indicator in content.lower():
                    found_indicators += 1
            
            if found_indicators >= 3:
                print(f"✅ Responsive design indicators found ({found_indicators}/{len(responsive_indicators)})")
                return True
            else:
                print(f"❌ Limited responsive design indicators ({found_indicators}/{len(responsive_indicators)})")
                return False
                
        except Exception as e:
            print(f"❌ Responsive design test failed: {e}")
            return False
    
    def run_integration_test(self):
        """Run complete integration test"""
        print("🚀 RapidTrader Frontend Integration Test")
        print("Testing complete platform integration...")
        print("=" * 60)
        
        tests = [
            ("API Health", self.test_api_health),
            ("Frontend Health", self.test_frontend_health),
            ("API Endpoints", self.test_api_endpoints),
            ("Symbol System", self.test_symbol_system),
            ("Docker Containers", self.test_docker_containers),
            ("Responsive Design", self.test_responsive_design),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                time.sleep(1)  # Small delay between tests
            except Exception as e:
                print(f"❌ Test '{test_name}' failed with exception: {e}")
        
        print("\n" + "=" * 60)
        print(f"🎯 Integration Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 Complete platform integration successful!")
            print("\n🚀 Your RapidTrader platform is ready:")
            print(f"   🌐 Frontend: {FRONTEND_URL}")
            print(f"   🔧 API: {API_BASE_URL}")
            print(f"   📚 API Docs: {API_BASE_URL}/docs")
            print(f"   🔑 Demo API Key: {DEMO_API_KEY}")
            print("\n✨ Features working:")
            print("   ✅ Responsive design (Mobile, Tablet, Desktop)")
            print("   ✅ Unified symbol system")
            print("   ✅ API integration")
            print("   ✅ Docker containerization")
            print("   ✅ Real-time updates ready")
        else:
            print("⚠️  Some integration tests failed.")
            print("Please check the platform setup and try again.")
        
        return passed == total

def main():
    """Main test function"""
    print("RapidTrader Frontend Integration Test Suite")
    print("Testing complete platform with responsive frontend")
    print()
    
    # Check if services are running
    print("🔍 Checking if services are running...")
    
    api_running = False
    frontend_running = False
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        api_running = response.status_code == 200
    except:
        pass
    
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        frontend_running = response.status_code == 200
    except:
        pass
    
    if not api_running or not frontend_running:
        print("❌ Services are not running. Please start the platform first:")
        print("   ./scripts/rapidtrader-api.sh start")
        print()
        print("Expected services:")
        print(f"   API: {API_BASE_URL} {'✅' if api_running else '❌'}")
        print(f"   Frontend: {FRONTEND_URL} {'✅' if frontend_running else '❌'}")
        return False
    
    print("✅ Services are running, proceeding with integration tests...")
    print()
    
    # Run integration tests
    tester = FrontendIntegrationTest()
    success = tester.run_integration_test()
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
