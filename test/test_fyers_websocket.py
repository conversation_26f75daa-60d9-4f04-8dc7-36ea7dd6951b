#!/usr/bin/env python3
"""
Test script for Fyers WebSocket real-time data streaming.

This script demonstrates how to use the Fyers WebSocket functionality
for real-time market data streaming including live quotes and market depth.
"""

import os
import sys
import time
import signal
import threading
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from broker.fyers_wrapper import FyersBroker
    from broker.fyers_symbol_mapper import to_fyers_symbol
    print("✓ Fyers broker imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


class FyersWebSocketTester:
    """Test class for Fyers WebSocket functionality."""
    
    def __init__(self):
        self.broker = None
        self.running = False
        self.quote_count = 0
        self.depth_count = 0
        self.order_count = 0
        
        # Test symbols
        self.test_symbols = [
            "NSE:SBIN-EQ",
            "NSE:RELIANCE-EQ",
            "NSE:TCS-EQ",
            "NSE:INFY-EQ"
        ]
        
        # Statistics
        self.stats = {
            "start_time": None,
            "quotes_received": 0,
            "depths_received": 0,
            "orders_received": 0,
            "last_quote_time": None,
            "last_depth_time": None
        }
    
    def setup_broker(self):
        """Setup Fyers broker with dummy credentials for testing."""
        print("\n=== Setting up Fyers Broker ===")
        
        try:
            # Initialize broker with dummy credentials
            self.broker = FyersBroker(
                client_id="dummy_client_id",
                access_token="dummy_access_token"
            )
            
            print("✓ Fyers broker initialized")
            return True
            
        except Exception as e:
            print(f"✗ Error setting up broker: {e}")
            return False
    
    def test_websocket_connection(self):
        """Test WebSocket connection."""
        print("\n=== Testing WebSocket Connection ===")
        
        try:
            # Note: This will fail with dummy credentials, but we can test the setup
            success = self.broker.enable_websocket()
            
            if success:
                print("✓ WebSocket connection successful")
                print(f"  Connected: {self.broker.is_websocket_connected()}")
                return True
            else:
                print("✗ WebSocket connection failed (expected with dummy credentials)")
                print("  This is normal for testing without real credentials")
                return False
                
        except Exception as e:
            print(f"✗ WebSocket connection error: {e}")
            return False
    
    def setup_callbacks(self):
        """Setup WebSocket callbacks for testing."""
        print("\n=== Setting up WebSocket Callbacks ===")
        
        try:
            # Quote callback
            def on_quote_update(symbol, quote_data):
                self.quote_count += 1
                self.stats["quotes_received"] += 1
                self.stats["last_quote_time"] = datetime.now()
                
                print(f"📈 Quote Update: {symbol}")
                print(f"   LTP: {quote_data.get('ltp', 'N/A')}")
                print(f"   Volume: {quote_data.get('vol_traded_today', 'N/A')}")
                print(f"   Change: {quote_data.get('ch', 'N/A')}")
                print(f"   Time: {quote_data.get('timestamp', 'N/A')}")
            
            # Depth callback
            def on_depth_update(symbol, depth_data):
                self.depth_count += 1
                self.stats["depths_received"] += 1
                self.stats["last_depth_time"] = datetime.now()
                
                print(f"📊 Depth Update: {symbol}")
                print(f"   Best Bid: {depth_data.get('bid', 'N/A')}")
                print(f"   Best Ask: {depth_data.get('ask', 'N/A')}")
                print(f"   Bid Size: {depth_data.get('bid_size', 'N/A')}")
                print(f"   Ask Size: {depth_data.get('ask_size', 'N/A')}")
            
            # Order callback
            def on_order_update(order_data):
                self.order_count += 1
                self.stats["orders_received"] += 1
                
                print(f"📋 Order Update:")
                print(f"   Order ID: {order_data.get('id', 'N/A')}")
                print(f"   Status: {order_data.get('status', 'N/A')}")
                print(f"   Symbol: {order_data.get('symbol', 'N/A')}")
                print(f"   Quantity: {order_data.get('qty', 'N/A')}")
            
            # Add callbacks to broker
            self.broker.add_quote_callback(on_quote_update)
            self.broker.add_depth_callback(on_depth_update)
            self.broker.add_order_callback(on_order_update)
            
            print("✓ WebSocket callbacks setup complete")
            return True
            
        except Exception as e:
            print(f"✗ Error setting up callbacks: {e}")
            return False
    
    def test_symbol_subscription(self):
        """Test symbol subscription."""
        print("\n=== Testing Symbol Subscription ===")
        
        try:
            if not self.broker.is_websocket_connected():
                print("⚠️  WebSocket not connected, skipping subscription test")
                return False
            
            # Subscribe to live quotes
            print(f"Subscribing to quotes for: {self.test_symbols}")
            quote_success = self.broker.subscribe_live_quotes(self.test_symbols)
            print(f"Quote subscription: {'✓ Success' if quote_success else '✗ Failed'}")
            
            # Subscribe to market depth
            depth_symbols = self.test_symbols[:2]  # Subscribe to fewer symbols for depth
            print(f"Subscribing to depth for: {depth_symbols}")
            depth_success = self.broker.subscribe_market_depth(depth_symbols)
            print(f"Depth subscription: {'✓ Success' if depth_success else '✗ Failed'}")
            
            # Check subscribed symbols
            subscribed = self.broker.get_subscribed_symbols()
            print(f"Subscribed symbols: {subscribed}")
            
            return quote_success or depth_success
            
        except Exception as e:
            print(f"✗ Error in symbol subscription: {e}")
            return False
    
    def test_live_data_access(self):
        """Test accessing live data from cache."""
        print("\n=== Testing Live Data Access ===")
        
        try:
            if not self.broker.is_websocket_connected():
                print("⚠️  WebSocket not connected, skipping live data test")
                return False
            
            # Wait a bit for data to arrive
            print("Waiting for live data...")
            time.sleep(5)
            
            # Test getting live quotes
            for symbol in self.test_symbols[:2]:
                quote = self.broker.get_live_quote(symbol)
                if quote:
                    print(f"✓ Live quote for {symbol}: LTP={quote.get('ltp', 'N/A')}")
                else:
                    print(f"✗ No live quote available for {symbol}")
            
            # Test getting live depth
            for symbol in self.test_symbols[:1]:
                depth = self.broker.get_live_depth(symbol)
                if depth:
                    print(f"✓ Live depth for {symbol}: Bid={depth.get('bid', 'N/A')}, Ask={depth.get('ask', 'N/A')}")
                else:
                    print(f"✗ No live depth available for {symbol}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error accessing live data: {e}")
            return False
    
    def test_websocket_statistics(self):
        """Test WebSocket statistics."""
        print("\n=== Testing WebSocket Statistics ===")
        
        try:
            stats = self.broker.get_websocket_statistics()
            
            print("WebSocket Statistics:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
            
            # Print our internal stats
            print("\nTest Statistics:")
            for key, value in self.stats.items():
                print(f"  {key}: {value}")
            
            print(f"  Total callbacks: {self.quote_count + self.depth_count + self.order_count}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error getting statistics: {e}")
            return False
    
    def run_live_test(self, duration: int = 30):
        """Run live test for specified duration."""
        print(f"\n=== Running Live Test for {duration} seconds ===")
        
        if not self.broker.is_websocket_connected():
            print("⚠️  WebSocket not connected, cannot run live test")
            return
        
        self.running = True
        self.stats["start_time"] = datetime.now()
        
        print("Live test started. Press Ctrl+C to stop early.")
        print("Monitoring real-time data...")
        
        try:
            start_time = time.time()
            while self.running and (time.time() - start_time) < duration:
                time.sleep(1)
                
                # Print periodic updates
                elapsed = int(time.time() - start_time)
                if elapsed % 10 == 0:
                    print(f"  [{elapsed}s] Quotes: {self.quote_count}, Depths: {self.depth_count}, Orders: {self.order_count}")
            
            print(f"\nLive test completed after {elapsed} seconds")
            
        except KeyboardInterrupt:
            print("\nLive test interrupted by user")
        finally:
            self.running = False
    
    def cleanup(self):
        """Cleanup resources."""
        print("\n=== Cleanup ===")
        
        try:
            if self.broker:
                self.broker.disable_websocket()
            print("✓ Cleanup completed")
            
        except Exception as e:
            print(f"✗ Error during cleanup: {e}")
    
    def run_all_tests(self):
        """Run all WebSocket tests."""
        print("Fyers WebSocket Test Suite")
        print("=" * 50)
        
        # Setup
        if not self.setup_broker():
            return False
        
        # Test WebSocket connection
        ws_connected = self.test_websocket_connection()
        
        # Setup callbacks (even if not connected, for testing)
        self.setup_callbacks()
        
        if ws_connected:
            # Test subscription
            self.test_symbol_subscription()
            
            # Test live data access
            self.test_live_data_access()
            
            # Run live test
            self.run_live_test(30)
        
        # Test statistics
        self.test_websocket_statistics()
        
        # Cleanup
        self.cleanup()
        
        print("\n" + "=" * 50)
        print("Test suite completed")
        return True


def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully."""
    print("\nReceived interrupt signal, stopping...")
    sys.exit(0)


def main():
    """Main test function."""
    # Setup signal handler
    signal.signal(signal.SIGINT, signal_handler)
    
    # Create and run tester
    tester = FyersWebSocketTester()
    
    try:
        tester.run_all_tests()
    except Exception as e:
        print(f"Test suite error: {e}")
        tester.cleanup()
    
    print("\nNote: This test uses dummy credentials, so WebSocket connection will fail.")
    print("To test with real data, update the credentials in the script.")


if __name__ == "__main__":
    main()
