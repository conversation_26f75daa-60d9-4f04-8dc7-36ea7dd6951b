#!/usr/bin/env python3
"""
Comprehensive test suite for RapidTrader core functionality.

This test suite covers:
- CLI initialization and health checks
- Configuration management
- Strategy management
- Broker integrations
- Data management
- Backtesting engine
- Money management
- API Gateway functionality
"""

import os
import sys
import json
import tempfile
import shutil
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestRapidTraderCore(unittest.TestCase):
    """Test core RapidTrader functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test directory structure
        self.userdata_dir = os.path.join(self.test_dir, "userdata")
        self.configs_dir = os.path.join(self.userdata_dir, "configs")
        self.strategies_dir = os.path.join(self.userdata_dir, "strategies")
        self.logs_dir = os.path.join(self.userdata_dir, "logs")
        self.results_dir = os.path.join(self.userdata_dir, "results")
        
        for directory in [self.userdata_dir, self.configs_dir, self.strategies_dir, 
                         self.logs_dir, self.results_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_rapidtrader_initialization(self):
        """Test RapidTrader CLI initialization"""
        try:
            from core.rapidtrader import init_components, check_system_health
            
            # Test initialization
            init_components()
            
            # Test health check
            health_status = check_system_health()
            
            # Verify health status structure
            self.assertIn('python_version', health_status)
            self.assertIn('required_dirs', health_status)
            self.assertIn('broker_modules', health_status)
            self.assertIn('docker_available', health_status)
            
            # Python version should be OK (we're running the test)
            self.assertTrue(health_status['python_version'])
            
        except ImportError as e:
            self.skipTest(f"RapidTrader core not available: {e}")
    
    def test_configuration_management(self):
        """Test configuration creation and management"""
        try:
            from core.rapidtrader import load_config
            
            # Create test configuration
            test_config = {
                "exchange": {"name": "fyers"},
                "broker": {"name": "fyers", "dry_run": True},
                "dry_run": True,
                "stake_currency": "INR",
                "stake_amount": 1000,
                "max_open_trades": 3,
                "strategies": [
                    {"name": "TestStrategy", "enabled": True, "params": {}}
                ]
            }
            
            config_file = os.path.join(self.configs_dir, "test-config.json")
            with open(config_file, 'w') as f:
                json.dump(test_config, f, indent=4)
            
            # Test loading configuration
            loaded_config = load_config(config_file)
            
            # Verify configuration loaded correctly
            self.assertEqual(loaded_config["exchange"]["name"], "fyers")
            self.assertEqual(loaded_config["stake_currency"], "INR")
            self.assertEqual(loaded_config["stake_amount"], 1000)
            self.assertTrue(loaded_config["dry_run"])
            
        except ImportError as e:
            self.skipTest(f"Configuration management not available: {e}")
    
    def test_strategy_creation(self):
        """Test strategy creation and validation"""
        # Create test strategy file
        strategy_content = '''
from userdata.strategies.base_strategy import BaseStrategy
import pandas as pd

class TestStrategy(BaseStrategy):
    def populate_indicators(self, dataframe):
        dataframe['sma_20'] = dataframe['close'].rolling(20).mean()
        return dataframe
    
    def populate_buy_signals(self, dataframe):
        dataframe['buy'] = dataframe['close'] > dataframe['sma_20']
        return dataframe
    
    def populate_sell_signals(self, dataframe):
        dataframe['sell'] = dataframe['close'] < dataframe['sma_20']
        return dataframe
'''
        
        strategy_file = os.path.join(self.strategies_dir, "TestStrategy.py")
        with open(strategy_file, 'w') as f:
            f.write(strategy_content)
        
        # Verify strategy file exists
        self.assertTrue(os.path.exists(strategy_file))
        
        # Test strategy listing (would be done by CLI)
        strategies = [f[:-3] for f in os.listdir(self.strategies_dir)
                     if f.endswith(".py") and not f.startswith("__")]
        
        self.assertIn("TestStrategy", strategies)


class TestBrokerIntegrations(unittest.TestCase):
    """Test broker integration functionality"""
    
    def test_fyers_broker_import(self):
        """Test Fyers broker module import"""
        try:
            from broker.fyers_wrapper import FyersBroker
            from broker.fyers_symbol_mapper import FyersSymbolMapper
            from broker.fyers_websocket import FyersWebSocketClient
            
            # Test basic instantiation (without credentials)
            self.assertTrue(FyersBroker)
            self.assertTrue(FyersSymbolMapper)
            self.assertTrue(FyersWebSocketClient)
            
        except ImportError as e:
            self.skipTest(f"Fyers broker module not available: {e}")
    
    def test_dhan_broker_import(self):
        """Test DhanHQ broker module import"""
        try:
            from broker.dhan_wrapper import DhanBroker
            from broker.symbol_mapper import SymbolMapper
            
            # Test basic instantiation (without credentials)
            self.assertTrue(DhanBroker)
            self.assertTrue(SymbolMapper)
            
        except ImportError as e:
            self.skipTest(f"DhanHQ broker module not available: {e}")
    
    def test_symbol_mapping(self):
        """Test symbol mapping functionality"""
        try:
            from broker.fyers_symbol_mapper import to_fyers_symbol, from_fyers_symbol
            
            # Test symbol conversion
            rapidtrader_symbol = "RELIANCE-EQ"
            fyers_symbol = to_fyers_symbol(rapidtrader_symbol)
            converted_back = from_fyers_symbol(fyers_symbol)
            
            # Should be able to convert back and forth
            self.assertIsNotNone(fyers_symbol)
            self.assertIsNotNone(converted_back)
            
        except ImportError as e:
            self.skipTest(f"Symbol mapping not available: {e}")


class TestDataManagement(unittest.TestCase):
    """Test data management functionality"""
    
    def test_data_manager_import(self):
        """Test data manager module import"""
        try:
            from core.data_manager import DataManager
            from data.data_manager import DataManager as DataManagerV2
            
            # Test basic instantiation
            self.assertTrue(DataManager)
            self.assertTrue(DataManagerV2)
            
        except ImportError as e:
            self.skipTest(f"Data manager not available: {e}")
    
    def test_yfinance_fetcher(self):
        """Test yfinance data fetcher"""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            fetcher = YFinanceFetcher()
            
            # Test basic functionality (without actual data fetch)
            self.assertIsNotNone(fetcher)
            self.assertTrue(hasattr(fetcher, 'fetch_data'))
            
        except ImportError as e:
            self.skipTest(f"YFinance fetcher not available: {e}")


class TestBacktestingEngine(unittest.TestCase):
    """Test backtesting engine functionality"""
    
    def test_backtest_engine_import(self):
        """Test backtest engine import"""
        try:
            from core.backtest_engine import BacktestEngine
            from core.trade_simulator import TradeSimulator
            from core.backtest_results import BacktestResults
            
            # Test basic instantiation
            self.assertTrue(BacktestEngine)
            self.assertTrue(TradeSimulator)
            self.assertTrue(BacktestResults)
            
        except ImportError as e:
            self.skipTest(f"Backtest engine not available: {e}")
    
    def test_optimized_backtest_engine(self):
        """Test optimized backtest engine"""
        try:
            from core.optimized_backtest_engine import OptimizedBacktestEngine
            
            # Test basic instantiation
            self.assertTrue(OptimizedBacktestEngine)
            
        except ImportError as e:
            self.skipTest(f"Optimized backtest engine not available: {e}")


class TestMoneyManagement(unittest.TestCase):
    """Test money management functionality"""
    
    def test_money_manager_import(self):
        """Test money manager import"""
        try:
            from money_management.core import MoneyManager
            from money_management.risk_manager import RiskManager
            from money_management.position_sizer import PositionSizer
            
            # Test basic instantiation
            self.assertTrue(MoneyManager)
            self.assertTrue(RiskManager)
            self.assertTrue(PositionSizer)
            
        except ImportError as e:
            self.skipTest(f"Money management not available: {e}")


class TestAPIGateway(unittest.TestCase):
    """Test API Gateway functionality"""
    
    def test_api_gateway_import(self):
        """Test API Gateway import"""
        try:
            from api_gateway.main import APIGateway
            from api_gateway.auth import auth_manager
            from api_gateway.broker_interface import broker_manager
            
            # Test basic instantiation
            self.assertTrue(APIGateway)
            self.assertTrue(auth_manager)
            self.assertTrue(broker_manager)
            
        except ImportError as e:
            self.skipTest(f"API Gateway not available: {e}")


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestRapidTraderCore,
        TestBrokerIntegrations,
        TestDataManagement,
        TestBacktestingEngine,
        TestMoneyManagement,
        TestAPIGateway
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
