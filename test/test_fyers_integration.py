#!/usr/bin/env python3
"""
Test script for Fyers API v3 integration.

This script tests the Fyers broker wrapper implementation.
"""

import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from broker.fyers_wrapper import (
        FyersBroker,
        FyersTransactionType,
        FyersExchange,
        FyersProductType,
        FyersOrderType,
        FyersValidity,
        FyersOrderStatus
    )
    from broker.fyers_symbol_mapper import (
        FyersSymbolMapper,
        to_fyers_symbol,
        from_fyers_symbol,
        validate_fyers_symbol
    )
    print("✓ Fyers broker imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

def test_symbol_mapping():
    """Test Fyers symbol mapping functionality."""
    print("\n=== Testing Fyers Symbol Mapping ===")

    # Test symbol conversion
    test_cases = [
        ("SBIN", "NSE_EQ"),
        ("RELIANCE", "NSE_EQ"),
        ("BANKNIFTY", "NSE_INDEX"),
        ("NIFTY", "NSE_INDEX"),
    ]

    for symbol, exchange in test_cases:
        try:
            fyers_symbol = to_fyers_symbol(symbol, exchange)
            print(f"  {symbol} ({exchange}) -> {fyers_symbol}")

            # Test validation
            if fyers_symbol:
                is_valid = validate_fyers_symbol(fyers_symbol)
                print(f"    Valid: {is_valid}")

                # Test reverse mapping
                parsed = from_fyers_symbol(fyers_symbol)
                print(f"    Parsed: {parsed}")
            else:
                print(f"    No mapping found for {symbol}")

        except Exception as e:
            print(f"  ✗ Error with {symbol}: {e}")

def test_broker_initialization():
    """Test Fyers broker initialization."""
    print("\n=== Testing Fyers Broker Initialization ===")

    # Test with dummy credentials (will fail authentication but should initialize)
    try:
        broker = FyersBroker(
            client_id="dummy_client_id",
            access_token="dummy_access_token"
        )
        print("✓ Fyers broker initialized successfully")
        print(f"  Client ID: {broker.client_id}")
        print(f"  Has access token: {bool(broker.access_token)}")

    except Exception as e:
        print(f"✗ Broker initialization error: {e}")

def test_order_data_structure():
    """Test order data structure creation."""
    print("\n=== Testing Order Data Structure ===")

    try:
        # Test market order data
        order_data = {
            "symbol": "NSE:SBIN-EQ",
            "qty": 10,
            "type": FyersOrderType.MARKET.value,
            "side": FyersTransactionType.BUY.value,
            "productType": FyersProductType.CNC.value,
            "limitPrice": 0,
            "stopPrice": 0,
            "validity": FyersValidity.DAY.value,
            "disclosedQty": 0,
            "offlineOrder": False,
            "stopLoss": 0,
            "takeProfit": 0
        }

        print("✓ Market order data structure:")
        for key, value in order_data.items():
            print(f"  {key}: {value}")

        # Test limit order data
        limit_order_data = order_data.copy()
        limit_order_data.update({
            "type": FyersOrderType.LIMIT.value,
            "limitPrice": 500.0
        })

        print("\n✓ Limit order data structure:")
        for key, value in limit_order_data.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"✗ Order data structure error: {e}")

def test_enum_values():
    """Test enum values."""
    print("\n=== Testing Enum Values ===")

    try:
        print("Transaction Types:")
        for tx_type in FyersTransactionType:
            print(f"  {tx_type.name}: {tx_type.value}")

        print("\nOrder Types:")
        for order_type in FyersOrderType:
            print(f"  {order_type.name}: {order_type.value}")

        print("\nProduct Types:")
        for product_type in FyersProductType:
            print(f"  {product_type.name}: {product_type.value}")

        print("\nValidity Types:")
        for validity in FyersValidity:
            print(f"  {validity.name}: {validity.value}")

        print("\nExchanges:")
        for exchange in FyersExchange:
            print(f"  {exchange.name}: {exchange.value}")

    except Exception as e:
        print(f"✗ Enum values error: {e}")

def main():
    """Main test function."""
    print("Fyers API v3 Integration Test")
    print("=" * 40)
    print(f"Test started at: {datetime.now()}")

    # Run tests
    test_symbol_mapping()
    test_broker_initialization()
    test_order_data_structure()
    test_enum_values()

    print(f"\nTest completed at: {datetime.now()}")
    print("=" * 40)

if __name__ == "__main__":
    main()
