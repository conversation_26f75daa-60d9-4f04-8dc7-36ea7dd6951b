#!/usr/bin/env python3
"""
Test script for Independent Dry Run Engine

This script tests the standalone paper trading engine that:
- Fetches delayed data from yfinance (15-20 min delay)
- Runs strategies with indicators from config
- Simulates buy/sell orders independently
- Logs all trades and follows strategy logic
- No dependency on DhanHQ or any broker module
"""

import os
import sys
import logging
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_config_loading():
    """Test configuration loading."""
    print("=" * 60)
    print("Testing Configuration Loading")
    print("=" * 60)
    
    try:
        from core.independent_dry_run import IndependentDryRunEngine
        
        config_path = "userdata/config/dry_run_config.json"
        engine = IndependentDryRunEngine(config_path, 100000)
        
        print(f"✓ Configuration loaded successfully")
        print(f"  - Symbols: {len(engine.symbols)}")
        print(f"  - Strategies: {len(engine.strategy_configs)}")
        print(f"  - Initial Capital: ₹{engine.initial_capital:,.2f}")
        print(f"  - Data Interval: {engine.data_interval}")
        print(f"  - Refresh Interval: {engine.refresh_interval}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return False

def test_market_data_fetching():
    """Test market data fetching from yfinance."""
    print("\n" + "=" * 60)
    print("Testing Market Data Fetching")
    print("=" * 60)
    
    try:
        from core.independent_dry_run import IndependentDryRunEngine
        
        config_path = "userdata/config/dry_run_config.json"
        engine = IndependentDryRunEngine(config_path, 100000)
        
        # Test fetching data for a few symbols
        test_symbols = ["RELIANCE", "TCS", "HDFCBANK"]
        
        for symbol in test_symbols:
            print(f"\nTesting {symbol}:")
            market_data = engine._fetch_market_data(symbol)
            
            if market_data:
                print(f"  ✓ Data fetched successfully")
                print(f"    Price: ₹{market_data['price']:.2f}")
                print(f"    Change: {market_data['change_percent']:.2f}%")
                print(f"    Volume: {market_data['volume']:,}")
                print(f"    Indicators: {len(market_data['indicators'])} calculated")
            else:
                print(f"  ❌ Failed to fetch data")
        
        return True
        
    except Exception as e:
        print(f"❌ Market data fetching failed: {e}")
        return False

def test_strategy_signals():
    """Test strategy signal evaluation."""
    print("\n" + "=" * 60)
    print("Testing Strategy Signal Evaluation")
    print("=" * 60)
    
    try:
        from core.independent_dry_run import IndependentDryRunEngine
        
        config_path = "userdata/config/dry_run_config.json"
        engine = IndependentDryRunEngine(config_path, 100000)
        
        # Fetch data and test signals
        symbol = "RELIANCE"
        market_data = engine._fetch_market_data(symbol)
        
        if market_data:
            signal, confidence = engine._evaluate_strategy_signals(symbol, market_data)
            
            print(f"✓ Strategy evaluation successful for {symbol}")
            print(f"  Signal: {signal}")
            print(f"  Confidence: {confidence:.2f}")
            print(f"  RSI: {market_data['indicators'].get('rsi', 'N/A')}")
            print(f"  MACD: {market_data['indicators'].get('macd', 'N/A')}")
            print(f"  BB Position: {market_data['indicators'].get('bb_position', 'N/A')}")
            
            return True
        else:
            print(f"❌ Could not fetch market data for signal testing")
            return False
        
    except Exception as e:
        print(f"❌ Strategy signal evaluation failed: {e}")
        return False

def test_order_simulation():
    """Test order placement and execution simulation."""
    print("\n" + "=" * 60)
    print("Testing Order Simulation")
    print("=" * 60)
    
    try:
        from core.independent_dry_run import IndependentDryRunEngine
        
        config_path = "userdata/config/dry_run_config.json"
        engine = IndependentDryRunEngine(config_path, 100000)
        
        # Test buy order
        symbol = "RELIANCE"
        quantity = 10
        price = 2500.0
        
        print(f"Testing BUY order: {quantity} {symbol} @ ₹{price}")
        order_id = engine._place_order(symbol, quantity, "BUY", price)
        
        print(f"  ✓ Order placed: {order_id}")
        print(f"  Available Balance: ₹{engine.available_balance:,.2f}")
        print(f"  Positions: {len(engine.positions)}")
        
        # Test sell order
        if symbol in engine.positions:
            print(f"\nTesting SELL order: {quantity} {symbol} @ ₹{price + 100}")
            sell_order_id = engine._place_order(symbol, quantity, "SELL", price + 100)
            
            print(f"  ✓ Sell order placed: {sell_order_id}")
            print(f"  Available Balance: ₹{engine.available_balance:,.2f}")
            print(f"  Total P&L: ₹{engine.total_pnl:,.2f}")
            print(f"  Total Trades: {engine.total_trades}")
        
        return True
        
    except Exception as e:
        print(f"❌ Order simulation failed: {e}")
        return False

def test_portfolio_tracking():
    """Test portfolio tracking and metrics."""
    print("\n" + "=" * 60)
    print("Testing Portfolio Tracking")
    print("=" * 60)
    
    try:
        from core.independent_dry_run import IndependentDryRunEngine
        
        config_path = "userdata/config/dry_run_config.json"
        engine = IndependentDryRunEngine(config_path, 100000)
        
        # Place some test orders
        engine._place_order("RELIANCE", 10, "BUY", 2500.0)
        engine._place_order("TCS", 5, "BUY", 3500.0)
        
        # Update portfolio metrics
        engine._update_portfolio_metrics()
        
        # Get portfolio summary
        summary = engine.get_portfolio_summary()
        
        print(f"✓ Portfolio tracking successful")
        print(f"  Initial Capital: ₹{summary['initial_capital']:,.2f}")
        print(f"  Current Value: ₹{summary['current_value']:,.2f}")
        print(f"  Available Balance: ₹{summary['available_balance']:,.2f}")
        print(f"  Total Return: {summary['total_return']:.2f}%")
        print(f"  Open Positions: {summary['open_positions']}")
        print(f"  Total Trades: {summary['total_trades']}")
        
        # Test positions
        positions = engine.get_positions()
        print(f"\n  Current Positions:")
        for symbol, position in positions.items():
            print(f"    {symbol}: {position['quantity']} @ ₹{position['avg_price']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Portfolio tracking failed: {e}")
        return False

def test_simulation_loop():
    """Test the main simulation loop (short duration)."""
    print("\n" + "=" * 60)
    print("Testing Simulation Loop (10 seconds)")
    print("=" * 60)
    
    try:
        from core.independent_dry_run import IndependentDryRunEngine
        
        config_path = "userdata/config/dry_run_config.json"
        engine = IndependentDryRunEngine(config_path, 100000)
        
        print("Starting simulation...")
        engine.start_simulation()
        
        # Run for 10 seconds
        time.sleep(10)
        
        print("Stopping simulation...")
        engine.stop_simulation()
        
        # Get final summary
        summary = engine.get_portfolio_summary()
        
        print(f"✓ Simulation loop completed successfully")
        print(f"  Final Value: ₹{summary['current_value']:,.2f}")
        print(f"  Total Return: {summary['total_return']:.2f}%")
        print(f"  Total Trades: {summary['total_trades']}")
        print(f"  Win Rate: {summary['win_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Simulation loop failed: {e}")
        return False

def main():
    """Main test function."""
    print("Independent Dry Run Engine Test Suite")
    print("=" * 60)
    
    # Check if config file exists
    config_path = "userdata/config/dry_run_config.json"
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        print("Please ensure the config file exists before running tests.")
        return False
    
    print(f"✓ Configuration file found: {config_path}")
    
    # Run tests
    tests = [
        ("Configuration Loading", test_config_loading),
        ("Market Data Fetching", test_market_data_fetching),
        ("Strategy Signals", test_strategy_signals),
        ("Order Simulation", test_order_simulation),
        ("Portfolio Tracking", test_portfolio_tracking),
        ("Simulation Loop", test_simulation_loop),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"\n❌ {test_name} test failed")
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
    
    # Final results
    print("\n" + "=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! Independent Dry Run Engine is working perfectly.")
        print("\n✅ Key Features Verified:")
        print("   • Configuration loading and parsing")
        print("   • Market data fetching from yfinance")
        print("   • Technical indicator calculations")
        print("   • Strategy signal evaluation")
        print("   • Order placement and execution simulation")
        print("   • Portfolio tracking and P&L calculation")
        print("   • Complete simulation loop")
        print("\n🚀 Ready for paper trading!")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    main()
