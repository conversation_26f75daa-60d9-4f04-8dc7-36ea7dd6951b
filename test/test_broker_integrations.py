#!/usr/bin/env python3
"""
Broker Integration Tests

Tests broker integrations including Fyers, DhanHQ, and symbol mapping.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestFyersBrokerIntegration(unittest.TestCase):
    """Test Fyers broker integration"""
    
    def test_fyers_broker_import(self):
        """Test Fyers broker module import"""
        try:
            from broker.fyers_wrapper import FyersBroker
            from broker.fyers_symbol_mapper import FyersSymbolMapper
            from broker.fyers_websocket import FyersWebSocketClient
            
            # Test basic class availability
            self.assertTrue(FyersBroker)
            self.assertTrue(FyersSymbolMapper)
            self.assertTrue(FyersWebSocketClient)
            
        except ImportError as e:
            self.skipTest(f"Fyers broker module not available: {e}")
    
    def test_fyers_broker_initialization(self):
        """Test Fyers broker initialization without credentials"""
        try:
            from broker.fyers_wrapper import FyersBroker
            
            # Should be able to create instance without credentials
            broker = FyersBroker()
            self.assertIsNotNone(broker)
            
        except ImportError as e:
            self.skipTest(f"Fyers broker not available: {e}")
        except Exception as e:
            # Expected if no credentials provided
            pass
    
    def test_fyers_symbol_mapping(self):
        """Test Fyers symbol mapping functionality"""
        try:
            from broker.fyers_symbol_mapper import to_fyers_symbol, from_fyers_symbol
            
            # Test symbol conversion
            rapidtrader_symbol = "RELIANCE-EQ"
            fyers_symbol = to_fyers_symbol(rapidtrader_symbol)
            
            # Should return a valid Fyers symbol format
            if fyers_symbol:
                self.assertIsInstance(fyers_symbol, str)
                # Fyers symbols typically have format like "NSE:RELIANCE-EQ"
                
            # Test reverse conversion
            if fyers_symbol:
                converted_back = from_fyers_symbol(fyers_symbol)
                if converted_back:
                    self.assertIsInstance(converted_back, str)
            
        except ImportError as e:
            self.skipTest(f"Fyers symbol mapping not available: {e}")
    
    def test_fyers_websocket_client(self):
        """Test Fyers WebSocket client initialization"""
        try:
            from broker.fyers_websocket import FyersWebSocketClient
            
            # Should be able to create instance
            ws_client = FyersWebSocketClient(
                access_token="dummy_token",
                client_id="dummy_client"
            )
            self.assertIsNotNone(ws_client)
            
        except ImportError as e:
            self.skipTest(f"Fyers WebSocket client not available: {e}")


class TestDhanBrokerIntegration(unittest.TestCase):
    """Test DhanHQ broker integration"""
    
    def test_dhan_broker_import(self):
        """Test DhanHQ broker module import"""
        try:
            from broker.dhan_wrapper import DhanBroker
            from broker.symbol_mapper import SymbolMapper
            
            # Test basic class availability
            self.assertTrue(DhanBroker)
            self.assertTrue(SymbolMapper)
            
        except ImportError as e:
            self.skipTest(f"DhanHQ broker module not available: {e}")
    
    def test_dhan_broker_initialization(self):
        """Test DhanHQ broker initialization without credentials"""
        try:
            from broker.dhan_wrapper import DhanBroker
            
            # Should be able to create instance without credentials
            broker = DhanBroker()
            self.assertIsNotNone(broker)
            
        except ImportError as e:
            self.skipTest(f"DhanHQ broker not available: {e}")
        except Exception as e:
            # Expected if no credentials provided
            pass
    
    def test_dhan_symbol_mapping(self):
        """Test DhanHQ symbol mapping functionality"""
        try:
            from broker.symbol_mapper import symbol_to_security_id, security_id_to_symbol
            
            # Test symbol to security ID conversion
            symbol = "RELIANCE"
            security_id = symbol_to_security_id(symbol)
            
            if security_id:
                self.assertIsInstance(security_id, (int, str))
                
                # Test reverse conversion
                converted_symbol = security_id_to_symbol(security_id)
                if converted_symbol:
                    self.assertIsInstance(converted_symbol, str)
            
        except ImportError as e:
            self.skipTest(f"DhanHQ symbol mapping not available: {e}")


class TestBrokerConstants(unittest.TestCase):
    """Test broker constants and enums"""
    
    def test_fyers_constants(self):
        """Test Fyers constants"""
        try:
            from broker.fyers_wrapper import (
                FyersTransactionType, FyersExchange, FyersProductType,
                FyersOrderType, FyersValidity
            )
            
            # Test enum availability
            self.assertTrue(hasattr(FyersTransactionType, 'BUY'))
            self.assertTrue(hasattr(FyersTransactionType, 'SELL'))
            self.assertTrue(hasattr(FyersExchange, 'NSE'))
            self.assertTrue(hasattr(FyersProductType, 'MIS'))
            
        except ImportError as e:
            self.skipTest(f"Fyers constants not available: {e}")
    
    def test_dhan_constants(self):
        """Test DhanHQ constants"""
        try:
            from broker.dhan_wrapper import (
                ExchangeSegment, TransactionType, ProductType,
                OrderType, Validity
            )
            
            # Test enum availability
            self.assertTrue(hasattr(TransactionType, 'BUY'))
            self.assertTrue(hasattr(TransactionType, 'SELL'))
            self.assertTrue(hasattr(ExchangeSegment, 'NSE_EQ'))
            self.assertTrue(hasattr(ProductType, 'MIS'))
            
        except ImportError as e:
            self.skipTest(f"DhanHQ constants not available: {e}")


class TestUnifiedBrokerInterface(unittest.TestCase):
    """Test unified broker interface"""
    
    def test_broker_module_import(self):
        """Test broker module can be imported"""
        try:
            import broker
            
            # Should be able to import broker module
            self.assertIsNotNone(broker)
            
        except ImportError as e:
            self.skipTest(f"Broker module not available: {e}")
    
    def test_broker_init_file(self):
        """Test broker __init__.py exports"""
        try:
            from broker import DhanBroker
            
            # Should be able to import DhanBroker from broker package
            self.assertTrue(DhanBroker)
            
        except ImportError as e:
            self.skipTest(f"DhanBroker not exported from broker package: {e}")
    
    def test_fyers_exports(self):
        """Test Fyers exports from broker package"""
        try:
            from broker import FyersBroker, FyersSymbolMapper
            
            # Should be able to import Fyers classes
            self.assertTrue(FyersBroker)
            self.assertTrue(FyersSymbolMapper)
            
        except ImportError as e:
            self.skipTest(f"Fyers classes not exported from broker package: {e}")


class TestSymbolMappingSystem(unittest.TestCase):
    """Test unified symbol mapping system"""
    
    def test_symbol_mapper_import(self):
        """Test symbol mapper can be imported"""
        try:
            from broker.symbol_mapper import SymbolMapper
            
            mapper = SymbolMapper()
            self.assertIsNotNone(mapper)
            
        except ImportError as e:
            self.skipTest(f"Symbol mapper not available: {e}")
    
    def test_fyers_symbol_mapper(self):
        """Test Fyers symbol mapper"""
        try:
            from broker.fyers_symbol_mapper import FyersSymbolMapper
            
            mapper = FyersSymbolMapper()
            self.assertIsNotNone(mapper)
            
            # Test basic mapping functionality
            if hasattr(mapper, 'to_fyers_symbol'):
                result = mapper.to_fyers_symbol("RELIANCE-EQ")
                # Should return something or None
                self.assertTrue(result is None or isinstance(result, str))
            
        except ImportError as e:
            self.skipTest(f"Fyers symbol mapper not available: {e}")
    
    def test_symbol_validation(self):
        """Test symbol validation"""
        try:
            from broker.fyers_symbol_mapper import validate_fyers_symbol
            
            # Test with valid format
            valid_symbol = "NSE:RELIANCE-EQ"
            is_valid = validate_fyers_symbol(valid_symbol)
            
            # Should return boolean
            self.assertIsInstance(is_valid, bool)
            
        except ImportError as e:
            self.skipTest(f"Symbol validation not available: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
