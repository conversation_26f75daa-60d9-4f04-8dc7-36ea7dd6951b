#!/usr/bin/env python3
"""
RapidTrader Data Module Test Suite

This script provides comprehensive tests for the data module of the RapidTrader trading engine.
"""

import os
import sys
import unittest
import logging
import json
import time
import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np
import tempfile
import shutil

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("data_module_test")


class BaseDataFetcherTest(unittest.TestCase):
    """Test cases for the BaseDataFetcher class."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
    
    def test_base_fetcher_initialization(self):
        """Test BaseDataFetcher initialization."""
        try:
            from data.base_fetcher import BaseDataFetcher
            
            fetcher = BaseDataFetcher()
            self.assertIsNotNone(fetcher)
        except Exception as e:
            self.fail(f"BaseDataFetcher initialization failed: {e}")
    
    def test_base_fetcher_abstract_methods(self):
        """Test BaseDataFetcher abstract methods."""
        try:
            from data.base_fetcher import BaseDataFetcher
            
            fetcher = BaseDataFetcher()
            
            # These methods should raise NotImplementedError
            with self.assertRaises(NotImplementedError):
                fetcher.fetch_ohlcv("AAPL", "1d")
            
            with self.assertRaises(NotImplementedError):
                fetcher.fetch_multiple_ohlcv(["AAPL"], "1d")
            
            with self.assertRaises(NotImplementedError):
                fetcher.get_available_timeframes()
            
            with self.assertRaises(NotImplementedError):
                fetcher.get_available_symbols()
        except Exception as e:
            self.fail(f"BaseDataFetcher abstract methods test failed: {e}")


class YFinanceFetcherTest(unittest.TestCase):
    """Test cases for the YFinanceFetcher class."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
    
    def test_yfinance_fetcher_initialization(self):
        """Test YFinanceFetcher initialization."""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            fetcher = YFinanceFetcher()
            self.assertIsNotNone(fetcher)
        except Exception as e:
            self.fail(f"YFinanceFetcher initialization failed: {e}")
    
    def test_yfinance_fetcher_timeframes(self):
        """Test YFinanceFetcher timeframes."""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            fetcher = YFinanceFetcher()
            timeframes = fetcher.get_available_timeframes()
            
            # Check if common timeframes are available
            self.assertIn("1d", timeframes)
            self.assertIn("1wk", timeframes)
            self.assertIn("1mo", timeframes)
        except Exception as e:
            self.fail(f"YFinanceFetcher timeframes test failed: {e}")


class DataManagerTest(unittest.TestCase):
    """Test cases for the DataManager class."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.csv_dir = os.path.join(self.temp_dir, "csv")
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
        shutil.rmtree(self.temp_dir)
    
    def test_data_manager_initialization(self):
        """Test DataManager initialization."""
        try:
            from data.data_manager import DataManager
            from data.yfinance_fetcher import YFinanceFetcher
            
            data_fetcher = YFinanceFetcher()
            data_manager = DataManager(
                data_fetcher=data_fetcher,
                db_path=self.db_path,
                csv_dir=self.csv_dir
            )
            
            self.assertIsNotNone(data_manager)
            self.assertEqual(data_manager.data_fetcher, data_fetcher)
            self.assertEqual(data_manager.db_path, self.db_path)
            self.assertEqual(data_manager.csv_dir, self.csv_dir)
            
            # Check if directories were created
            self.assertTrue(os.path.exists(os.path.dirname(self.db_path)))
            self.assertTrue(os.path.exists(self.csv_dir))
        except Exception as e:
            self.fail(f"DataManager initialization failed: {e}")


class DownloadDataTest(unittest.TestCase):
    """Test cases for the download_data module."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
    
    def test_download_data_import(self):
        """Test download_data module import."""
        try:
            import data.download_data
            
            self.assertIsNotNone(data.download_data)
        except Exception as e:
            self.fail(f"download_data module import failed: {e}")
    
    def test_download_data_functions(self):
        """Test download_data module functions."""
        try:
            from data.download_data import validate_timeframe, parse_date
            
            # Test validate_timeframe
            self.assertEqual(validate_timeframe("1d"), "1d")
            self.assertEqual(validate_timeframe("1m"), "1m")
            self.assertEqual(validate_timeframe("daily"), "1d")
            
            # Test parse_date
            self.assertEqual(parse_date("2023-01-01").date(), datetime.date(2023, 1, 1))
            self.assertEqual(parse_date("01-01-2023").date(), datetime.date(2023, 1, 1))
            self.assertEqual(parse_date("20230101").date(), datetime.date(2023, 1, 1))
        except Exception as e:
            self.fail(f"download_data functions test failed: {e}")


class FetcherTest(unittest.TestCase):
    """Test cases for the Fetcher class."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
    
    def test_fetcher_initialization(self):
        """Test Fetcher initialization."""
        try:
            from data.fetcher import Fetcher
            
            fetcher = Fetcher()
            self.assertIsNotNone(fetcher)
        except Exception as e:
            self.fail(f"Fetcher initialization failed: {e}")
    
    def test_fetcher_sources(self):
        """Test Fetcher sources."""
        try:
            from data.fetcher import Fetcher
            
            fetcher = Fetcher()
            sources = fetcher.get_available_sources()
            
            # Check if yfinance is available
            self.assertIn("yfinance", sources)
        except Exception as e:
            self.fail(f"Fetcher sources test failed: {e}")


class DataCleanupTest(unittest.TestCase):
    """Test cases for the data_cleanup module."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
        shutil.rmtree(self.temp_dir)
    
    def test_data_cleanup_import(self):
        """Test data_cleanup module import."""
        try:
            import data.data_cleanup
            
            self.assertIsNotNone(data.data_cleanup)
        except Exception as e:
            self.fail(f"data_cleanup module import failed: {e}")


class DataOptimizerTest(unittest.TestCase):
    """Test cases for the data_optimizer module."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time
        shutil.rmtree(self.temp_dir)
    
    def test_data_optimizer_import(self):
        """Test data_optimizer module import."""
        try:
            import data.data_optimizer
            
            self.assertIsNotNone(data.data_optimizer)
        except Exception as e:
            self.fail(f"data_optimizer module import failed: {e}")


def run_tests():
    """Run all tests."""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(loader.loadTestsFromTestCase(BaseDataFetcherTest))
    suite.addTest(loader.loadTestsFromTestCase(YFinanceFetcherTest))
    suite.addTest(loader.loadTestsFromTestCase(DataManagerTest))
    suite.addTest(loader.loadTestsFromTestCase(DownloadDataTest))
    suite.addTest(loader.loadTestsFromTestCase(FetcherTest))
    suite.addTest(loader.loadTestsFromTestCase(DataCleanupTest))
    suite.addTest(loader.loadTestsFromTestCase(DataOptimizerTest))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result


if __name__ == "__main__":
    result = run_tests()
    sys.exit(0 if result.wasSuccessful() else 1)
