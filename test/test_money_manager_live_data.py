#!/usr/bin/env python3
"""
Test script to verify Money Manager integration with live DhanHQ data.

This script tests:
1. DhanBroker initialization with live data enabled
2. Money Manager connection to real API
3. Balance fetching from DhanHQ API
4. Comparison between live and mock data modes
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_dhan_broker_modes():
    """Test DhanBroker in different modes."""
    print("=" * 60)
    print("Testing DhanBroker Modes")
    print("=" * 60)
    
    try:
        from broker.dhan_wrapper import DhanBroker
        
        # Test 1: Dry run with mock data
        print("\n1. Testing Dry Run + Mock Data Mode:")
        broker_mock = DhanBroker(dry_run=True, live_data=False)
        print(f"   - Dry Run: {broker_mock.is_dry_run()}")
        print(f"   - Live Data: {broker_mock.is_live_data_enabled()}")
        
        # Test 2: Dry run with live data
        print("\n2. Testing Dry Run + Live Data Mode:")
        broker_live = DhanBroker(dry_run=True, live_data=True)
        print(f"   - Dry Run: {broker_live.is_dry_run()}")
        print(f"   - Live Data: {broker_live.is_live_data_enabled()}")
        
        # Test 3: Live trading mode
        print("\n3. Testing Live Trading Mode:")
        broker_full_live = DhanBroker(dry_run=False, live_data=True)
        print(f"   - Dry Run: {broker_full_live.is_dry_run()}")
        print(f"   - Live Data: {broker_full_live.is_live_data_enabled()}")
        
        return broker_live  # Return the broker we want to test with
        
    except Exception as e:
        print(f"Error testing DhanBroker modes: {e}")
        return None

def test_fund_limit_api(broker):
    """Test direct fund limit API call."""
    print("\n" + "=" * 60)
    print("Testing Direct Fund Limit API")
    print("=" * 60)
    
    try:
        print("Calling get_fund_limit()...")
        fund_info = broker.get_fund_limit()
        
        if isinstance(fund_info, dict):
            if "error" in fund_info:
                print(f"API Error: {fund_info['error']}")
                return False
            else:
                print("✓ Successfully fetched fund limit data:")
                for key, value in fund_info.items():
                    if isinstance(value, (int, float)):
                        print(f"   - {key}: ₹{value:,.2f}")
                    else:
                        print(f"   - {key}: {value}")
                return True
        else:
            print(f"Unexpected response type: {type(fund_info)}")
            return False
            
    except Exception as e:
        print(f"Error calling fund limit API: {e}")
        return False

def test_money_manager_integration(broker):
    """Test Money Manager integration with live data."""
    print("\n" + "=" * 60)
    print("Testing Money Manager Integration")
    print("=" * 60)
    
    try:
        from money_management import get_money_manager
        
        print("Initializing Money Manager with live data broker...")
        money_manager = get_money_manager(broker=broker, force_new=True)
        
        print("\nFetching balance through Money Manager...")
        balance_info = money_manager.get_available_balance(force_refresh=True)
        
        if "error" in balance_info:
            print(f"Error: {balance_info['error']}")
            return False
        
        print("✓ Successfully fetched balance through Money Manager:")
        for key, value in balance_info.items():
            print(f"   - {key.replace('_', ' ').title()}: ₹{value:,.2f}")
        
        # Test allocation summary
        print("\nFetching allocation summary...")
        summary = money_manager.get_allocation_summary()
        
        if "error" in summary:
            print(f"Error: {summary['error']}")
            return False
        
        print("✓ Successfully fetched allocation summary:")
        print(f"   - Total Balance: ₹{summary['balance_info']['total_balance']:,.2f}")
        print(f"   - Available Balance: ₹{summary['balance_info']['available_balance']:,.2f}")
        print(f"   - Emergency Reserve: ₹{summary['emergency_reserve']:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"Error testing Money Manager integration: {e}")
        return False

def test_comparison_modes():
    """Compare mock vs live data modes."""
    print("\n" + "=" * 60)
    print("Comparing Mock vs Live Data Modes")
    print("=" * 60)
    
    try:
        from broker.dhan_wrapper import DhanBroker
        from money_management import get_money_manager
        
        # Test with mock data
        print("1. Testing with Mock Data:")
        broker_mock = DhanBroker(dry_run=True, live_data=False)
        mm_mock = get_money_manager(broker=broker_mock, force_new=True)
        balance_mock = mm_mock.get_available_balance(force_refresh=True)
        print(f"   Mock Available Balance: ₹{balance_mock['available_balance']:,.2f}")
        
        # Test with live data
        print("\n2. Testing with Live Data:")
        broker_live = DhanBroker(dry_run=True, live_data=True)
        mm_live = get_money_manager(broker=broker_live, force_new=True)
        balance_live = mm_live.get_available_balance(force_refresh=True)
        print(f"   Live Available Balance: ₹{balance_live['available_balance']:,.2f}")
        
        # Compare
        print("\n3. Comparison:")
        if balance_mock['available_balance'] != balance_live['available_balance']:
            print("✓ Live data is different from mock data - API integration working!")
            return True
        else:
            print("⚠ Live and mock data are the same - check API connection")
            return False
            
    except Exception as e:
        print(f"Error comparing modes: {e}")
        return False

def main():
    """Main test function."""
    print("RapidTrader Money Manager Live Data Integration Test")
    print("=" * 60)
    
    # Check environment variables
    client_id = os.getenv("DHAN_CLIENT_ID")
    access_token = os.getenv("DHAN_ACCESS_TOKEN")
    
    if not client_id or not access_token:
        print("❌ DhanHQ credentials not found in environment variables")
        print("Please ensure DHAN_CLIENT_ID and DHAN_ACCESS_TOKEN are set in .env file")
        return False
    
    print(f"✓ DhanHQ Client ID: {client_id}")
    print(f"✓ DhanHQ Access Token: {'*' * 20}...{access_token[-10:]}")
    
    # Run tests
    success = True
    
    # Test 1: DhanBroker modes
    broker = test_dhan_broker_modes()
    if not broker:
        success = False
    
    # Test 2: Direct API call
    if broker and not test_fund_limit_api(broker):
        success = False
    
    # Test 3: Money Manager integration
    if broker and not test_money_manager_integration(broker):
        success = False
    
    # Test 4: Comparison
    if not test_comparison_modes():
        success = False
    
    # Final result
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! Money Manager is connected to live DhanHQ data.")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
