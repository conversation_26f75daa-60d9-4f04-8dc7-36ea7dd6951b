#!/usr/bin/env python3
"""
Comprehensive API Gateway Test Suite

Tests the API Gateway functionality including:
- Authentication system
- Broker interface
- Container management
- WebSocket connections
- Rate limiting
- Security features
"""

import os
import sys
import json
import tempfile
import shutil
import unittest
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from pathlib import Path
import asyncio

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestAPIGatewayCore(unittest.TestCase):
    """Test core API Gateway functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_api_gateway_initialization(self):
        """Test API Gateway initialization"""
        try:
            from api_gateway.main import APIGateway
            
            # Mock dependencies to avoid actual connections
            with patch('docker.from_env'), \
                 patch('api_gateway.auth.auth_manager'), \
                 patch('api_gateway.broker_interface.broker_manager'):
                
                gateway = APIGateway()
                
                # Verify basic initialization
                self.assertIsNotNone(gateway.app)
                self.assertEqual(gateway.app.title, "RapidTrader API Gateway")
                self.assertEqual(gateway.app.version, "2.0.0")
                
        except ImportError as e:
            self.skipTest(f"API Gateway not available: {e}")
    
    def test_authentication_manager(self):
        """Test authentication manager functionality"""
        try:
            from api_gateway.auth import AuthManager
            
            # Create test auth manager
            auth_manager = AuthManager()
            
            # Test API key generation
            key_id, api_key = auth_manager.generate_api_key(
                name="test_key",
                description="Test API key",
                permissions=["read", "write"]
            )
            
            # Verify key generation
            self.assertIsNotNone(key_id)
            self.assertIsNotNone(api_key)
            self.assertIsInstance(key_id, str)
            self.assertIsInstance(api_key, str)
            
            # Test key validation
            key_info = auth_manager.validate_api_key(api_key)
            self.assertIsNotNone(key_info)
            self.assertEqual(key_info['name'], "test_key")
            
        except ImportError as e:
            self.skipTest(f"Authentication manager not available: {e}")
    
    def test_broker_interface_manager(self):
        """Test broker interface manager"""
        try:
            from api_gateway.broker_interface import BrokerManager
            
            broker_manager = BrokerManager()
            
            # Test broker registration
            test_broker_config = {
                "broker_name": "fyers",
                "credentials": {
                    "client_id": "test_client",
                    "secret_key": "test_secret"
                },
                "dry_run": True
            }
            
            success = broker_manager.add_broker("test_fyers", test_broker_config)
            self.assertTrue(success)
            
            # Test broker listing
            brokers = broker_manager.list_brokers()
            self.assertIn("test_fyers", brokers)
            
            # Test broker removal
            success = broker_manager.remove_broker("test_fyers")
            self.assertTrue(success)
            
        except ImportError as e:
            self.skipTest(f"Broker interface manager not available: {e}")
    
    def test_rate_limiter(self):
        """Test rate limiting functionality"""
        try:
            from api_gateway.auth import RateLimiter
            
            rate_limiter = RateLimiter()
            
            # Test rate limiting
            user_id = "test_user"
            endpoint = "placeorder"
            
            # Should be allowed initially
            self.assertTrue(rate_limiter.is_allowed(user_id, endpoint))
            
            # Test rate limit enforcement
            # (This would need actual rate limiting logic)
            
        except ImportError as e:
            self.skipTest(f"Rate limiter not available: {e}")


class TestAPIGatewayEndpoints(unittest.TestCase):
    """Test API Gateway endpoints"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    @patch('docker.from_env')
    @patch('api_gateway.auth.auth_manager')
    @patch('api_gateway.broker_interface.broker_manager')
    def test_health_endpoint(self, mock_broker_manager, mock_auth_manager, mock_docker):
        """Test health check endpoint"""
        try:
            from api_gateway.main import APIGateway
            from fastapi.testclient import TestClient
            
            # Mock dependencies
            mock_broker_manager.list_brokers.return_value = ["fyers", "dhan"]
            
            gateway = APIGateway()
            client = TestClient(gateway.app)
            
            # Test health endpoint
            response = client.get("/health")
            
            self.assertEqual(response.status_code, 200)
            data = response.json()
            
            self.assertIn("status", data)
            self.assertEqual(data["status"], "healthy")
            self.assertIn("timestamp", data)
            
        except ImportError as e:
            self.skipTest(f"FastAPI test client not available: {e}")
    
    @patch('docker.from_env')
    @patch('api_gateway.auth.auth_manager')
    @patch('api_gateway.broker_interface.broker_manager')
    def test_root_endpoint(self, mock_broker_manager, mock_auth_manager, mock_docker):
        """Test root endpoint"""
        try:
            from api_gateway.main import APIGateway
            from fastapi.testclient import TestClient
            
            gateway = APIGateway()
            client = TestClient(gateway.app)
            
            # Test root endpoint
            response = client.get("/")
            
            self.assertEqual(response.status_code, 200)
            data = response.json()
            
            self.assertIn("message", data)
            self.assertIn("RapidTrader API Gateway", data["message"])
            self.assertIn("version", data)
            self.assertEqual(data["version"], "2.0.0")
            
        except ImportError as e:
            self.skipTest(f"FastAPI test client not available: {e}")


class TestContainerManagement(unittest.TestCase):
    """Test container management functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    @patch('docker.from_env')
    def test_container_listing(self, mock_docker):
        """Test container listing functionality"""
        try:
            from api_gateway.main import APIGateway
            
            # Mock Docker client
            mock_container = Mock()
            mock_container.id = "test_container_id"
            mock_container.name = "rapidtrader-test"
            mock_container.status = "running"
            mock_container.image.tags = ["rapidtrader:alpine"]
            mock_container.attrs = {"Created": "2024-01-01T00:00:00Z"}
            mock_container.labels = {"rapidtrader.managed": "true"}
            
            mock_docker_client = Mock()
            mock_docker_client.containers.list.return_value = [mock_container]
            mock_docker.return_value = mock_docker_client
            
            with patch('api_gateway.auth.auth_manager'), \
                 patch('api_gateway.broker_interface.broker_manager'):
                
                gateway = APIGateway()
                
                # Test container listing (would be called via API)
                containers = mock_docker_client.containers.list(
                    filters={"label": "rapidtrader.managed=true"}
                )
                
                self.assertEqual(len(containers), 1)
                self.assertEqual(containers[0].name, "rapidtrader-test")
                
        except ImportError as e:
            self.skipTest(f"Container management not available: {e}")
    
    def test_container_request_validation(self):
        """Test container request validation"""
        try:
            from api_gateway.main import ContainerRequest
            
            # Test valid container request
            valid_request = ContainerRequest(
                action="start",
                container_type="backtest",
                strategy="TestStrategy",
                symbols=["RELIANCE", "TCS"]
            )
            
            self.assertEqual(valid_request.action, "start")
            self.assertEqual(valid_request.container_type, "backtest")
            self.assertEqual(valid_request.strategy, "TestStrategy")
            self.assertEqual(valid_request.symbols, ["RELIANCE", "TCS"])
            
        except ImportError as e:
            self.skipTest(f"Container request models not available: {e}")


class TestWebSocketFunctionality(unittest.TestCase):
    """Test WebSocket functionality"""
    
    def test_websocket_connection_management(self):
        """Test WebSocket connection management"""
        try:
            from api_gateway.main import APIGateway
            
            with patch('docker.from_env'), \
                 patch('api_gateway.auth.auth_manager'), \
                 patch('api_gateway.broker_interface.broker_manager'):
                
                gateway = APIGateway()
                
                # Test WebSocket connection list initialization
                self.assertEqual(len(gateway.websocket_connections), 0)
                
                # Mock WebSocket connection
                mock_websocket = Mock()
                gateway.websocket_connections.append(mock_websocket)
                
                self.assertEqual(len(gateway.websocket_connections), 1)
                
        except ImportError as e:
            self.skipTest(f"WebSocket functionality not available: {e}")


class TestSecurityFeatures(unittest.TestCase):
    """Test security features"""
    
    def test_api_key_security(self):
        """Test API key security features"""
        try:
            from api_gateway.auth import AuthManager
            import secrets
            
            auth_manager = AuthManager()
            
            # Test secure key generation
            key_id, api_key = auth_manager.generate_api_key(
                name="security_test",
                permissions=["read"]
            )
            
            # Verify key properties
            self.assertGreaterEqual(len(api_key), 32)  # Minimum length
            self.assertIsInstance(api_key, str)
            
            # Test key validation
            key_info = auth_manager.validate_api_key(api_key)
            self.assertIsNotNone(key_info)
            
            # Test invalid key rejection
            invalid_key_info = auth_manager.validate_api_key("invalid_key")
            self.assertIsNone(invalid_key_info)
            
        except ImportError as e:
            self.skipTest(f"Security features not available: {e}")
    
    def test_permission_system(self):
        """Test permission system"""
        try:
            from api_gateway.auth import AuthManager
            
            auth_manager = AuthManager()
            
            # Create key with limited permissions
            key_id, api_key = auth_manager.generate_api_key(
                name="limited_key",
                permissions=["read"]
            )
            
            key_info = auth_manager.validate_api_key(api_key)
            
            # Test permission checking
            self.assertIn("read", key_info.get("permissions", []))
            self.assertNotIn("write", key_info.get("permissions", []))
            
        except ImportError as e:
            self.skipTest(f"Permission system not available: {e}")


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestAPIGatewayCore,
        TestAPIGatewayEndpoints,
        TestContainerManagement,
        TestWebSocketFunctionality,
        TestSecurityFeatures
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Exit with appropriate code
    sys.exit(0 if result.wasSuccessful() else 1)
