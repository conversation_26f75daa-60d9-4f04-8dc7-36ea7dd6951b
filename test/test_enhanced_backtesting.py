#!/usr/bin/env python3
"""
Enhanced Backtesting Test Suite

Tests the enhanced backtesting functionality including:
- Docker container deployment
- Automatic data management
- Strategy execution
- Results collection
- Performance metrics
"""

import os
import sys
import json
import tempfile
import shutil
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestEnhancedBacktesting(unittest.TestCase):
    """Test enhanced backtesting functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test directory structure
        self.userdata_dir = os.path.join(self.test_dir, "userdata")
        self.configs_dir = os.path.join(self.userdata_dir, "configs")
        self.strategies_dir = os.path.join(self.userdata_dir, "strategies")
        self.results_dir = os.path.join(self.userdata_dir, "results")
        self.data_dir = os.path.join(self.userdata_dir, "historical_data")
        
        for directory in [self.userdata_dir, self.configs_dir, self.strategies_dir, 
                         self.results_dir, self.data_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # Create test configuration
        self.test_config = {
            "strategy": {
                "name": "TestStrategy",
                "params": {
                    "sma_period": 20,
                    "rsi_period": 14
                }
            },
            "backtest": {
                "timeframe": "1d",
                "start_date": "2024-01-01",
                "end_date": "2024-06-30",
                "initial_balance": 100000,
                "commission": 0.001
            },
            "symbols": ["RELIANCE", "TCS", "INFY"],
            "dry_run": True
        }
        
        config_file = os.path.join(self.configs_dir, "test-backtest-config.json")
        with open(config_file, 'w') as f:
            json.dump(self.test_config, f, indent=4)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def create_test_strategy(self):
        """Create a test strategy for backtesting"""
        strategy_content = '''
import pandas as pd
import numpy as np
from userdata.strategies.base_strategy import BaseStrategy

class TestStrategy(BaseStrategy):
    """Simple test strategy for backtesting"""
    
    def __init__(self, config=None):
        super().__init__(config)
        self.name = "TestStrategy"
        
        # Get parameters from config
        strategy_params = self.config.get("strategy", {}).get("params", {})
        self.sma_period = strategy_params.get("sma_period", 20)
        self.rsi_period = strategy_params.get("rsi_period", 14)
    
    def populate_indicators(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Add indicators to dataframe"""
        # Simple Moving Average
        dataframe[f'sma_{self.sma_period}'] = dataframe['close'].rolling(self.sma_period).mean()
        
        # RSI calculation
        delta = dataframe['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
        rs = gain / loss
        dataframe[f'rsi_{self.rsi_period}'] = 100 - (100 / (1 + rs))
        
        return dataframe
    
    def populate_buy_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Generate buy signals"""
        dataframe['buy'] = (
            (dataframe['close'] > dataframe[f'sma_{self.sma_period}']) &
            (dataframe[f'rsi_{self.rsi_period}'] < 70) &
            (dataframe[f'rsi_{self.rsi_period}'] > 30)
        )
        return dataframe
    
    def populate_sell_signals(self, dataframe: pd.DataFrame) -> pd.DataFrame:
        """Generate sell signals"""
        dataframe['sell'] = (
            (dataframe['close'] < dataframe[f'sma_{self.sma_period}']) |
            (dataframe[f'rsi_{self.rsi_period}'] > 80)
        )
        return dataframe
'''
        
        strategy_file = os.path.join(self.strategies_dir, "TestStrategy.py")
        with open(strategy_file, 'w') as f:
            f.write(strategy_content)
        
        return strategy_file
    
    def create_test_data(self, symbol: str, days: int = 252) -> pd.DataFrame:
        """Create synthetic test data for backtesting"""
        dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
        
        # Generate synthetic price data with some trend and volatility
        np.random.seed(42)  # For reproducible results
        
        # Start with base price
        base_price = 100.0
        returns = np.random.normal(0.001, 0.02, days)  # Daily returns with slight upward bias
        
        # Generate price series
        prices = [base_price]
        for i in range(1, days):
            prices.append(prices[-1] * (1 + returns[i]))
        
        # Create OHLCV data
        data = pd.DataFrame({
            'date': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(100000, 1000000, days)
        })
        
        # Ensure high >= close >= low and high >= open >= low
        data['high'] = data[['open', 'close', 'high']].max(axis=1)
        data['low'] = data[['open', 'close', 'low']].min(axis=1)
        
        return data
    
    def test_backtest_engine_initialization(self):
        """Test backtest engine can be initialized"""
        try:
            from core.backtest_engine import BacktestEngine
            
            engine = BacktestEngine(
                starting_balance=100000,
                commission=0.001,
                slippage=0.0001
            )
            
            self.assertIsNotNone(engine)
            self.assertEqual(engine.starting_balance, 100000)
            self.assertEqual(engine.commission, 0.001)
            
        except ImportError as e:
            self.skipTest(f"Backtest engine not available: {e}")
    
    def test_strategy_analysis(self):
        """Test strategy analysis on test data"""
        try:
            # Create test strategy
            strategy_file = self.create_test_strategy()
            
            # Create test data
            test_data = self.create_test_data("RELIANCE")
            
            # This would normally be done by importing the strategy
            # For testing, we'll simulate the analysis
            
            # Add simple indicators
            test_data['sma_20'] = test_data['close'].rolling(20).mean()
            
            # Add buy/sell signals
            test_data['buy'] = test_data['close'] > test_data['sma_20']
            test_data['sell'] = test_data['close'] < test_data['sma_20']
            
            # Verify signals were generated
            self.assertTrue('buy' in test_data.columns)
            self.assertTrue('sell' in test_data.columns)
            self.assertTrue(test_data['buy'].any())
            self.assertTrue(test_data['sell'].any())
            
        except Exception as e:
            self.skipTest(f"Strategy analysis test failed: {e}")
    
    def test_trade_simulation(self):
        """Test trade simulation functionality"""
        try:
            from core.trade_simulator import TradeSimulator
            
            simulator = TradeSimulator(
                initial_balance=100000,
                commission=0.001,
                slippage=0.0001
            )
            
            # Create test data with signals
            test_data = self.create_test_data("RELIANCE")
            test_data['sma_20'] = test_data['close'].rolling(20).mean()
            test_data['buy'] = test_data['close'] > test_data['sma_20']
            test_data['sell'] = test_data['close'] < test_data['sma_20']
            
            # Simulate some trades
            for i, row in test_data.iterrows():
                if i < 20:  # Skip first 20 rows (SMA calculation)
                    continue
                
                if row['buy'] and not simulator.has_position("RELIANCE"):
                    simulator.enter_position(
                        symbol="RELIANCE",
                        price=row['close'],
                        quantity=100,
                        timestamp=row['date']
                    )
                elif row['sell'] and simulator.has_position("RELIANCE"):
                    simulator.exit_position(
                        symbol="RELIANCE",
                        price=row['close'],
                        timestamp=row['date']
                    )
            
            # Verify trades were executed
            trades = simulator.get_trades()
            self.assertGreater(len(trades), 0)
            
        except ImportError as e:
            self.skipTest(f"Trade simulator not available: {e}")
    
    def test_backtest_results_calculation(self):
        """Test backtest results calculation"""
        try:
            from core.backtest_results import BacktestResults
            
            results = BacktestResults()
            
            # Add some test trades
            test_trades = [
                {
                    'symbol': 'RELIANCE',
                    'entry_price': 100.0,
                    'exit_price': 105.0,
                    'quantity': 100,
                    'profit_loss': 500.0,
                    'entry_time': '2024-01-01',
                    'exit_time': '2024-01-05'
                },
                {
                    'symbol': 'TCS',
                    'entry_price': 200.0,
                    'exit_price': 195.0,
                    'quantity': 50,
                    'profit_loss': -250.0,
                    'entry_time': '2024-01-10',
                    'exit_time': '2024-01-15'
                }
            ]
            
            for trade in test_trades:
                results.add_trade(trade)
            
            # Calculate metrics
            metrics = results.calculate_metrics()
            
            # Verify basic metrics
            self.assertEqual(metrics['total_trades'], 2)
            self.assertEqual(metrics['winning_trades'], 1)
            self.assertEqual(metrics['losing_trades'], 1)
            self.assertEqual(metrics['total_profit_loss'], 250.0)
            self.assertEqual(metrics['win_rate'], 50.0)
            
        except ImportError as e:
            self.skipTest(f"Backtest results not available: {e}")
    
    @patch('subprocess.run')
    def test_enhanced_backtest_docker_deployment(self, mock_subprocess):
        """Test enhanced backtest Docker deployment"""
        # Mock successful subprocess execution
        mock_subprocess.return_value.returncode = 0
        
        try:
            # This would normally trigger Docker deployment
            # For testing, we'll simulate the command execution
            
            container_name = "backtest-reliance-tcs-infy-test-config-teststrategy-20240101-120000"
            
            # Verify container name generation
            self.assertIn("backtest", container_name)
            self.assertIn("test-config", container_name)
            self.assertIn("teststrategy", container_name)
            
            # Verify subprocess was called (mocked)
            # In real implementation, this would call docker-compose
            
        except Exception as e:
            self.skipTest(f"Enhanced backtest deployment test failed: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
