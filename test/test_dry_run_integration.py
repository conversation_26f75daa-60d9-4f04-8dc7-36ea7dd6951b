#!/usr/bin/env python3
"""
Test script to verify Dry Run integration with DhanHQ API v2.

This script tests:
1. DhanBroker dry run mode with live data
2. Order simulation using DhanHQ API structure
3. Live market data fetching during dry run
4. Balance and position simulation
5. Complete dry run workflow
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_dry_run_broker_initialization():
    """Test DhanBroker initialization in dry run mode."""
    print("=" * 60)
    print("Testing DhanBroker Dry Run Initialization")
    print("=" * 60)
    
    try:
        from broker.dhan_wrapper import DhanBroker
        
        # Test dry run with live data (recommended for realistic simulation)
        print("\n1. Testing Dry Run + Live Data Mode:")
        broker = DhanBroker(dry_run=True, live_data=True)
        print(f"   - Dry Run: {broker.is_dry_run()}")
        print(f"   - Live Data: {broker.is_live_data_enabled()}")
        print(f"   - Client ID: {broker.get_client_id()}")
        
        return broker
        
    except Exception as e:
        print(f"Error initializing DhanBroker: {e}")
        return None

def test_live_market_data(broker):
    """Test live market data fetching in dry run mode."""
    print("\n" + "=" * 60)
    print("Testing Live Market Data in Dry Run")
    print("=" * 60)
    
    test_symbols = ["RELIANCE", "TCS", "HDFCBANK"]
    
    for symbol in test_symbols:
        try:
            print(f"\nTesting {symbol}:")
            
            # Test live quote
            quote = broker.get_quote(symbol, "NSE_EQ")
            
            if "error" in quote:
                print(f"   Quote Error: {quote['error']}")
            else:
                print(f"   ✓ Live Quote: LTP=₹{quote.get('ltp', 0):.2f}")
                print(f"     Open=₹{quote.get('open', 0):.2f}, High=₹{quote.get('high', 0):.2f}")
                print(f"     Low=₹{quote.get('low', 0):.2f}, Volume={quote.get('volume', 0):,}")
            
        except Exception as e:
            print(f"   Error getting quote for {symbol}: {e}")
    
    return True

def test_order_simulation(broker):
    """Test order placement simulation in dry run mode."""
    print("\n" + "=" * 60)
    print("Testing Order Simulation in Dry Run")
    print("=" * 60)
    
    try:
        # Test 1: Place a limit order
        print("\n1. Testing Limit Order Placement:")
        order_response = broker.place_order_by_symbol(
            symbol="RELIANCE",
            quantity=10,
            transaction_type="BUY",
            price=2500.0,
            product_type="CNC",
            order_type="LIMIT"
        )
        
        if "error" in order_response:
            print(f"   Order Error: {order_response['error']}")
        else:
            print(f"   ✓ Order Placed: ID={order_response.get('orderId')}")
            print(f"     Status: {order_response.get('orderStatus')}")
        
        # Test 2: Place a market order
        print("\n2. Testing Market Order Placement:")
        market_order = broker.place_order_by_symbol(
            symbol="TCS",
            quantity=5,
            transaction_type="BUY",
            product_type="CNC",
            order_type="MARKET"
        )
        
        if "error" in market_order:
            print(f"   Order Error: {market_order['error']}")
        else:
            print(f"   ✓ Market Order Placed: ID={market_order.get('orderId')}")
            print(f"     Status: {market_order.get('orderStatus')}")
        
        # Test 3: Get all orders
        print("\n3. Testing Order Retrieval:")
        orders = broker.get_orders()
        print(f"   ✓ Total Orders: {len(orders)}")
        
        for order in orders[-2:]:  # Show last 2 orders
            print(f"     Order {order.get('orderId')}: {order.get('orderStatus')} - "
                  f"{order.get('quantity')} x {order.get('tradingSymbol')} @ ₹{order.get('price', 0)}")
        
        return True
        
    except Exception as e:
        print(f"Error testing order simulation: {e}")
        return False

def test_balance_and_positions(broker):
    """Test balance and position simulation."""
    print("\n" + "=" * 60)
    print("Testing Balance and Position Simulation")
    print("=" * 60)
    
    try:
        # Test fund limit (should use live data if available)
        print("\n1. Testing Fund Limit:")
        fund_info = broker.get_fund_limit()
        
        if "error" in fund_info:
            print(f"   Fund Error: {fund_info['error']}")
        else:
            print(f"   ✓ Available Balance: ₹{fund_info.get('availableBalance', 0):,.2f}")
            print(f"     Total Balance: ₹{fund_info.get('totalBalance', 0):,.2f}")
            print(f"     Used Amount: ₹{fund_info.get('utilizedAmount', 0):,.2f}")
        
        # Test holdings (simulated in dry run)
        print("\n2. Testing Holdings:")
        holdings = broker.get_holdings()
        print(f"   ✓ Holdings Count: {len(holdings)}")
        
        for holding in holdings[:3]:  # Show first 3 holdings
            print(f"     {holding.get('tradingSymbol')}: {holding.get('totalQty')} shares @ ₹{holding.get('avgCostPrice', 0):.2f}")
        
        # Test positions (simulated in dry run)
        print("\n3. Testing Positions:")
        positions = broker.get_positions()
        print(f"   ✓ Positions Count: {len(positions)}")
        
        return True
        
    except Exception as e:
        print(f"Error testing balance and positions: {e}")
        return False

def test_dry_run_summary(broker):
    """Test dry run summary and statistics."""
    print("\n" + "=" * 60)
    print("Testing Dry Run Summary")
    print("=" * 60)
    
    try:
        summary = broker.get_dry_run_summary()
        
        if "error" in summary:
            print(f"   Summary Error: {summary['error']}")
        else:
            print(f"   ✓ Total Orders: {summary.get('total_orders', 0)}")
            print(f"     Pending Orders: {summary.get('pending_orders', 0)}")
            print(f"     Cancelled Orders: {summary.get('cancelled_orders', 0)}")
            print(f"     Total Trades: {summary.get('total_trades', 0)}")
            print(f"     Next Order ID: {summary.get('next_order_id', 1)}")
        
        return True
        
    except Exception as e:
        print(f"Error getting dry run summary: {e}")
        return False

def test_margin_calculation(broker):
    """Test margin calculation in dry run mode."""
    print("\n" + "=" * 60)
    print("Testing Margin Calculation")
    print("=" * 60)
    
    try:
        # Get security ID for RELIANCE
        security_id = broker.get_security_id("RELIANCE", "NSE_EQ")
        
        if security_id:
            print(f"\n1. Testing Margin Calculation for RELIANCE (ID: {security_id}):")
            
            margin_info = broker.calculate_margin(
                security_id=security_id,
                exchange_segment="NSE_EQ",
                transaction_type="BUY",
                quantity=10,
                product_type="CNC",
                price=2500.0
            )
            
            if "error" in margin_info:
                print(f"   Margin Error: {margin_info['error']}")
            else:
                print(f"   ✓ Total Margin: ₹{margin_info.get('totalMargin', 0):,.2f}")
                print(f"     Span Margin: ₹{margin_info.get('spanMargin', 0):,.2f}")
                print(f"     Exposure Margin: ₹{margin_info.get('exposureMargin', 0):,.2f}")
                print(f"     Leverage: {margin_info.get('leverage', 1):.2f}x")
        else:
            print("   Security ID not found for RELIANCE")
        
        return True
        
    except Exception as e:
        print(f"Error testing margin calculation: {e}")
        return False

def main():
    """Main test function."""
    print("RapidTrader Dry Run Integration Test with DhanHQ API v2")
    print("=" * 60)
    
    # Check environment variables
    client_id = os.getenv("DHAN_CLIENT_ID")
    access_token = os.getenv("DHAN_ACCESS_TOKEN")
    
    if not client_id or not access_token:
        print("❌ DhanHQ credentials not found in environment variables")
        print("Please ensure DHAN_CLIENT_ID and DHAN_ACCESS_TOKEN are set in .env file")
        return False
    
    print(f"✓ DhanHQ Client ID: {client_id}")
    print(f"✓ DhanHQ Access Token: {'*' * 20}...{access_token[-10:]}")
    
    # Run tests
    success = True
    
    # Test 1: Broker initialization
    broker = test_dry_run_broker_initialization()
    if not broker:
        success = False
        return success
    
    # Test 2: Live market data
    if not test_live_market_data(broker):
        success = False
    
    # Test 3: Order simulation
    if not test_order_simulation(broker):
        success = False
    
    # Test 4: Balance and positions
    if not test_balance_and_positions(broker):
        success = False
    
    # Test 5: Margin calculation
    if not test_margin_calculation(broker):
        success = False
    
    # Test 6: Dry run summary
    if not test_dry_run_summary(broker):
        success = False
    
    # Final result
    print("\n" + "=" * 60)
    if success:
        print("🎉 All dry run tests passed! DhanHQ API v2 integration working perfectly.")
        print("\n✅ Key Features Verified:")
        print("   • Live market data fetching during dry run")
        print("   • Order simulation using DhanHQ API structure")
        print("   • Balance and position data (live + simulated)")
        print("   • Margin calculations")
        print("   • Complete dry run workflow")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
