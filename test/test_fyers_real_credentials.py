#!/usr/bin/env python3
"""
Test script for Fyers broker with real credentials.

This script tests the Fyers broker functionality using real API credentials
from the .env file. It demonstrates basic broker operations and WebSocket streaming.
"""

import os
import sys
import time
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from broker import FyersBroker
    print("✓ Fyers broker imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)


def test_broker_initialization():
    """Test broker initialization with real credentials."""
    print("\n=== Testing Broker Initialization ===")

    try:
        # Initialize broker (will read credentials from .env)
        broker = FyersBroker()

        if broker.client_id and broker.access_token:
            print("✓ Broker initialized with real credentials")
            print(f"  Client ID: {broker.client_id}")
            print(f"  Has access token: {bool(broker.access_token)}")
            return broker
        else:
            print("✗ No credentials found in .env file")
            return None

    except Exception as e:
        print(f"✗ Error initializing broker: {e}")
        return None


def test_profile_access(broker):
    """Test getting user profile."""
    print("\n=== Testing Profile Access ===")

    try:
        profile = broker.get_profile()

        if profile.get('s') == 'ok':
            data = profile.get('data', {})
            print("✓ Profile retrieved successfully")
            print(f"  User ID: {data.get('fy_id', 'N/A')}")
            print(f"  Name: {data.get('name', 'N/A')}")
            print(f"  Email: {data.get('email_id', 'N/A')}")
            print(f"  PAN: {data.get('PAN', 'N/A')}")
            return True
        else:
            print("✗ Failed to get profile")
            print(f"  Error: {profile.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"✗ Error getting profile: {e}")
        return False


def test_funds_access(broker):
    """Test getting fund information."""
    print("\n=== Testing Funds Access ===")

    try:
        funds = broker.get_funds()

        if funds.get('s') == 'ok':
            # Handle both possible response formats
            data = funds.get('fund_limit', funds.get('data', {}))
            if isinstance(data, list) and data:
                data = data[0]  # Take first item if it's a list

            print("✓ Funds retrieved successfully")
            print(f"  Available Cash: ₹{data.get('availablecash', data.get('available_cash', 'N/A'))}")
            print(f"  Utilized Margin: ₹{data.get('utilizedmargin', data.get('utilized_margin', 'N/A'))}")
            print(f"  Available Margin: ₹{data.get('availablemargin', data.get('available_margin', 'N/A'))}")
            return True
        else:
            print("✗ Failed to get funds")
            print(f"  Error: {funds.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"✗ Error getting funds: {e}")
        return False


def test_positions_access(broker):
    """Test getting positions."""
    print("\n=== Testing Positions Access ===")

    try:
        positions = broker.get_positions()

        if positions.get('s') == 'ok':
            data = positions.get('netPositions', [])
            print("✓ Positions retrieved successfully")
            print(f"  Number of positions: {len(data)}")

            if data:
                for i, pos in enumerate(data[:3]):  # Show first 3 positions
                    print(f"  Position {i+1}: {pos.get('symbol', 'N/A')} - "
                          f"Qty: {pos.get('netQty', 'N/A')}, "
                          f"P&L: ₹{pos.get('realized_profit', 'N/A')}")
            else:
                print("  No open positions")
            return True
        else:
            print("✗ Failed to get positions")
            print(f"  Error: {positions.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"✗ Error getting positions: {e}")
        return False


def test_websocket_connection(broker):
    """Test WebSocket connection."""
    print("\n=== Testing WebSocket Connection ===")

    try:
        # Enable WebSocket
        success = broker.enable_websocket()

        if success:
            print("✓ WebSocket connection successful")
            print(f"  Connected: {broker.is_websocket_connected()}")
            return True
        else:
            print("✗ WebSocket connection failed")
            return False

    except Exception as e:
        print(f"✗ WebSocket connection error: {e}")
        return False


def test_live_data_subscription(broker):
    """Test live data subscription."""
    print("\n=== Testing Live Data Subscription ===")

    if not broker.is_websocket_connected():
        print("⚠️  WebSocket not connected, skipping subscription test")
        return False

    try:
        # Test symbols
        symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ"]

        # Subscribe to live quotes
        quote_success = broker.subscribe_live_quotes(symbols)
        print(f"Quote subscription: {'✓ Success' if quote_success else '✗ Failed'}")

        # Subscribe to market depth
        depth_success = broker.subscribe_market_depth(symbols[:1])
        print(f"Depth subscription: {'✓ Success' if depth_success else '✗ Failed'}")

        # Check subscribed symbols
        subscribed = broker.get_subscribed_symbols()
        print(f"Subscribed symbols: {subscribed}")

        return quote_success or depth_success

    except Exception as e:
        print(f"✗ Error in subscription: {e}")
        return False


def test_live_data_streaming(broker):
    """Test live data streaming for a short period."""
    print("\n=== Testing Live Data Streaming ===")

    if not broker.is_websocket_connected():
        print("⚠️  WebSocket not connected, skipping streaming test")
        return False

    try:
        quote_count = 0
        depth_count = 0

        def on_quote_update(symbol, data):
            nonlocal quote_count
            quote_count += 1
            ltp = data.get('ltp', 'N/A')
            change = data.get('ch', 'N/A')
            print(f"📈 Quote: {symbol} = ₹{ltp} (Change: {change})")

        def on_depth_update(symbol, data):
            nonlocal depth_count
            depth_count += 1
            bid = data.get('bid', [{}])[0].get('price', 'N/A') if data.get('bid') else 'N/A'
            ask = data.get('ask', [{}])[0].get('price', 'N/A') if data.get('ask') else 'N/A'
            print(f"📊 Depth: {symbol} - Bid: ₹{bid}, Ask: ₹{ask}")

        # Add callbacks
        broker.add_quote_callback(on_quote_update)
        broker.add_depth_callback(on_depth_update)

        print("Monitoring live data for 15 seconds...")
        start_time = time.time()

        while time.time() - start_time < 15:
            time.sleep(1)
            elapsed = int(time.time() - start_time)
            if elapsed % 5 == 0:
                print(f"  [{elapsed}s] Quotes: {quote_count}, Depths: {depth_count}")

        print(f"\nStreaming test completed:")
        print(f"  Total quotes received: {quote_count}")
        print(f"  Total depths received: {depth_count}")

        return quote_count > 0 or depth_count > 0

    except Exception as e:
        print(f"✗ Error in streaming test: {e}")
        return False


def test_dry_run_order(broker):
    """Test placing a dry run order."""
    print("\n=== Testing Dry Run Order ===")

    try:
        # Place a dry run market order
        order_response = broker.place_order(
            symbol="NSE:SBIN-EQ",
            qty=1,
            side=1,  # BUY
            type=2,  # MARKET
            productType="CNC"
        )

        if order_response.get('s') == 'ok' or 'DRY' in str(order_response.get('id', '')):
            print("✓ Dry run order placed successfully")
            print(f"  Order ID: {order_response.get('id', 'N/A')}")
            print(f"  Status: {order_response.get('status', 'N/A')}")
            print(f"  Message: {order_response.get('message', 'N/A')}")
            return True
        else:
            print("✗ Failed to place dry run order")
            print(f"  Error: {order_response.get('message', 'Unknown error')}")
            return False

    except Exception as e:
        print(f"✗ Error placing dry run order: {e}")
        return False


def main():
    """Main test function."""
    print("Fyers Real Credentials Test Suite")
    print("=" * 50)
    print(f"Test started at: {datetime.now()}")

    # Initialize broker
    broker = test_broker_initialization()
    if not broker:
        print("\n✗ Cannot proceed without valid broker initialization")
        return

    # Test basic API access
    profile_ok = test_profile_access(broker)
    funds_ok = test_funds_access(broker)
    positions_ok = test_positions_access(broker)

    # Test WebSocket functionality
    ws_connected = test_websocket_connection(broker)

    if ws_connected:
        subscription_ok = test_live_data_subscription(broker)
        if subscription_ok:
            streaming_ok = test_live_data_streaming(broker)

    # Test dry run trading
    order_ok = test_dry_run_order(broker)

    # Cleanup
    if broker:
        broker.disable_websocket()

    print(f"\nTest completed at: {datetime.now()}")
    print("=" * 50)

    # Summary
    print("\n=== Test Summary ===")
    print(f"✓ Broker initialization: {'✓' if broker else '✗'}")
    print(f"✓ Profile access: {'✓' if profile_ok else '✗'}")
    print(f"✓ Funds access: {'✓' if funds_ok else '✗'}")
    print(f"✓ Positions access: {'✓' if positions_ok else '✗'}")
    print(f"✓ WebSocket connection: {'✓' if ws_connected else '✗'}")
    print(f"✓ Dry run order: {'✓' if order_ok else '✗'}")

    if ws_connected:
        print("✓ Real-time data streaming is available!")

    print("\n🎉 Fyers integration is working with real credentials!")


if __name__ == "__main__":
    main()
