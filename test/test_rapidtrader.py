#!/usr/bin/env python3
"""
RapidTrader Test Suite

This script provides a comprehensive test suite for the RapidTrader trading engine.
It can test all modules and features, and record changes between test runs.
"""

import os
import sys
import unittest
import logging
import json
import time
import datetime
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import pandas as pd
import numpy as np

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("rapidtrader_test")

# Constants
TEST_RESULTS_DIR = os.path.join("test", "results")
TEST_HISTORY_FILE = os.path.join(TEST_RESULTS_DIR, "test_history.json")


class TestResult:
    """Class to store test results and changes."""
    
    def __init__(self, name: str, module: str, success: bool, message: str = "", duration: float = 0.0):
        """
        Initialize a test result.
        
        Args:
            name: Test name
            module: Module being tested
            success: Whether the test passed
            message: Test message or error
            duration: Test duration in seconds
        """
        self.name = name
        self.module = module
        self.success = success
        self.message = message
        self.duration = duration
        self.timestamp = datetime.datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the test result to a dictionary."""
        return {
            "name": self.name,
            "module": self.module,
            "success": self.success,
            "message": self.message,
            "duration": self.duration,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestResult':
        """Create a test result from a dictionary."""
        return cls(
            name=data["name"],
            module=data["module"],
            success=data["success"],
            message=data.get("message", ""),
            duration=data.get("duration", 0.0)
        )


class TestHistory:
    """Class to manage test history and changes."""
    
    def __init__(self, history_file: str = TEST_HISTORY_FILE):
        """
        Initialize test history.
        
        Args:
            history_file: Path to the history file
        """
        self.history_file = history_file
        self.history = self._load_history()
    
    def _load_history(self) -> Dict[str, List[Dict[str, Any]]]:
        """Load test history from file."""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, "r") as f:
                    return json.load(f)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in history file: {self.history_file}")
        
        return {"tests": []}
    
    def save_history(self) -> None:
        """Save test history to file."""
        os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
        with open(self.history_file, "w") as f:
            json.dump(self.history, f, indent=4)
    
    def add_result(self, result: TestResult) -> None:
        """Add a test result to the history."""
        self.history["tests"].append(result.to_dict())
    
    def get_changes(self, current_results: List[TestResult]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get changes between current results and previous run.
        
        Args:
            current_results: Current test results
            
        Returns:
            Dictionary with changes
        """
        if not self.history["tests"]:
            return {"new": [r.to_dict() for r in current_results], "fixed": [], "broken": []}
        
        # Get the most recent run for each test
        previous_results = {}
        for test in self.history["tests"]:
            test_key = f"{test['module']}.{test['name']}"
            if test_key not in previous_results or test["timestamp"] > previous_results[test_key]["timestamp"]:
                previous_results[test_key] = test
        
        # Compare current results with previous results
        changes = {"new": [], "fixed": [], "broken": []}
        
        for result in current_results:
            test_key = f"{result.module}.{result.name}"
            
            if test_key not in previous_results:
                # New test
                changes["new"].append(result.to_dict())
            elif result.success and not previous_results[test_key]["success"]:
                # Fixed test
                changes["fixed"].append({
                    "current": result.to_dict(),
                    "previous": previous_results[test_key]
                })
            elif not result.success and previous_results[test_key]["success"]:
                # Broken test
                changes["broken"].append({
                    "current": result.to_dict(),
                    "previous": previous_results[test_key]
                })
        
        return changes


class RapidTraderTestCase(unittest.TestCase):
    """Base class for RapidTrader test cases."""
    
    def setUp(self):
        """Set up the test case."""
        self.start_time = time.time()
    
    def tearDown(self):
        """Tear down the test case."""
        self.duration = time.time() - self.start_time


class DataModuleTest(RapidTraderTestCase):
    """Test cases for the data module."""
    
    def test_data_fetcher_initialization(self):
        """Test data fetcher initialization."""
        try:
            from data.fetcher import Fetcher
            
            fetcher = Fetcher()
            self.assertIsNotNone(fetcher)
            self.duration = time.time() - self.start_time
        except Exception as e:
            self.duration = time.time() - self.start_time
            self.fail(f"Data fetcher initialization failed: {e}")
    
    def test_yfinance_fetcher(self):
        """Test YFinance fetcher."""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            fetcher = YFinanceFetcher()
            self.assertIsNotNone(fetcher)
            self.duration = time.time() - self.start_time
        except Exception as e:
            self.duration = time.time() - self.start_time
            self.fail(f"YFinance fetcher initialization failed: {e}")
    
    def test_data_manager(self):
        """Test data manager."""
        try:
            from data.data_manager import DataManager
            from data.yfinance_fetcher import YFinanceFetcher
            
            data_fetcher = YFinanceFetcher()
            data_manager = DataManager(data_fetcher=data_fetcher)
            self.assertIsNotNone(data_manager)
            self.duration = time.time() - self.start_time
        except Exception as e:
            self.duration = time.time() - self.start_time
            self.fail(f"Data manager initialization failed: {e}")


class BrokerModuleTest(RapidTraderTestCase):
    """Test cases for the broker module."""
    
    def test_broker_initialization(self):
        """Test broker initialization."""
        try:
            from broker import BaseBroker, DhanBroker
            
            # We can't fully initialize DhanBroker without credentials,
            # but we can check if the class is available
            self.assertTrue(hasattr(DhanBroker, '__init__'))
            self.duration = time.time() - self.start_time
        except Exception as e:
            self.duration = time.time() - self.start_time
            self.fail(f"Broker initialization failed: {e}")


class CoreModuleTest(RapidTraderTestCase):
    """Test cases for the core module."""
    
    def test_core_initialization(self):
        """Test core module initialization."""
        try:
            import core
            
            self.assertIsNotNone(core)
            self.duration = time.time() - self.start_time
        except Exception as e:
            self.duration = time.time() - self.start_time
            self.fail(f"Core module initialization failed: {e}")


class CLITest(RapidTraderTestCase):
    """Test cases for the CLI."""
    
    def test_cli_initialization(self):
        """Test CLI initialization."""
        try:
            import rapidtrader
            
            self.assertIsNotNone(rapidtrader)
            self.duration = time.time() - self.start_time
        except Exception as e:
            self.duration = time.time() - self.start_time
            self.fail(f"CLI initialization failed: {e}")


def run_tests(modules: List[str] = None) -> List[TestResult]:
    """
    Run tests for specified modules.
    
    Args:
        modules: List of modules to test (None for all)
        
    Returns:
        List of test results
    """
    results = []
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases based on modules
    if not modules or "data" in modules:
        suite.addTest(loader.loadTestsFromTestCase(DataModuleTest))
    
    if not modules or "broker" in modules:
        suite.addTest(loader.loadTestsFromTestCase(BrokerModuleTest))
    
    if not modules or "core" in modules:
        suite.addTest(loader.loadTestsFromTestCase(CoreModuleTest))
    
    if not modules or "cli" in modules:
        suite.addTest(loader.loadTestsFromTestCase(CLITest))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    test_result = runner.run(suite)
    
    # Process results
    for test_case in test_result.failures + test_result.errors:
        test_name = test_case[0]._testMethodName
        test_module = test_case[0].__class__.__module__
        test_class = test_case[0].__class__.__name__
        module_name = test_class.replace("Test", "").lower()
        
        results.append(TestResult(
            name=test_name,
            module=module_name,
            success=False,
            message=test_case[1],
            duration=getattr(test_case[0], 'duration', 0.0)
        ))
    
    for test in test_result.successes:
        test_name = test._testMethodName
        test_class = test.__class__.__name__
        module_name = test_class.replace("Test", "").lower()
        
        results.append(TestResult(
            name=test_name,
            module=module_name,
            success=True,
            duration=getattr(test, 'duration', 0.0)
        ))
    
    return results


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="RapidTrader Test Suite")
    parser.add_argument(
        "--modules",
        help="Comma-separated list of modules to test (default: all)",
    )
    parser.add_argument(
        "--history-file",
        default=TEST_HISTORY_FILE,
        help="Path to the history file",
    )
    parser.add_argument(
        "--no-history",
        action="store_true",
        help="Don't record test history",
    )
    args = parser.parse_args()
    
    # Parse modules
    modules = None
    if args.modules:
        modules = [m.strip() for m in args.modules.split(",")]
    
    # Run tests
    results = run_tests(modules)
    
    # Process history and changes
    if not args.no_history:
        history = TestHistory(args.history_file)
        changes = history.get_changes(results)
        
        # Print changes
        if changes["new"]:
            print("\nNew tests:")
            for test in changes["new"]:
                status = "PASS" if test["success"] else "FAIL"
                print(f"  {test['module']}.{test['name']}: {status}")
        
        if changes["fixed"]:
            print("\nFixed tests:")
            for test in changes["fixed"]:
                print(f"  {test['current']['module']}.{test['current']['name']}")
        
        if changes["broken"]:
            print("\nBroken tests:")
            for test in changes["broken"]:
                print(f"  {test['current']['module']}.{test['current']['name']}")
        
        # Add results to history
        for result in results:
            history.add_result(result)
        
        # Save history
        history.save_history()
    
    # Print summary
    success_count = sum(1 for r in results if r.success)
    total_count = len(results)
    
    print(f"\nTest Summary: {success_count}/{total_count} tests passed")
    
    # Return exit code based on test results
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    sys.exit(main())
