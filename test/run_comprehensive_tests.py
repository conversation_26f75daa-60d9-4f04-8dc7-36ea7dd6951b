#!/usr/bin/env python3
"""
Comprehensive Test Runner for RapidTrader

This script runs all test suites and generates a comprehensive test report.
"""

import os
import sys
import unittest
import json
import time
from datetime import datetime
from pathlib import Path
from io import StringIO

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestResult:
    """Custom test result class to capture detailed information"""
    
    def __init__(self):
        self.tests_run = 0
        self.failures = []
        self.errors = []
        self.skipped = []
        self.successes = []
        self.start_time = None
        self.end_time = None
    
    def start_test(self, test):
        """Called when a test starts"""
        if self.start_time is None:
            self.start_time = time.time()
    
    def add_success(self, test):
        """Called when a test passes"""
        self.tests_run += 1
        self.successes.append({
            'test': str(test),
            'status': 'PASS'
        })
    
    def add_failure(self, test, err):
        """Called when a test fails"""
        self.tests_run += 1
        self.failures.append({
            'test': str(test),
            'status': 'FAIL',
            'error': str(err[1])
        })
    
    def add_error(self, test, err):
        """Called when a test has an error"""
        self.tests_run += 1
        self.errors.append({
            'test': str(test),
            'status': 'ERROR',
            'error': str(err[1])
        })
    
    def add_skip(self, test, reason):
        """Called when a test is skipped"""
        self.tests_run += 1
        self.skipped.append({
            'test': str(test),
            'status': 'SKIP',
            'reason': str(reason)
        })
    
    def stop_test(self, test):
        """Called when a test ends"""
        self.end_time = time.time()
    
    def get_summary(self):
        """Get test summary"""
        total_time = (self.end_time - self.start_time) if self.end_time and self.start_time else 0
        
        return {
            'total_tests': self.tests_run,
            'passed': len(self.successes),
            'failed': len(self.failures),
            'errors': len(self.errors),
            'skipped': len(self.skipped),
            'success_rate': (len(self.successes) / self.tests_run * 100) if self.tests_run > 0 else 0,
            'execution_time': total_time,
            'timestamp': datetime.now().isoformat()
        }


class ComprehensiveTestRunner:
    """Comprehensive test runner for RapidTrader"""
    
    def __init__(self):
        self.test_modules = [
            'test_core_initialization',
            'test_broker_integrations', 
            'test_backtesting_engine',
            'test_api_gateway_core',
            'test_money_management',
            'test_data_management',
            'test_docker_integration'
        ]
        
        self.results = {}
        self.overall_result = TestResult()
    
    def run_test_module(self, module_name):
        """Run tests for a specific module"""
        print(f"\n{'='*60}")
        print(f"Running {module_name}")
        print(f"{'='*60}")
        
        try:
            # Import the test module
            test_module = __import__(module_name)
            
            # Create test suite
            loader = unittest.TestLoader()
            suite = loader.loadTestsFromModule(test_module)
            
            # Run tests with custom result
            stream = StringIO()
            runner = unittest.TextTestRunner(
                stream=stream,
                verbosity=2,
                resultclass=unittest.TestResult
            )
            
            result = runner.run(suite)
            
            # Capture results
            module_result = {
                'module': module_name,
                'tests_run': result.testsRun,
                'failures': len(result.failures),
                'errors': len(result.errors),
                'skipped': len(result.skipped),
                'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
                'details': {
                    'failures': [{'test': str(test), 'error': str(error)} for test, error in result.failures],
                    'errors': [{'test': str(test), 'error': str(error)} for test, error in result.errors],
                    'skipped': [{'test': str(test), 'reason': str(reason)} for test, reason in result.skipped]
                },
                'output': stream.getvalue()
            }
            
            self.results[module_name] = module_result
            
            # Update overall results
            self.overall_result.tests_run += result.testsRun
            self.overall_result.failures.extend([{'test': str(test), 'error': str(error)} for test, error in result.failures])
            self.overall_result.errors.extend([{'test': str(test), 'error': str(error)} for test, error in result.errors])
            self.overall_result.skipped.extend([{'test': str(test), 'reason': str(reason)} for test, reason in result.skipped])
            
            # Print summary
            print(f"\nModule: {module_name}")
            print(f"Tests run: {result.testsRun}")
            print(f"Failures: {len(result.failures)}")
            print(f"Errors: {len(result.errors)}")
            print(f"Skipped: {len(result.skipped)}")
            print(f"Success rate: {module_result['success_rate']:.1f}%")
            
            return module_result
            
        except ImportError as e:
            print(f"Could not import {module_name}: {e}")
            error_result = {
                'module': module_name,
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'skipped': 0,
                'success_rate': 0,
                'details': {
                    'failures': [],
                    'errors': [{'test': module_name, 'error': str(e)}],
                    'skipped': []
                },
                'output': f"ImportError: {e}"
            }
            self.results[module_name] = error_result
            return error_result
        
        except Exception as e:
            print(f"Error running {module_name}: {e}")
            error_result = {
                'module': module_name,
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'skipped': 0,
                'success_rate': 0,
                'details': {
                    'failures': [],
                    'errors': [{'test': module_name, 'error': str(e)}],
                    'skipped': []
                },
                'output': f"Error: {e}"
            }
            self.results[module_name] = error_result
            return error_result
    
    def run_all_tests(self):
        """Run all test modules"""
        print("🚀 RapidTrader Comprehensive Test Suite")
        print("=" * 60)
        
        start_time = time.time()
        
        for module_name in self.test_modules:
            self.run_test_module(module_name)
        
        end_time = time.time()
        
        # Generate overall summary
        total_tests = sum(result['tests_run'] for result in self.results.values())
        total_failures = sum(result['failures'] for result in self.results.values())
        total_errors = sum(result['errors'] for result in self.results.values())
        total_skipped = sum(result['skipped'] for result in self.results.values())
        total_passed = total_tests - total_failures - total_errors
        
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n{'='*60}")
        print("OVERALL TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total test modules: {len(self.test_modules)}")
        print(f"Total tests run: {total_tests}")
        print(f"Passed: {total_passed}")
        print(f"Failed: {total_failures}")
        print(f"Errors: {total_errors}")
        print(f"Skipped: {total_skipped}")
        print(f"Success rate: {overall_success_rate:.1f}%")
        print(f"Execution time: {end_time - start_time:.2f} seconds")
        
        # Generate detailed report
        self.generate_report()
        
        return overall_success_rate >= 80  # Consider 80% success rate as passing
    
    def generate_report(self):
        """Generate detailed test report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_modules': len(self.test_modules),
                'total_tests': sum(result['tests_run'] for result in self.results.values()),
                'total_passed': sum(result['tests_run'] - result['failures'] - result['errors'] for result in self.results.values()),
                'total_failed': sum(result['failures'] for result in self.results.values()),
                'total_errors': sum(result['errors'] for result in self.results.values()),
                'total_skipped': sum(result['skipped'] for result in self.results.values()),
                'overall_success_rate': 0
            },
            'modules': self.results
        }
        
        # Calculate overall success rate
        if report['summary']['total_tests'] > 0:
            report['summary']['overall_success_rate'] = (
                report['summary']['total_passed'] / report['summary']['total_tests'] * 100
            )
        
        # Save JSON report
        results_dir = Path("test/results")
        results_dir.mkdir(exist_ok=True)
        
        json_report_path = results_dir / "comprehensive_test_report.json"
        with open(json_report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nDetailed report saved to: {json_report_path}")
        
        # Generate HTML report
        self.generate_html_report(report)
    
    def generate_html_report(self, report):
        """Generate HTML test report"""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>RapidTrader Comprehensive Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .module {{ margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }}
        .module-header {{ background-color: #e9e9e9; padding: 10px; font-weight: bold; }}
        .module-content {{ padding: 10px; }}
        .pass {{ color: green; }}
        .fail {{ color: red; }}
        .skip {{ color: orange; }}
        .error {{ color: red; font-weight: bold; }}
        table {{ width: 100%; border-collapse: collapse; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 RapidTrader Comprehensive Test Report</h1>
        <p>Generated on: {report['timestamp']}</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Total Modules</td><td>{report['summary']['total_modules']}</td></tr>
            <tr><td>Total Tests</td><td>{report['summary']['total_tests']}</td></tr>
            <tr><td>Passed</td><td class="pass">{report['summary']['total_passed']}</td></tr>
            <tr><td>Failed</td><td class="fail">{report['summary']['total_failed']}</td></tr>
            <tr><td>Errors</td><td class="error">{report['summary']['total_errors']}</td></tr>
            <tr><td>Skipped</td><td class="skip">{report['summary']['total_skipped']}</td></tr>
            <tr><td>Success Rate</td><td>{report['summary']['overall_success_rate']:.1f}%</td></tr>
        </table>
    </div>
    
    <h2>Module Details</h2>
"""
        
        for module_name, module_result in report['modules'].items():
            html_content += f"""
    <div class="module">
        <div class="module-header">{module_name}</div>
        <div class="module-content">
            <p>Tests: {module_result['tests_run']}, 
               Passed: <span class="pass">{module_result['tests_run'] - module_result['failures'] - module_result['errors']}</span>, 
               Failed: <span class="fail">{module_result['failures']}</span>, 
               Errors: <span class="error">{module_result['errors']}</span>, 
               Skipped: <span class="skip">{module_result['skipped']}</span>
               (Success Rate: {module_result['success_rate']:.1f}%)</p>
        </div>
    </div>
"""
        
        html_content += """
</body>
</html>
"""
        
        # Save HTML report
        results_dir = Path("test/results")
        html_report_path = results_dir / "comprehensive_test_report.html"
        with open(html_report_path, 'w') as f:
            f.write(html_content)
        
        print(f"HTML report saved to: {html_report_path}")


def main():
    """Main function"""
    runner = ComprehensiveTestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n✅ Test suite completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test suite completed with issues!")
        sys.exit(1)


if __name__ == '__main__':
    main()
