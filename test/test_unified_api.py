#!/usr/bin/env python3
"""
Test script for RapidTrader Unified API v2.0

Tests the unified symbol system and API functionality.
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configuration
API_BASE_URL = "http://localhost:8000"
DEMO_API_KEY = "rt_demo_key_12345"

class UnifiedAPITest:
    """Test class for RapidTrader Unified API"""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {DEMO_API_KEY}",
            "Content-Type": "application/json"
        }
        
    def test_health_check(self):
        """Test API health check"""
        print("🔍 Testing API health...")
        
        try:
            response = requests.get(f"{self.base_url}/health")
            response.raise_for_status()
            
            health_data = response.json()
            print(f"✅ API is healthy")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Dhan symbols: {health_data.get('symbol_mappers', {}).get('dhan_symbols', 0)}")
            print(f"   Fyers symbols: {health_data.get('symbol_mappers', {}).get('fyers_symbols', 0)}")
            return True
            
        except Exception as e:
            print(f"❌ Health check failed: {e}")
            return False
    
    def test_symbol_search(self):
        """Test symbol search functionality"""
        print("\n🔍 Testing symbol search...")
        
        test_symbols = ["SBIN", "RELIANCE", "TCS", "INFY"]
        
        for symbol in test_symbols:
            try:
                response = requests.get(
                    f"{self.base_url}/symbols/search/{symbol}",
                    headers=self.headers,
                    params={"exchange": "NSE_EQ"}
                )
                response.raise_for_status()
                
                data = response.json()
                print(f"✅ {symbol}:")
                print(f"   RapidTrader: {data.get('rapidtrader_symbol')}")
                print(f"   Fyers: {data.get('fyers_symbol', 'Not mapped')}")
                print(f"   Dhan ID: {data.get('dhan_security_id', 'Not mapped')}")
                print(f"   Available: {data.get('is_available')}")
                
            except Exception as e:
                print(f"❌ Failed to search {symbol}: {e}")
        
        return True
    
    def test_symbol_listing(self):
        """Test symbol listing"""
        print("\n📋 Testing symbol listing...")
        
        try:
            response = requests.get(
                f"{self.base_url}/symbols/list",
                headers=self.headers,
                params={"exchange": "NSE_EQ", "limit": 10}
            )
            response.raise_for_status()
            
            data = response.json()
            symbols = data.get("symbols", [])
            
            print(f"✅ Found {len(symbols)} symbols:")
            for symbol in symbols[:5]:  # Show first 5
                print(f"   📊 {symbol}")
            
            if len(symbols) > 5:
                print(f"   ... and {len(symbols) - 5} more")
            
            print(f"   Total available: {data.get('total_available', 0)}")
            return True
            
        except Exception as e:
            print(f"❌ Symbol listing failed: {e}")
            return False
    
    def test_broker_management(self):
        """Test broker management"""
        print("\n🏦 Testing broker management...")
        
        # Test listing brokers (should be empty initially)
        try:
            response = requests.get(
                f"{self.base_url}/brokers/list",
                headers=self.headers
            )
            response.raise_for_status()
            
            data = response.json()
            brokers = data.get("brokers", [])
            
            print(f"✅ Current brokers: {len(brokers)}")
            for broker in brokers:
                print(f"   🏦 {broker.get('id')} ({broker.get('type')})")
            
            return True
            
        except Exception as e:
            print(f"❌ Broker listing failed: {e}")
            return False
    
    def test_api_documentation(self):
        """Test API documentation access"""
        print("\n📚 Testing API documentation...")
        
        try:
            # Test docs endpoint
            response = requests.get(f"{self.base_url}/docs")
            if response.status_code == 200:
                print("✅ API documentation accessible at /docs")
            
            # Test OpenAPI spec
            response = requests.get(f"{self.base_url}/openapi.json")
            if response.status_code == 200:
                spec = response.json()
                print(f"✅ OpenAPI specification available")
                print(f"   Title: {spec.get('info', {}).get('title')}")
                print(f"   Version: {spec.get('info', {}).get('version')}")
                print(f"   Endpoints: {len(spec.get('paths', {}))}")
            
            return True
            
        except Exception as e:
            print(f"❌ Documentation test failed: {e}")
            return False
    
    def test_unified_symbol_demo(self):
        """Demonstrate unified symbol system"""
        print("\n🎯 Unified Symbol System Demo...")
        
        demo_symbols = [
            {"symbol": "SBIN", "name": "State Bank of India"},
            {"symbol": "RELIANCE", "name": "Reliance Industries"},
            {"symbol": "TCS", "name": "Tata Consultancy Services"}
        ]
        
        print("\n📊 Symbol Mapping Demonstration:")
        print("=" * 60)
        
        for item in demo_symbols:
            symbol = item["symbol"]
            name = item["name"]
            
            try:
                response = requests.get(
                    f"{self.base_url}/symbols/search/{symbol}",
                    headers=self.headers,
                    params={"exchange": "NSE_EQ"}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    print(f"\n🏢 {name}")
                    print(f"   RapidTrader Symbol: {data.get('rapidtrader_symbol', 'N/A')}")
                    print(f"   Fyers Format:      {data.get('fyers_symbol', 'Not mapped')}")
                    print(f"   Dhan Security ID:  {data.get('dhan_security_id', 'Not mapped')}")
                    print(f"   Status:            {'✅ Available' if data.get('is_available') else '❌ Not available'}")
                else:
                    print(f"\n❌ {name}: Symbol not found")
                    
            except Exception as e:
                print(f"\n❌ {name}: Error - {e}")
        
        print("\n" + "=" * 60)
        print("🎯 Key Benefits:")
        print("  ✅ Use simple symbols (SBIN, RELIANCE) everywhere")
        print("  ✅ Automatic conversion to broker-specific formats")
        print("  ✅ No need to remember different symbol conventions")
        print("  ✅ Easy strategy portability between brokers")
        
        return True
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 RapidTrader Unified API Test Suite")
        print("Inspired by OpenAlgo & FreqTrade, Built for RapidTrader")
        print("=" * 70)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("Symbol Search", self.test_symbol_search),
            ("Symbol Listing", self.test_symbol_listing),
            ("Broker Management", self.test_broker_management),
            ("API Documentation", self.test_api_documentation),
            ("Unified Symbol Demo", self.test_unified_symbol_demo)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                if test_func():
                    passed += 1
                time.sleep(1)  # Small delay between tests
            except Exception as e:
                print(f"❌ Test '{test_name}' failed with exception: {e}")
        
        print("\n" + "=" * 70)
        print(f"🎯 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! RapidTrader Unified API is working perfectly.")
            print("\n🚀 Next Steps:")
            print("  1. Add your broker: ./scripts/rapidtrader-api.sh add-fyers")
            print("  2. Search symbols: ./scripts/rapidtrader-api.sh search-symbol YOURSTOCK")
            print("  3. Start trading with unified symbols!")
        else:
            print("⚠️  Some tests failed. Please check the API setup.")
        
        return passed == total

def main():
    """Main test function"""
    print("RapidTrader Unified API Test Suite v2.0")
    print("Testing unified symbol system and API functionality")
    print()
    
    # Check if API is running
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ API is not responding correctly")
            print("Please start the API with: ./scripts/rapidtrader-api.sh start")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to API at {API_BASE_URL}")
        print("Please start the API with: ./scripts/rapidtrader-api.sh start")
        return False
    except Exception as e:
        print(f"❌ Error checking API: {e}")
        return False
    
    # Run tests
    tester = UnifiedAPITest()
    success = tester.run_all_tests()
    
    if success:
        print(f"\n📖 API Documentation: {API_BASE_URL}/docs")
        print(f"🔑 Demo API Key: {DEMO_API_KEY}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
