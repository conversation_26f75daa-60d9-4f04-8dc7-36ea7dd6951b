#!/usr/bin/env python3
"""
RapidTrader Test Runner

This script runs all the test suites for the RapidTrader trading engine
and generates a comprehensive report.
"""

import os
import sys
import argparse
import unittest
import logging
import json
import time
import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import importlib
import subprocess
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("test_runner")

# Constants
TEST_RESULTS_DIR = os.path.join("test", "results")
TEST_HISTORY_FILE = os.path.join(TEST_RESULTS_DIR, "test_history.json")
TEST_REPORT_FILE = os.path.join(TEST_RESULTS_DIR, "test_report.json")
TEST_REPORT_HTML = os.path.join(TEST_RESULTS_DIR, "test_report.html")

# Initialize rich console
console = Console()


class TestResult:
    """Class to store test results and changes."""

    def __init__(self, name: str, module: str, success: bool, message: str = "", duration: float = 0.0):
        """
        Initialize a test result.

        Args:
            name: Test name
            module: Module being tested
            success: Whether the test passed
            message: Test message or error
            duration: Test duration in seconds
        """
        self.name = name
        self.module = module
        self.success = success
        self.message = message
        self.duration = duration
        self.timestamp = datetime.datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert the test result to a dictionary."""
        return {
            "name": self.name,
            "module": self.module,
            "success": self.success,
            "message": self.message,
            "duration": self.duration,
            "timestamp": self.timestamp
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestResult':
        """Create a test result from a dictionary."""
        return cls(
            name=data["name"],
            module=data["module"],
            success=data["success"],
            message=data.get("message", ""),
            duration=data.get("duration", 0.0)
        )


def run_test_module(module_name: str) -> List[TestResult]:
    """
    Run tests for a specific module.

    Args:
        module_name: Name of the test module to run

    Returns:
        List of test results
    """
    results = []

    try:
        # Get the full path to the test module
        module_path = os.path.join("test", f"{module_name}.py")

        # Run the test module as a subprocess
        process = subprocess.run(
            [sys.executable, module_path],
            capture_output=True,
            text=True
        )

        # Check if the tests passed
        if process.returncode == 0:
            # Tests passed
            results.append(TestResult(
                name=module_name,
                module=module_name.replace("test_", ""),
                success=True,
                message="All tests passed",
                duration=0.0
            ))
        else:
            # Tests failed
            results.append(TestResult(
                name=module_name,
                module=module_name.replace("test_", ""),
                success=False,
                message=process.stderr or process.stdout,
                duration=0.0
            ))
    except Exception as e:
        logger.error(f"Error running tests for module {module_name}: {e}")
        results.append(TestResult(
            name="module_import",
            module=module_name.replace("test_", ""),
            success=False,
            message=str(e),
            duration=0.0
        ))

    return results


def run_comprehensive_tests() -> bool:
    """Run comprehensive test suite"""
    try:
        from test.run_comprehensive_tests import ComprehensiveTestRunner

        console.print("🚀 Running RapidTrader Comprehensive Test Suite", style="bold green")
        console.print("=" * 60)

        runner = ComprehensiveTestRunner()
        success = runner.run_all_tests()

        return success

    except ImportError as e:
        console.print(f"Comprehensive test runner not available: {e}", style="bold red")
        console.print("Falling back to standard test discovery...", style="yellow")
        return False

def run_all_tests(modules: List[str] = None) -> List[TestResult]:
    """
    Run tests for all modules.

    Args:
        modules: List of modules to test (None for all)

    Returns:
        List of test results
    """
    results = []

    # Get all test modules
    if not modules:
        modules = []
        for file in os.listdir("test"):
            if file.startswith("test_") and file.endswith(".py"):
                modules.append(file[:-3])

    # Run tests for each module
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        console=console
    ) as progress:
        task = progress.add_task("[green]Running tests...", total=len(modules))

        for module in modules:
            progress.update(task, description=f"[green]Running tests for {module}...")
            module_results = run_test_module(module)
            results.extend(module_results)
            progress.advance(task)

    return results


def save_results(results: List[TestResult], report_file: str = TEST_REPORT_FILE) -> None:
    """
    Save test results to a file.

    Args:
        results: List of test results
        report_file: Path to the report file
    """
    os.makedirs(os.path.dirname(report_file), exist_ok=True)

    # Convert results to dictionaries
    results_dict = {
        "timestamp": datetime.datetime.now().isoformat(),
        "results": [r.to_dict() for r in results]
    }

    # Save to file
    with open(report_file, "w") as f:
        json.dump(results_dict, f, indent=4)


def generate_html_report(results: List[TestResult], report_file: str = TEST_REPORT_HTML) -> None:
    """
    Generate an HTML report from test results.

    Args:
        results: List of test results
        report_file: Path to the HTML report file
    """
    os.makedirs(os.path.dirname(report_file), exist_ok=True)

    # Group results by module
    modules = {}
    for result in results:
        if result.module not in modules:
            modules[result.module] = []
        modules[result.module].append(result)

    # Calculate statistics
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r.success)
    failed_tests = total_tests - passed_tests
    pass_rate = passed_tests / total_tests * 100 if total_tests > 0 else 0

    # Generate HTML
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>RapidTrader Test Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1 {{ color: #333; }}
            .summary {{ margin-bottom: 20px; }}
            .module {{ margin-bottom: 30px; }}
            .module-header {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
            .test-table {{ width: 100%; border-collapse: collapse; }}
            .test-table th, .test-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            .test-table th {{ background-color: #f0f0f0; }}
            .test-row-pass {{ background-color: #dff0d8; }}
            .test-row-fail {{ background-color: #f2dede; }}
            .progress-bar {{ width: 100%; background-color: #f0f0f0; border-radius: 5px; }}
            .progress {{ height: 20px; background-color: #4CAF50; border-radius: 5px; text-align: center; color: white; }}
        </style>
    </head>
    <body>
        <h1>RapidTrader Test Report</h1>
        <div class="summary">
            <h2>Summary</h2>
            <p>Generated on: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
            <p>Total Tests: {total_tests}</p>
            <p>Passed: {passed_tests}</p>
            <p>Failed: {failed_tests}</p>
            <div class="progress-bar">
                <div class="progress" style="width: {pass_rate}%;">{pass_rate:.1f}%</div>
            </div>
        </div>
    """

    # Add module results
    for module_name, module_results in modules.items():
        module_total = len(module_results)
        module_passed = sum(1 for r in module_results if r.success)
        module_pass_rate = module_passed / module_total * 100 if module_total > 0 else 0

        html += f"""
        <div class="module">
            <h2 class="module-header">{module_name} ({module_passed}/{module_total})</h2>
            <div class="progress-bar">
                <div class="progress" style="width: {module_pass_rate}%;">{module_pass_rate:.1f}%</div>
            </div>
            <table class="test-table">
                <tr>
                    <th>Test</th>
                    <th>Status</th>
                    <th>Duration (s)</th>
                    <th>Message</th>
                </tr>
        """

        for result in module_results:
            row_class = "test-row-pass" if result.success else "test-row-fail"
            status = "Pass" if result.success else "Fail"

            html += f"""
                <tr class="{row_class}">
                    <td>{result.name}</td>
                    <td>{status}</td>
                    <td>{result.duration:.3f}</td>
                    <td>{result.message}</td>
                </tr>
            """

        html += """
            </table>
        </div>
        """

    html += """
    </body>
    </html>
    """

    # Save to file
    with open(report_file, "w") as f:
        f.write(html)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="RapidTrader Test Runner")
    parser.add_argument(
        "--modules",
        help="Comma-separated list of test modules to run (default: all)",
    )
    parser.add_argument(
        "--comprehensive", "-c",
        action="store_true",
        help="Run comprehensive test suite",
    )
    parser.add_argument(
        "--report-file",
        default=TEST_REPORT_FILE,
        help="Path to the JSON report file",
    )
    parser.add_argument(
        "--html-report",
        default=TEST_REPORT_HTML,
        help="Path to the HTML report file",
    )
    parser.add_argument(
        "--no-report",
        action="store_true",
        help="Don't generate a report",
    )
    args = parser.parse_args()

    # Check if comprehensive tests requested
    if args.comprehensive:
        success = run_comprehensive_tests()
        if success:
            console.print("\n✅ Comprehensive test suite completed successfully!", style="bold green")
            return 0
        else:
            console.print("\n❌ Comprehensive test suite completed with issues!", style="bold red")
            return 1

    # Parse modules
    modules = None
    if args.modules:
        modules = [m.strip() for m in args.modules.split(",")]

    # Run tests
    console.print("[bold green]Running RapidTrader tests...[/bold green]")
    start_time = time.time()
    results = run_all_tests(modules)
    duration = time.time() - start_time

    # Save results
    if not args.no_report:
        save_results(results, args.report_file)
        generate_html_report(results, args.html_report)
        console.print(f"[bold green]Test report saved to {args.report_file}[/bold green]")
        console.print(f"[bold green]HTML report saved to {args.html_report}[/bold green]")

    # Print summary
    success_count = sum(1 for r in results if r.success)
    total_count = len(results)

    table = Table(title="Test Summary")
    table.add_column("Module", style="cyan")
    table.add_column("Tests", style="magenta")
    table.add_column("Passed", style="green")
    table.add_column("Failed", style="red")
    table.add_column("Pass Rate", style="yellow")

    # Group results by module
    modules = {}
    for result in results:
        if result.module not in modules:
            modules[result.module] = {"total": 0, "passed": 0}
        modules[result.module]["total"] += 1
        if result.success:
            modules[result.module]["passed"] += 1

    # Add rows to the table
    for module, stats in modules.items():
        pass_rate = stats["passed"] / stats["total"] * 100 if stats["total"] > 0 else 0
        table.add_row(
            module,
            str(stats["total"]),
            str(stats["passed"]),
            str(stats["total"] - stats["passed"]),
            f"{pass_rate:.1f}%"
        )

    # Add total row
    pass_rate = success_count / total_count * 100 if total_count > 0 else 0
    table.add_row(
        "Total",
        str(total_count),
        str(success_count),
        str(total_count - success_count),
        f"{pass_rate:.1f}%",
        style="bold"
    )

    console.print(table)
    console.print(f"[bold]Total time: {duration:.2f} seconds[/bold]")

    # Return exit code based on test results
    return 0 if success_count == total_count else 1


if __name__ == "__main__":
    sys.exit(main())
