# RapidTrader Test Suite

This directory contains a comprehensive test suite for the RapidTrader trading engine. The test suite is designed to test all modules and features of the RapidTrader project, and to record changes between test runs.

## Test Files

- `test_rapidtrader.py`: Main test file that tests the core functionality of RapidTrader
- `test_data_module.py`: Tests for the data module (fetchers, data manager, etc.)
- `test_broker_module.py`: Tests for the broker module (DhanBroker, etc.)
- `run_tests.py`: Script to run all tests and generate a comprehensive report

## Running Tests

### Running All Tests

To run all tests and generate a comprehensive report, use the `run_tests.py` script:

```bash
python test/run_tests.py
```

This will run all tests and generate a JSON report and an HTML report in the `test/results` directory.

### Running Specific Test Modules

To run specific test modules, use the `--modules` option:

```bash
python test/run_tests.py --modules test_data_module,test_broker_module
```

### Running Individual Test Files

You can also run individual test files directly:

```bash
python test/test_data_module.py
python test/test_broker_module.py
python test/test_rapidtrader.py
```

## Test Reports

The test runner generates two types of reports:

1. **JSON Report**: A machine-readable report in JSON format, saved to `test/results/test_report.json`
2. **HTML Report**: A human-readable report in HTML format, saved to `test/results/test_report.html`

The HTML report includes:
- A summary of all tests
- Pass/fail statistics for each module
- Detailed results for each test
- Error messages for failed tests

## Test History

The test runner also maintains a history of test runs in `test/results/test_history.json`. This allows you to track changes between test runs, such as:

- New tests that were added
- Tests that were fixed (previously failing, now passing)
- Tests that were broken (previously passing, now failing)

To view changes between test runs, use the `test_rapidtrader.py` script with the `--history-file` option:

```bash
python test/test_rapidtrader.py --history-file test/results/test_history.json
```

## Adding New Tests

To add new tests:

1. Create a new test file in the `test` directory, following the naming convention `test_*.py`
2. Implement your test cases using the `unittest` framework
3. Add a `run_tests()` function that returns the test results
4. Add your test module to the appropriate section in `run_tests.py`

## Test Coverage

The test suite is designed to cover all major components of the RapidTrader project:

- **Core Module**: Basic functionality, CLI, etc.
- **Data Module**: Data fetchers, data manager, download functionality, etc.
- **Broker Module**: Broker wrappers, order placement, etc.
- **Backtester Module**: Backtesting functionality
- **Strategy Module**: Strategy management, execution, etc.

## Continuous Integration

The test suite is designed to be run in a CI/CD pipeline. You can add the following to your CI configuration:

```yaml
test:
  script:
    - python test/run_tests.py
  artifacts:
    paths:
      - test/results/
```

This will run all tests and save the test reports as artifacts.
