#!/usr/bin/env python3
"""
API Gateway Core Tests

Tests core API Gateway functionality including authentication, broker interface, and basic endpoints.
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestAPIGatewayCore(unittest.TestCase):
    """Test core API Gateway functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_api_gateway_import(self):
        """Test API Gateway can be imported"""
        try:
            from api_gateway.main import APIGateway
            
            # Should be able to import
            self.assertTrue(APIGateway)
            
        except ImportError as e:
            self.skipTest(f"API Gateway not available: {e}")
    
    @patch('docker.from_env')
    @patch('api_gateway.auth.auth_manager')
    @patch('api_gateway.broker_interface.broker_manager')
    def test_api_gateway_initialization(self, mock_broker_manager, mock_auth_manager, mock_docker):
        """Test API Gateway initialization"""
        try:
            from api_gateway.main import APIGateway
            
            # Mock dependencies
            mock_docker.return_value = Mock()
            mock_auth_manager.return_value = Mock()
            mock_broker_manager.return_value = Mock()
            
            gateway = APIGateway()
            
            # Verify basic initialization
            self.assertIsNotNone(gateway.app)
            self.assertEqual(gateway.app.title, "RapidTrader API Gateway")
            self.assertEqual(gateway.app.version, "2.0.0")
            
        except ImportError as e:
            self.skipTest(f"API Gateway not available: {e}")
    
    def test_pydantic_models(self):
        """Test Pydantic models for API Gateway"""
        try:
            from api_gateway.main import (
                APIKeyCreate, BrokerConfig, OrderRequest, 
                ContainerRequest, LogQuery, PnLQuery
            )
            
            # Test APIKeyCreate model
            api_key_data = APIKeyCreate(
                name="test_key",
                description="Test API key",
                permissions=["read", "write"]
            )
            
            self.assertEqual(api_key_data.name, "test_key")
            self.assertEqual(api_key_data.permissions, ["read", "write"])
            
            # Test BrokerConfig model
            broker_config = BrokerConfig(
                broker_name="fyers",
                credentials={"client_id": "test", "secret": "test"},
                dry_run=True
            )
            
            self.assertEqual(broker_config.broker_name, "fyers")
            self.assertTrue(broker_config.dry_run)
            
            # Test OrderRequest model
            order_request = OrderRequest(
                symbol="RELIANCE-EQ",
                quantity=100,
                side="BUY",
                order_type="MARKET"
            )
            
            self.assertEqual(order_request.symbol, "RELIANCE-EQ")
            self.assertEqual(order_request.quantity, 100)
            
        except ImportError as e:
            self.skipTest(f"API Gateway models not available: {e}")


class TestAuthenticationSystem(unittest.TestCase):
    """Test authentication system"""
    
    def test_auth_manager_import(self):
        """Test authentication manager can be imported"""
        try:
            from api_gateway.auth import AuthManager
            
            # Should be able to import
            self.assertTrue(AuthManager)
            
        except ImportError as e:
            self.skipTest(f"Authentication manager not available: {e}")
    
    def test_auth_manager_initialization(self):
        """Test authentication manager initialization"""
        try:
            from api_gateway.auth import AuthManager
            
            auth_manager = AuthManager()
            self.assertIsNotNone(auth_manager)
            
        except ImportError as e:
            self.skipTest(f"Authentication manager not available: {e}")
    
    def test_api_key_generation(self):
        """Test API key generation"""
        try:
            from api_gateway.auth import AuthManager
            
            auth_manager = AuthManager()
            
            # Generate API key
            key_id, api_key = auth_manager.generate_api_key(
                name="test_key",
                description="Test API key",
                permissions=["read", "write"]
            )
            
            # Verify key generation
            self.assertIsNotNone(key_id)
            self.assertIsNotNone(api_key)
            self.assertIsInstance(key_id, str)
            self.assertIsInstance(api_key, str)
            self.assertGreaterEqual(len(api_key), 32)  # Minimum length
            
        except ImportError as e:
            self.skipTest(f"Authentication manager not available: {e}")
    
    def test_api_key_validation(self):
        """Test API key validation"""
        try:
            from api_gateway.auth import AuthManager
            
            auth_manager = AuthManager()
            
            # Generate and validate key
            key_id, api_key = auth_manager.generate_api_key(
                name="validation_test",
                permissions=["read"]
            )
            
            # Test validation
            key_info = auth_manager.validate_api_key(api_key)
            self.assertIsNotNone(key_info)
            self.assertEqual(key_info['name'], "validation_test")
            
            # Test invalid key
            invalid_key_info = auth_manager.validate_api_key("invalid_key")
            self.assertIsNone(invalid_key_info)
            
        except ImportError as e:
            self.skipTest(f"Authentication manager not available: {e}")
    
    def test_rate_limiter(self):
        """Test rate limiter functionality"""
        try:
            from api_gateway.auth import RateLimiter
            
            rate_limiter = RateLimiter()
            
            # Test rate limiting
            user_id = "test_user"
            endpoint = "placeorder"
            
            # Should be allowed initially
            self.assertTrue(rate_limiter.is_allowed(user_id, endpoint))
            
        except ImportError as e:
            self.skipTest(f"Rate limiter not available: {e}")


class TestBrokerInterface(unittest.TestCase):
    """Test broker interface manager"""
    
    def test_broker_manager_import(self):
        """Test broker manager can be imported"""
        try:
            from api_gateway.broker_interface import BrokerManager
            
            # Should be able to import
            self.assertTrue(BrokerManager)
            
        except ImportError as e:
            self.skipTest(f"Broker manager not available: {e}")
    
    def test_broker_manager_initialization(self):
        """Test broker manager initialization"""
        try:
            from api_gateway.broker_interface import BrokerManager
            
            broker_manager = BrokerManager()
            self.assertIsNotNone(broker_manager)
            
        except ImportError as e:
            self.skipTest(f"Broker manager not available: {e}")
    
    def test_broker_registration(self):
        """Test broker registration"""
        try:
            from api_gateway.broker_interface import BrokerManager
            
            broker_manager = BrokerManager()
            
            # Test broker registration
            test_config = {
                "broker_name": "fyers",
                "credentials": {
                    "client_id": "test_client",
                    "secret_key": "test_secret"
                },
                "dry_run": True
            }
            
            success = broker_manager.add_broker("test_fyers", test_config)
            self.assertTrue(success)
            
            # Test broker listing
            brokers = broker_manager.list_brokers()
            self.assertIn("test_fyers", brokers)
            
            # Test broker removal
            success = broker_manager.remove_broker("test_fyers")
            self.assertTrue(success)
            
        except ImportError as e:
            self.skipTest(f"Broker manager not available: {e}")


class TestUnifiedAPI(unittest.TestCase):
    """Test unified API functionality"""
    
    def test_unified_api_import(self):
        """Test unified API can be imported"""
        try:
            from api_gateway.unified_api import UnifiedAPI
            
            # Should be able to import
            self.assertTrue(UnifiedAPI)
            
        except ImportError as e:
            self.skipTest(f"Unified API not available: {e}")
    
    def test_unified_api_initialization(self):
        """Test unified API initialization"""
        try:
            from api_gateway.unified_api import UnifiedAPI
            
            unified_api = UnifiedAPI()
            self.assertIsNotNone(unified_api)
            
        except ImportError as e:
            self.skipTest(f"Unified API not available: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
