#!/usr/bin/env python3
"""
Backtesting Engine Tests

Tests backtesting functionality including engines, trade simulation, and results.
"""

import os
import sys
import unittest
import tempfile
import shutil
import pandas as pd
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestBacktestEngine(unittest.TestCase):
    """Test core backtest engine functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_backtest_engine_import(self):
        """Test backtest engine can be imported"""
        try:
            from core.backtest_engine import BacktestEngine
            
            # Should be able to import
            self.assertTrue(BacktestEngine)
            
        except ImportError as e:
            self.skipTest(f"Backtest engine not available: {e}")
    
    def test_backtest_engine_initialization(self):
        """Test backtest engine initialization"""
        try:
            from core.backtest_engine import BacktestEngine
            
            engine = BacktestEngine(
                starting_balance=100000,
                commission=0.001,
                slippage=0.0001
            )
            
            self.assertIsNotNone(engine)
            self.assertEqual(engine.starting_balance, 100000)
            self.assertEqual(engine.commission, 0.001)
            self.assertEqual(engine.slippage, 0.0001)
            
        except ImportError as e:
            self.skipTest(f"Backtest engine not available: {e}")
    
    def test_optimized_backtest_engine(self):
        """Test optimized backtest engine"""
        try:
            from core.optimized_backtest_engine import OptimizedBacktestEngine
            
            # Should be able to import and instantiate
            engine = OptimizedBacktestEngine()
            self.assertIsNotNone(engine)
            
        except ImportError as e:
            self.skipTest(f"Optimized backtest engine not available: {e}")


class TestTradeSimulator(unittest.TestCase):
    """Test trade simulation functionality"""
    
    def test_trade_simulator_import(self):
        """Test trade simulator can be imported"""
        try:
            from core.trade_simulator import TradeSimulator
            
            # Should be able to import
            self.assertTrue(TradeSimulator)
            
        except ImportError as e:
            self.skipTest(f"Trade simulator not available: {e}")
    
    def test_trade_simulator_initialization(self):
        """Test trade simulator initialization"""
        try:
            from core.trade_simulator import TradeSimulator
            
            simulator = TradeSimulator(
                initial_balance=100000,
                commission=0.001,
                slippage=0.0001
            )
            
            self.assertIsNotNone(simulator)
            self.assertEqual(simulator.initial_balance, 100000)
            
        except ImportError as e:
            self.skipTest(f"Trade simulator not available: {e}")
    
    def create_test_data(self, symbol: str = "TEST", days: int = 100):
        """Create synthetic test data"""
        dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
        
        # Generate synthetic price data
        np.random.seed(42)  # For reproducible results
        base_price = 100.0
        returns = np.random.normal(0.001, 0.02, days)
        
        prices = [base_price]
        for i in range(1, days):
            prices.append(prices[-1] * (1 + returns[i]))
        
        data = pd.DataFrame({
            'date': dates,
            'open': prices,
            'high': [p * 1.02 for p in prices],
            'low': [p * 0.98 for p in prices],
            'close': prices,
            'volume': np.random.randint(100000, 1000000, days)
        })
        
        return data
    
    def test_trade_execution(self):
        """Test basic trade execution"""
        try:
            from core.trade_simulator import TradeSimulator
            
            simulator = TradeSimulator(initial_balance=100000)
            
            # Test entering a position
            simulator.enter_position(
                symbol="TEST",
                price=100.0,
                quantity=100,
                timestamp="2024-01-01"
            )
            
            # Check position exists
            self.assertTrue(simulator.has_position("TEST"))
            
            # Test exiting position
            simulator.exit_position(
                symbol="TEST",
                price=105.0,
                timestamp="2024-01-02"
            )
            
            # Check position closed
            self.assertFalse(simulator.has_position("TEST"))
            
            # Check trades recorded
            trades = simulator.get_trades()
            self.assertGreater(len(trades), 0)
            
        except ImportError as e:
            self.skipTest(f"Trade simulator not available: {e}")


class TestBacktestResults(unittest.TestCase):
    """Test backtest results functionality"""
    
    def test_backtest_results_import(self):
        """Test backtest results can be imported"""
        try:
            from core.backtest_results import BacktestResults
            
            # Should be able to import
            self.assertTrue(BacktestResults)
            
        except ImportError as e:
            self.skipTest(f"Backtest results not available: {e}")
    
    def test_backtest_results_initialization(self):
        """Test backtest results initialization"""
        try:
            from core.backtest_results import BacktestResults
            
            results = BacktestResults()
            self.assertIsNotNone(results)
            
        except ImportError as e:
            self.skipTest(f"Backtest results not available: {e}")
    
    def test_trade_recording(self):
        """Test trade recording and metrics calculation"""
        try:
            from core.backtest_results import BacktestResults
            
            results = BacktestResults()
            
            # Add test trades
            test_trades = [
                {
                    'symbol': 'TEST1',
                    'entry_price': 100.0,
                    'exit_price': 105.0,
                    'quantity': 100,
                    'profit_loss': 500.0,
                    'entry_time': '2024-01-01',
                    'exit_time': '2024-01-05'
                },
                {
                    'symbol': 'TEST2',
                    'entry_price': 200.0,
                    'exit_price': 195.0,
                    'quantity': 50,
                    'profit_loss': -250.0,
                    'entry_time': '2024-01-10',
                    'exit_time': '2024-01-15'
                }
            ]
            
            for trade in test_trades:
                results.add_trade(trade)
            
            # Calculate metrics
            metrics = results.calculate_metrics()
            
            # Verify basic metrics
            self.assertEqual(metrics['total_trades'], 2)
            self.assertEqual(metrics['winning_trades'], 1)
            self.assertEqual(metrics['losing_trades'], 1)
            self.assertEqual(metrics['total_profit_loss'], 250.0)
            self.assertEqual(metrics['win_rate'], 50.0)
            
        except ImportError as e:
            self.skipTest(f"Backtest results not available: {e}")


class TestDataManager(unittest.TestCase):
    """Test data manager for backtesting"""
    
    def test_data_manager_import(self):
        """Test data manager can be imported"""
        try:
            from core.data_manager import DataManager
            
            # Should be able to import
            self.assertTrue(DataManager)
            
        except ImportError as e:
            self.skipTest(f"Data manager not available: {e}")
    
    def test_data_manager_initialization(self):
        """Test data manager initialization"""
        try:
            from core.data_manager import DataManager
            
            data_manager = DataManager()
            self.assertIsNotNone(data_manager)
            
        except ImportError as e:
            self.skipTest(f"Data manager not available: {e}")


class TestBacktestIntegration(unittest.TestCase):
    """Test backtest integration functionality"""
    
    def test_backtest_module_import(self):
        """Test backtest module can be imported"""
        try:
            from core.backtest import run_backtest, show_backtest_results
            
            # Should be able to import functions
            self.assertTrue(callable(run_backtest))
            self.assertTrue(callable(show_backtest_results))
            
        except ImportError as e:
            self.skipTest(f"Backtest module not available: {e}")
    
    def test_enhanced_backtest_runner(self):
        """Test enhanced backtest runner"""
        try:
            # Check if enhanced backtest script exists
            script_path = project_root / "scripts" / "enhanced_backtest_runner.py"
            
            if script_path.exists():
                # Script exists, test import
                sys.path.insert(0, str(script_path.parent))
                
                # Should be able to import without errors
                self.assertTrue(script_path.exists())
            else:
                self.skipTest("Enhanced backtest runner script not found")
                
        except Exception as e:
            self.skipTest(f"Enhanced backtest runner not available: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
