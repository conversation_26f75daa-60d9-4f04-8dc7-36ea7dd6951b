#!/usr/bin/env python3
"""
Complete RapidTrader Functionality Test

This script tests the entire RapidTrader platform including:
- Fyers broker integration
- WebSocket real-time data
- CLI functionality
- Configuration management
- Docker compatibility
- Web interface components

Usage:
    python test_rapidtrader_complete.py [--broker fyers|dhan] [--test-websocket] [--test-cli] [--test-docker]
"""

import os
import sys
import time
import json
import subprocess
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from broker import FyersBroker, DhanBroker
    from broker.fyers_symbol_mapper import to_fyers_symbol, validate_fyers_symbol
    _has_brokers = True
except ImportError as e:
    _has_brokers = False
    print(f"Warning: Broker imports failed: {e}")

import argparse
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RapidTraderTester:
    """Comprehensive tester for RapidTrader functionality."""
    
    def __init__(self, broker_name='fyers'):
        self.broker_name = broker_name
        self.broker = None
        self.test_results = {
            'broker_connection': False,
            'profile_access': False,
            'funds_access': False,
            'positions_access': False,
            'websocket_connection': False,
            'websocket_subscription': False,
            'symbol_mapping': False,
            'cli_functionality': False,
            'config_files': False,
            'docker_compatibility': False
        }
        
        print(f"🚀 RapidTrader Complete Functionality Test")
        print(f"=" * 60)
        print(f"Testing broker: {broker_name.upper()}")
        print(f"Test started at: {datetime.now()}")
        print(f"=" * 60)
    
    def test_broker_initialization(self):
        """Test broker initialization and connection."""
        print("\n📡 Testing Broker Initialization...")
        
        try:
            if not _has_brokers:
                print("❌ Broker modules not available")
                return False
            
            if self.broker_name == 'fyers':
                self.broker = FyersBroker()
                
                # Test profile access
                profile = self.broker.get_profile()
                if profile and profile.get('s') == 'ok':
                    user_data = profile.get('data', {})
                    print(f"✅ Fyers connection successful")
                    print(f"   User: {user_data.get('fy_id', 'Unknown')}")
                    print(f"   Name: {user_data.get('name', 'Unknown')}")
                    self.test_results['broker_connection'] = True
                    self.test_results['profile_access'] = True
                else:
                    print(f"❌ Fyers connection failed: {profile.get('message', 'Unknown error') if profile else 'No response'}")
                    return False
                    
            elif self.broker_name == 'dhan':
                self.broker = DhanBroker()
                
                # Test profile access
                profile = self.broker.get_user_profile()
                if profile and profile.get('dhanClientId'):
                    print(f"✅ DhanHQ connection successful")
                    print(f"   Client ID: {profile.get('dhanClientId')}")
                    self.test_results['broker_connection'] = True
                    self.test_results['profile_access'] = True
                else:
                    print(f"❌ DhanHQ connection failed")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Broker initialization error: {e}")
            return False
    
    def test_account_access(self):
        """Test account information access."""
        print("\n💰 Testing Account Information Access...")
        
        if not self.broker:
            print("❌ No broker connection")
            return False
        
        try:
            # Test funds access
            if self.broker_name == 'fyers':
                funds = self.broker.get_funds()
                if funds and funds.get('s') == 'ok':
                    funds_data = funds.get('fund_limit', funds.get('data', {}))
                    if isinstance(funds_data, list) and funds_data:
                        funds_data = funds_data[0]
                    
                    available_cash = funds_data.get('availablecash', funds_data.get('available_cash', 0))
                    print(f"✅ Funds access successful")
                    print(f"   Available Cash: ₹{float(available_cash):,.2f}")
                    self.test_results['funds_access'] = True
                else:
                    print(f"❌ Funds access failed")
                
                # Test positions access
                positions = self.broker.get_positions()
                if positions and positions.get('s') == 'ok':
                    positions_data = positions.get('netPositions', [])
                    print(f"✅ Positions access successful")
                    print(f"   Open positions: {len(positions_data)}")
                    self.test_results['positions_access'] = True
                else:
                    print(f"❌ Positions access failed")
                    
            elif self.broker_name == 'dhan':
                funds = self.broker.get_fund_limits()
                if funds:
                    print(f"✅ DhanHQ funds access successful")
                    self.test_results['funds_access'] = True
                else:
                    print(f"❌ DhanHQ funds access failed")
                
                positions = self.broker.get_positions()
                if positions:
                    print(f"✅ DhanHQ positions access successful")
                    print(f"   Open positions: {len(positions)}")
                    self.test_results['positions_access'] = True
                else:
                    print(f"❌ DhanHQ positions access failed")
            
            return True
            
        except Exception as e:
            print(f"❌ Account access error: {e}")
            return False
    
    def test_websocket_functionality(self):
        """Test WebSocket real-time data functionality."""
        print("\n🌐 Testing WebSocket Functionality...")
        
        if not self.broker:
            print("❌ No broker connection")
            return False
        
        if self.broker_name != 'fyers':
            print("⚠️  WebSocket testing only available for Fyers")
            return True
        
        try:
            # Test WebSocket connection
            if self.broker.enable_websocket():
                print("✅ WebSocket connection successful")
                self.test_results['websocket_connection'] = True
                
                # Test symbol subscription
                test_symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ"]
                
                quote_count = 0
                def on_quote_update(symbol, data):
                    nonlocal quote_count
                    quote_count += 1
                    if quote_count <= 3:  # Show first 3 updates
                        ltp = data.get('ltp', 'N/A')
                        print(f"   📈 {symbol}: ₹{ltp}")
                
                self.broker.add_quote_callback(on_quote_update)
                
                if self.broker.subscribe_live_quotes(test_symbols):
                    print("✅ Symbol subscription successful")
                    print(f"   Subscribed to: {test_symbols}")
                    self.test_results['websocket_subscription'] = True
                    
                    # Monitor for a few seconds
                    print("   Monitoring live data for 10 seconds...")
                    time.sleep(10)
                    
                    if quote_count > 0:
                        print(f"✅ Received {quote_count} live updates")
                    else:
                        print("⚠️  No live updates received (market may be closed)")
                else:
                    print("❌ Symbol subscription failed")
                
                self.broker.disable_websocket()
            else:
                print("❌ WebSocket connection failed")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ WebSocket functionality error: {e}")
            return False
    
    def test_symbol_mapping(self):
        """Test symbol mapping functionality."""
        print("\n🔄 Testing Symbol Mapping...")
        
        if self.broker_name != 'fyers':
            print("⚠️  Symbol mapping testing only available for Fyers")
            return True
        
        try:
            # Test symbol conversion
            test_cases = [
                ("SBIN", "NSE_EQ"),
                ("RELIANCE", "NSE_EQ"),
                ("TCS", "NSE_EQ")
            ]
            
            success_count = 0
            for symbol, exchange in test_cases:
                try:
                    fyers_symbol = to_fyers_symbol(symbol, exchange)
                    if fyers_symbol:
                        is_valid = validate_fyers_symbol(fyers_symbol)
                        if is_valid:
                            print(f"✅ {symbol} → {fyers_symbol}")
                            success_count += 1
                        else:
                            print(f"❌ Invalid symbol format: {fyers_symbol}")
                    else:
                        print(f"⚠️  No mapping found for {symbol}")
                except Exception as e:
                    print(f"❌ Error mapping {symbol}: {e}")
            
            if success_count > 0:
                print(f"✅ Symbol mapping functional ({success_count}/{len(test_cases)} successful)")
                self.test_results['symbol_mapping'] = True
                return True
            else:
                print("❌ Symbol mapping failed")
                return False
                
        except Exception as e:
            print(f"❌ Symbol mapping error: {e}")
            return False
    
    def test_cli_functionality(self):
        """Test CLI functionality."""
        print("\n⚡ Testing CLI Functionality...")
        
        try:
            # Test rapidtrader CLI
            rapidtrader_path = project_root / 'rapidtrader'
            
            if not rapidtrader_path.exists():
                print("❌ RapidTrader CLI not found")
                return False
            
            # Test profile command
            try:
                result = subprocess.run(
                    [str(rapidtrader_path), 'profile', 'test', '--broker', self.broker_name],
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=str(project_root)
                )
                
                if result.returncode == 0:
                    print("✅ CLI profile test successful")
                    self.test_results['cli_functionality'] = True
                else:
                    print(f"❌ CLI profile test failed: {result.stderr}")
                    return False
                    
            except subprocess.TimeoutExpired:
                print("⚠️  CLI test timed out")
                return False
            except Exception as e:
                print(f"❌ CLI test error: {e}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ CLI functionality error: {e}")
            return False
    
    def test_configuration_files(self):
        """Test configuration files."""
        print("\n⚙️  Testing Configuration Files...")
        
        try:
            configs_dir = project_root / 'userdata' / 'configs'
            
            if not configs_dir.exists():
                print("❌ Configs directory not found")
                return False
            
            # Check for Fyers config files
            fyers_config = configs_dir / 'fyers-config.json'
            fyers_live_config = configs_dir / 'fyers-live-config.json'
            
            configs_found = 0
            
            if fyers_config.exists():
                try:
                    with open(fyers_config, 'r') as f:
                        config_data = json.load(f)
                    
                    if config_data.get('broker', {}).get('name') == 'fyers':
                        print("✅ Fyers dry-run config found and valid")
                        configs_found += 1
                    else:
                        print("❌ Fyers dry-run config invalid")
                except Exception as e:
                    print(f"❌ Error reading Fyers dry-run config: {e}")
            
            if fyers_live_config.exists():
                try:
                    with open(fyers_live_config, 'r') as f:
                        config_data = json.load(f)
                    
                    if config_data.get('broker', {}).get('name') == 'fyers':
                        print("✅ Fyers live config found and valid")
                        configs_found += 1
                    else:
                        print("❌ Fyers live config invalid")
                except Exception as e:
                    print(f"❌ Error reading Fyers live config: {e}")
            
            if configs_found > 0:
                print(f"✅ Configuration files functional ({configs_found} configs found)")
                self.test_results['config_files'] = True
                return True
            else:
                print("❌ No valid configuration files found")
                return False
                
        except Exception as e:
            print(f"❌ Configuration files error: {e}")
            return False
    
    def test_docker_compatibility(self):
        """Test Docker compatibility."""
        print("\n🐳 Testing Docker Compatibility...")
        
        try:
            # Check if Docker is available
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
            
            if result.returncode != 0:
                print("⚠️  Docker not available, skipping Docker tests")
                return True
            
            print(f"✅ Docker available: {result.stdout.strip()}")
            
            # Check if docker-compose.yml exists
            docker_compose_path = project_root / 'docker-compose.yml'
            
            if not docker_compose_path.exists():
                print("❌ docker-compose.yml not found")
                return False
            
            print("✅ docker-compose.yml found")
            
            # Check if Dockerfile exists
            dockerfile_path = project_root / 'Dockerfile'
            
            if not dockerfile_path.exists():
                print("❌ Dockerfile not found")
                return False
            
            print("✅ Dockerfile found")
            
            # Test docker-compose config validation
            try:
                result = subprocess.run(
                    ['docker-compose', 'config'],
                    capture_output=True,
                    text=True,
                    cwd=str(project_root)
                )
                
                if result.returncode == 0:
                    print("✅ docker-compose configuration valid")
                    self.test_results['docker_compatibility'] = True
                else:
                    print(f"❌ docker-compose configuration invalid: {result.stderr}")
                    return False
                    
            except Exception as e:
                print(f"❌ Docker compose validation error: {e}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Docker compatibility error: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests and generate report."""
        print(f"\n🧪 Running Complete RapidTrader Test Suite...")
        
        # Run tests
        tests = [
            ('Broker Initialization', self.test_broker_initialization),
            ('Account Access', self.test_account_access),
            ('Symbol Mapping', self.test_symbol_mapping),
            ('CLI Functionality', self.test_cli_functionality),
            ('Configuration Files', self.test_configuration_files),
            ('Docker Compatibility', self.test_docker_compatibility),
        ]
        
        # Add WebSocket test if requested
        if hasattr(self, 'test_websocket') and self.test_websocket:
            tests.insert(3, ('WebSocket Functionality', self.test_websocket_functionality))
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
        
        # Generate report
        self.generate_report(passed_tests, total_tests)
    
    def generate_report(self, passed_tests, total_tests):
        """Generate test report."""
        print(f"\n" + "=" * 60)
        print(f"🎯 RapidTrader Test Report")
        print(f"=" * 60)
        print(f"Broker: {self.broker_name.upper()}")
        print(f"Tests Passed: {passed_tests}/{total_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        print(f"Test Completed: {datetime.now()}")
        
        print(f"\n📊 Detailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name.replace('_', ' ').title()}: {status}")
        
        if passed_tests == total_tests:
            print(f"\n🎉 All tests passed! RapidTrader is fully functional.")
            print(f"\n🚀 Ready for production use:")
            print(f"   • Dry-run trading: ./scripts/rapidtrader-docker.sh fyers-dryrun")
            print(f"   • Live trading: ./scripts/rapidtrader-docker.sh fyers-live")
            print(f"   • Web interface: ./scripts/rapidtrader-docker.sh web")
            print(f"   • Complete setup: ./scripts/rapidtrader-docker.sh fyers-web-trading")
        else:
            print(f"\n⚠️  Some tests failed. Please check the issues above.")
            print(f"   • Check credentials in .env file")
            print(f"   • Ensure broker API is accessible")
            print(f"   • Verify Docker installation")
        
        print(f"=" * 60)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Complete RapidTrader Functionality Test')
    parser.add_argument('--broker', choices=['fyers', 'dhan'], default='fyers',
                       help='Broker to test (default: fyers)')
    parser.add_argument('--test-websocket', action='store_true',
                       help='Include WebSocket functionality tests')
    parser.add_argument('--test-cli', action='store_true',
                       help='Include CLI functionality tests')
    parser.add_argument('--test-docker', action='store_true',
                       help='Include Docker compatibility tests')
    
    args = parser.parse_args()
    
    # Create tester
    tester = RapidTraderTester(broker_name=args.broker)
    
    # Set test flags
    if args.test_websocket:
        tester.test_websocket = True
    
    try:
        # Run all tests
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test suite error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
