#!/usr/bin/env python3
"""
Debug script to test Fyers quote functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from broker.fyers_wrapper import FyersBroker

def test_quotes():
    print("=" * 60)
    print("Testing Fyers Quote Functionality")
    print("=" * 60)
    
    try:
        # Initialize broker
        print("1. Initializing FyersBroker...")
        broker = FyersBroker(dry_run=True)
        print("   ✓ Broker initialized successfully")
        
        # Test get_quotes method
        print("\n2. Testing get_quotes method...")
        symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ"]
        result = broker.get_quotes(symbols)
        print(f"   Raw response: {result}")
        
        # Test get_quote method
        print("\n3. Testing get_quote method...")
        quote_result = broker.get_quote("SBIN", "NSE_EQ")
        print(f"   Quote result: {quote_result}")
        
        # Test with different symbol format
        print("\n4. Testing with NSE:SBIN-EQ format...")
        quote_result2 = broker.get_quote("NSE:SBIN-EQ", "NSE_EQ")
        print(f"   Quote result2: {quote_result2}")
        
    except Exception as e:
        print(f"   Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_quotes()
