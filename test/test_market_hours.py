#!/usr/bin/env python3
"""
Test script to check what data Fyers API returns when market is closed
"""

import sys
import os
from datetime import datetime
import pytz

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from broker.fyers_wrapper import FyersBroker

def check_market_status():
    print("=" * 60)
    print("Market Status and Data Analysis")
    print("=" * 60)
    
    # Check current time in IST
    ist = pytz.timezone('Asia/Kolkata')
    current_time = datetime.now(ist)
    print(f"Current IST Time: {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    
    # Market hours: 9:15 AM to 3:30 PM IST
    market_open = current_time.replace(hour=9, minute=15, second=0, microsecond=0)
    market_close = current_time.replace(hour=15, minute=30, second=0, microsecond=0)
    
    is_market_open = market_open <= current_time <= market_close
    print(f"Market Status: {'🟢 OPEN' if is_market_open else '🔴 CLOSED'}")
    print(f"Market Hours: {market_open.strftime('%H:%M')} - {market_close.strftime('%H:%M')} IST")
    
    if not is_market_open:
        if current_time < market_open:
            print(f"Market opens in: {market_open - current_time}")
        else:
            print(f"Market closed: {current_time - market_close} ago")
    
    print("\n" + "=" * 60)
    print("Testing Fyers API Data")
    print("=" * 60)
    
    try:
        broker = FyersBroker(dry_run=True)
        
        # Test multiple symbols
        symbols = ["NSE:SBIN-EQ", "NSE:RELIANCE-EQ", "NSE:TCS-EQ"]
        
        for symbol in symbols:
            print(f"\n📊 Testing {symbol}:")
            
            # Get raw quotes response
            raw_response = broker.get_quotes([symbol])
            print(f"   Raw API Response: {raw_response}")
            
            if raw_response.get('s') == 'ok' and 'd' in raw_response:
                quote_data = raw_response['d'][0]['v']
                timestamp = quote_data.get('tt', 'N/A')
                
                # Convert timestamp if available
                if timestamp != 'N/A' and timestamp:
                    try:
                        dt = datetime.fromtimestamp(int(timestamp), tz=ist)
                        print(f"   📅 Data Timestamp: {dt.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                        
                        # Check how old the data is
                        age = current_time - dt
                        print(f"   ⏰ Data Age: {age}")
                        
                        if age.total_seconds() > 3600:  # More than 1 hour old
                            print(f"   ⚠️  Data is from market close (stale)")
                        else:
                            print(f"   ✅ Recent data")
                            
                    except:
                        print(f"   ❌ Could not parse timestamp: {timestamp}")
                
                print(f"   💰 LTP: ₹{quote_data.get('lp', 0)}")
                print(f"   📈 Volume: {quote_data.get('volume', 0):,}")
                
            # Test our get_quote method
            quote_result = broker.get_quote(symbol.replace("NSE:", "").replace("-EQ", ""))
            print(f"   🔄 get_quote result: LTP=₹{quote_result.get('ltp', 0)}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_market_status()
