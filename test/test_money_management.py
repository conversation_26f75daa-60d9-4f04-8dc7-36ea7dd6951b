#!/usr/bin/env python3
"""
Money Management Tests

Tests money management functionality including risk management, position sizing, and portfolio allocation.
"""

import os
import sys
import unittest
import tempfile
import shutil
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestMoneyManagerCore(unittest.TestCase):
    """Test core money management functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # Create test config
        self.test_config = {
            "total_capital": 100000,
            "max_risk_per_trade": 0.02,
            "max_portfolio_risk": 0.10,
            "position_sizing": {
                "method": "fixed_percentage",
                "percentage": 0.05
            },
            "risk_management": {
                "stop_loss": 0.05,
                "take_profit": 0.10,
                "max_drawdown": 0.15
            }
        }
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_money_manager_import(self):
        """Test money manager can be imported"""
        try:
            from money_management.core import MoneyManager
            
            # Should be able to import
            self.assertTrue(MoneyManager)
            
        except ImportError as e:
            self.skipTest(f"Money manager not available: {e}")
    
    def test_money_manager_initialization(self):
        """Test money manager initialization"""
        try:
            from money_management.core import MoneyManager
            
            money_manager = MoneyManager(self.test_config)
            self.assertIsNotNone(money_manager)
            
        except ImportError as e:
            self.skipTest(f"Money manager not available: {e}")
    
    def test_risk_manager_import(self):
        """Test risk manager can be imported"""
        try:
            from money_management.risk_manager import RiskManager
            
            # Should be able to import
            self.assertTrue(RiskManager)
            
        except ImportError as e:
            self.skipTest(f"Risk manager not available: {e}")
    
    def test_position_sizer_import(self):
        """Test position sizer can be imported"""
        try:
            from money_management.position_sizer import PositionSizer
            
            # Should be able to import
            self.assertTrue(PositionSizer)
            
        except ImportError as e:
            self.skipTest(f"Position sizer not available: {e}")


class TestRiskManagement(unittest.TestCase):
    """Test risk management functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = {
            "total_capital": 100000,
            "max_risk_per_trade": 0.02,
            "max_portfolio_risk": 0.10,
            "stop_loss": 0.05
        }
    
    def test_risk_manager_initialization(self):
        """Test risk manager initialization"""
        try:
            from money_management.risk_manager import RiskManager
            
            risk_manager = RiskManager(self.test_config)
            self.assertIsNotNone(risk_manager)
            
        except ImportError as e:
            self.skipTest(f"Risk manager not available: {e}")
    
    def test_risk_calculation(self):
        """Test risk calculation"""
        try:
            from money_management.risk_manager import RiskManager
            
            risk_manager = RiskManager(self.test_config)
            
            # Test position risk calculation
            position_value = 10000
            stop_loss_percentage = 0.05
            
            risk = risk_manager.calculate_position_risk(position_value, stop_loss_percentage)
            
            # Risk should be position_value * stop_loss_percentage
            expected_risk = position_value * stop_loss_percentage
            self.assertEqual(risk, expected_risk)
            
        except ImportError as e:
            self.skipTest(f"Risk manager not available: {e}")
    
    def test_risk_validation(self):
        """Test risk validation"""
        try:
            from money_management.risk_manager import RiskManager
            
            risk_manager = RiskManager(self.test_config)
            
            # Test if position is within risk limits
            position_risk = 1000  # 1% of capital
            is_valid = risk_manager.is_position_risk_acceptable(position_risk)
            
            # Should be acceptable (1% < 2% max risk per trade)
            self.assertTrue(is_valid)
            
            # Test excessive risk
            excessive_risk = 3000  # 3% of capital
            is_valid = risk_manager.is_position_risk_acceptable(excessive_risk)
            
            # Should not be acceptable (3% > 2% max risk per trade)
            self.assertFalse(is_valid)
            
        except ImportError as e:
            self.skipTest(f"Risk manager not available: {e}")


class TestPositionSizing(unittest.TestCase):
    """Test position sizing functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = {
            "total_capital": 100000,
            "position_sizing": {
                "method": "fixed_percentage",
                "percentage": 0.05
            }
        }
    
    def test_position_sizer_initialization(self):
        """Test position sizer initialization"""
        try:
            from money_management.position_sizer import PositionSizer
            
            position_sizer = PositionSizer(self.test_config)
            self.assertIsNotNone(position_sizer)
            
        except ImportError as e:
            self.skipTest(f"Position sizer not available: {e}")
    
    def test_fixed_percentage_sizing(self):
        """Test fixed percentage position sizing"""
        try:
            from money_management.position_sizer import PositionSizer
            
            position_sizer = PositionSizer(self.test_config)
            
            # Calculate position size for a stock
            stock_price = 100
            position_size = position_sizer.calculate_position_size(stock_price)
            
            # Expected: 5% of 100000 = 5000, divided by stock price = 50 shares
            expected_shares = (self.test_config["total_capital"] * 0.05) / stock_price
            self.assertEqual(position_size, expected_shares)
            
        except ImportError as e:
            self.skipTest(f"Position sizer not available: {e}")
    
    def test_risk_based_sizing(self):
        """Test risk-based position sizing"""
        try:
            from money_management.position_sizer import PositionSizer
            
            # Configure for risk-based sizing
            risk_config = {
                "total_capital": 100000,
                "max_risk_per_trade": 0.02,
                "position_sizing": {
                    "method": "risk_based"
                }
            }
            
            position_sizer = PositionSizer(risk_config)
            
            # Calculate position size based on risk
            stock_price = 100
            stop_loss_percentage = 0.05
            
            position_size = position_sizer.calculate_risk_based_size(
                stock_price, stop_loss_percentage
            )
            
            # Expected: max_risk (2000) / (stock_price * stop_loss_percentage) = 400 shares
            expected_shares = (risk_config["total_capital"] * 0.02) / (stock_price * stop_loss_percentage)
            self.assertEqual(position_size, expected_shares)
            
        except ImportError as e:
            self.skipTest(f"Position sizer not available: {e}")


class TestAllocationEngine(unittest.TestCase):
    """Test allocation engine functionality"""
    
    def test_allocation_engine_import(self):
        """Test allocation engine can be imported"""
        try:
            from money_management.allocation_engine import AllocationEngine
            
            # Should be able to import
            self.assertTrue(AllocationEngine)
            
        except ImportError as e:
            self.skipTest(f"Allocation engine not available: {e}")
    
    def test_allocation_engine_initialization(self):
        """Test allocation engine initialization"""
        try:
            from money_management.allocation_engine import AllocationEngine
            
            config = {
                "total_capital": 100000,
                "allocation_strategy": "equal_weight"
            }
            
            allocation_engine = AllocationEngine(config)
            self.assertIsNotNone(allocation_engine)
            
        except ImportError as e:
            self.skipTest(f"Allocation engine not available: {e}")


class TestMoneyManagementIntegration(unittest.TestCase):
    """Test money management integration"""
    
    def test_config_manager_import(self):
        """Test config manager can be imported"""
        try:
            from money_management.config_manager import ConfigManager
            
            # Should be able to import
            self.assertTrue(ConfigManager)
            
        except ImportError as e:
            self.skipTest(f"Config manager not available: {e}")
    
    def test_interfaces_import(self):
        """Test interfaces can be imported"""
        try:
            from money_management.interfaces import (
                RiskManagerInterface, PositionSizerInterface
            )
            
            # Should be able to import
            self.assertTrue(RiskManagerInterface)
            self.assertTrue(PositionSizerInterface)
            
        except ImportError as e:
            self.skipTest(f"Money management interfaces not available: {e}")
    
    def test_money_management_cli_integration(self):
        """Test money management CLI integration"""
        try:
            # Check if money management commands are available in CLI
            from core.rapidtrader import cli
            
            # Get command names
            command_names = [cmd.name for cmd in cli.commands.values()]
            
            # Check if money command exists
            if 'money' in command_names:
                # Money management CLI is integrated
                self.assertIn('money', command_names)
            else:
                self.skipTest("Money management CLI not integrated")
                
        except ImportError as e:
            self.skipTest(f"CLI integration not available: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
