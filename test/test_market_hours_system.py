#!/usr/bin/env python3
"""
Test script for the enhanced market hours management system
"""

import sys
import os
from datetime import datetime
import pytz

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.market_hours_manager import MarketHoursManager, MarketStatus

def test_market_hours_manager():
    """Test the market hours manager functionality."""
    print("=" * 60)
    print("Testing Market Hours Manager")
    print("=" * 60)
    
    # Test NSE market hours
    manager = MarketHoursManager("NSE")
    
    # Get current status
    status, details = manager.get_current_status()
    
    print(f"Exchange: NSE")
    print(f"Current Status: {status.value.upper()}")
    print(f"Details: {details}")
    
    # Get market info
    market_info = manager.get_market_info()
    print(f"\nMarket Info:")
    for key, value in market_info.items():
        print(f"  {key}: {value}")
    
    # Test different exchanges
    print("\n" + "=" * 60)
    print("Testing Different Exchanges")
    print("=" * 60)
    
    exchanges = ["NSE", "NYSE", "NASDAQ"]
    
    for exchange in exchanges:
        try:
            mgr = MarketHoursManager(exchange)
            status, details = mgr.get_current_status()
            print(f"\n{exchange}:")
            print(f"  Status: {status.value.upper()}")
            if details.get('next_open'):
                print(f"  Next Open: {details['next_open']}")
        except Exception as e:
            print(f"\n{exchange}: Error - {e}")

def test_dry_run_with_market_hours():
    """Test dry-run with market hours integration."""
    print("\n" + "=" * 60)
    print("Testing Dry-Run with Market Hours")
    print("=" * 60)
    
    # This would normally start the dry-run engine
    # For testing, we'll just show what would happen
    
    manager = MarketHoursManager("NSE")
    status, details = manager.get_current_status()
    
    print(f"Current Market Status: {status.value.upper()}")
    
    if status == MarketStatus.OPEN:
        print("✅ Market is open - Trading would proceed normally")
    else:
        print("🔴 Market is closed - User would be prompted:")
        print("   1. Continue with last traded prices (simulated trading)")
        print("   2. Stop and wait for market to open")
        
        if details.get('next_open'):
            print(f"   Next market open: {details['next_open']}")

def simulate_user_interaction():
    """Simulate the user interaction for after-hours trading."""
    print("\n" + "=" * 60)
    print("Simulating User Interaction")
    print("=" * 60)
    
    manager = MarketHoursManager("NSE")
    status, details = manager.get_current_status()
    
    if status != MarketStatus.OPEN:
        print("🔴 MARKET IS CURRENTLY CLOSED")
        print("="*60)
        print(f"Market Status: {status.value.upper()}")
        
        if details.get('next_open'):
            print(f"Next Market Open: {details['next_open']}")
        
        print("\nOptions:")
        print("1. Continue with last traded prices (simulated trading)")
        print("2. Stop and wait for market to open")
        
        print("\n📝 In actual usage, user would be prompted to choose...")
        print("   For this test, we'll simulate choosing option 1")
        
        return True  # Simulate user choosing to continue
    else:
        print("✅ Market is open - No user interaction needed")
        return True

if __name__ == "__main__":
    test_market_hours_manager()
    test_dry_run_with_market_hours()
    simulate_user_interaction()
    
    print("\n" + "=" * 60)
    print("✅ Market Hours System Test Complete")
    print("=" * 60)
