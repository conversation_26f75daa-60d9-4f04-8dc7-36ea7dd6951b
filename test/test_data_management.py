#!/usr/bin/env python3
"""
Data Management Tests

Tests data management functionality including data fetchers, data managers, and market data services.
"""

import os
import sys
import unittest
import tempfile
import shutil
import pandas as pd
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestDataManager(unittest.TestCase):
    """Test core data manager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir)
    
    def test_core_data_manager_import(self):
        """Test core data manager can be imported"""
        try:
            from core.data_manager import DataManager
            
            # Should be able to import
            self.assertTrue(DataManager)
            
        except ImportError as e:
            self.skipTest(f"Core data manager not available: {e}")
    
    def test_data_module_data_manager_import(self):
        """Test data module data manager can be imported"""
        try:
            from data.data_manager import DataManager
            
            # Should be able to import
            self.assertTrue(DataManager)
            
        except ImportError as e:
            self.skipTest(f"Data module data manager not available: {e}")
    
    def test_data_manager_initialization(self):
        """Test data manager initialization"""
        try:
            from data.data_manager import DataManager
            
            data_manager = DataManager()
            self.assertIsNotNone(data_manager)
            
        except ImportError as e:
            self.skipTest(f"Data manager not available: {e}")


class TestDataFetchers(unittest.TestCase):
    """Test data fetcher functionality"""
    
    def test_base_fetcher_import(self):
        """Test base fetcher can be imported"""
        try:
            from data.base_fetcher import BaseFetcher
            
            # Should be able to import
            self.assertTrue(BaseFetcher)
            
        except ImportError as e:
            self.skipTest(f"Base fetcher not available: {e}")
    
    def test_yfinance_fetcher_import(self):
        """Test yfinance fetcher can be imported"""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            # Should be able to import
            self.assertTrue(YFinanceFetcher)
            
        except ImportError as e:
            self.skipTest(f"YFinance fetcher not available: {e}")
    
    def test_yfinance_fetcher_initialization(self):
        """Test yfinance fetcher initialization"""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            fetcher = YFinanceFetcher()
            self.assertIsNotNone(fetcher)
            
            # Check if it has required methods
            self.assertTrue(hasattr(fetcher, 'fetch_data'))
            
        except ImportError as e:
            self.skipTest(f"YFinance fetcher not available: {e}")
    
    def test_fetcher_module_import(self):
        """Test fetcher module can be imported"""
        try:
            from data.fetcher import Fetcher
            
            # Should be able to import
            self.assertTrue(Fetcher)
            
        except ImportError as e:
            self.skipTest(f"Fetcher module not available: {e}")


class TestMarketDataService(unittest.TestCase):
    """Test market data service functionality"""
    
    def test_market_data_service_import(self):
        """Test market data service can be imported"""
        try:
            from data.market_data_service import MarketDataService
            
            # Should be able to import
            self.assertTrue(MarketDataService)
            
        except ImportError as e:
            self.skipTest(f"Market data service not available: {e}")
    
    def test_market_data_service_initialization(self):
        """Test market data service initialization"""
        try:
            from data.market_data_service import MarketDataService
            
            service = MarketDataService()
            self.assertIsNotNone(service)
            
        except ImportError as e:
            self.skipTest(f"Market data service not available: {e}")


class TestDataOptimization(unittest.TestCase):
    """Test data optimization functionality"""
    
    def test_data_optimizer_import(self):
        """Test data optimizer can be imported"""
        try:
            from data.data_optimizer import DataOptimizer
            
            # Should be able to import
            self.assertTrue(DataOptimizer)
            
        except ImportError as e:
            self.skipTest(f"Data optimizer not available: {e}")
    
    def test_data_cleanup_import(self):
        """Test data cleanup can be imported"""
        try:
            from data.data_cleanup import DataCleanup
            
            # Should be able to import
            self.assertTrue(DataCleanup)
            
        except ImportError as e:
            self.skipTest(f"Data cleanup not available: {e}")


class TestSymbolManagement(unittest.TestCase):
    """Test symbol management functionality"""
    
    def test_group_manager_import(self):
        """Test group manager can be imported"""
        try:
            from data.group_manager import GroupManager
            
            # Should be able to import
            self.assertTrue(GroupManager)
            
        except ImportError as e:
            self.skipTest(f"Group manager not available: {e}")
    
    def test_symbol_files_exist(self):
        """Test symbol files exist"""
        symbols_dir = project_root / "data" / "symbols"
        
        if symbols_dir.exists():
            # Check for common symbol files
            expected_files = [
                "nse_symbols.json",
                "bse_symbols.json",
                "all_symbols.json",
                "groups.json"
            ]
            
            for file_name in expected_files:
                file_path = symbols_dir / file_name
                if file_path.exists():
                    self.assertTrue(file_path.exists())
                    
                    # If it's a JSON file, try to load it
                    if file_name.endswith('.json'):
                        try:
                            import json
                            with open(file_path, 'r') as f:
                                data = json.load(f)
                            self.assertIsInstance(data, (dict, list))
                        except json.JSONDecodeError:
                            self.fail(f"Invalid JSON in {file_name}")
        else:
            self.skipTest("Symbols directory not found")


class TestDataDownload(unittest.TestCase):
    """Test data download functionality"""
    
    def test_download_data_script_exists(self):
        """Test download data script exists"""
        script_path = project_root / "data" / "download_data.py"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's a valid Python file
            try:
                with open(script_path, 'r') as f:
                    content = f.read()
                
                # Should contain some expected content
                self.assertIn("def", content)  # Should have function definitions
                
            except Exception as e:
                self.fail(f"Error reading download_data.py: {e}")
        else:
            self.skipTest("Download data script not found")
    
    def test_nse_symbols_script_exists(self):
        """Test NSE symbols script exists"""
        script_path = project_root / "data" / "symbols" / "nse_symbols.py"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
        else:
            self.skipTest("NSE symbols script not found")


class TestDataIntegration(unittest.TestCase):
    """Test data integration functionality"""
    
    def test_data_module_init(self):
        """Test data module __init__.py"""
        try:
            import data
            
            # Should be able to import data module
            self.assertIsNotNone(data)
            
        except ImportError as e:
            self.skipTest(f"Data module not available: {e}")
    
    def test_data_manager_in_core(self):
        """Test data manager integration in core"""
        try:
            from core.data_manager import DataManager
            
            # Should be able to import from core
            self.assertTrue(DataManager)
            
        except ImportError as e:
            self.skipTest(f"Core data manager not available: {e}")
    
    def test_data_fetcher_functionality(self):
        """Test basic data fetcher functionality"""
        try:
            from data.yfinance_fetcher import YFinanceFetcher
            
            fetcher = YFinanceFetcher()
            
            # Test basic functionality without actual network calls
            self.assertTrue(hasattr(fetcher, 'fetch_data'))
            self.assertTrue(callable(getattr(fetcher, 'fetch_data')))
            
        except ImportError as e:
            self.skipTest(f"Data fetcher functionality not available: {e}")


if __name__ == '__main__':
    unittest.main(verbosity=2)
