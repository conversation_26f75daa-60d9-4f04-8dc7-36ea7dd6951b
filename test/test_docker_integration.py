#!/usr/bin/env python3
"""
Docker Integration Tests

Tests Docker integration functionality including container management, scripts, and deployment.
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class TestDockerFiles(unittest.TestCase):
    """Test Docker configuration files"""
    
    def test_dockerfile_exists(self):
        """Test main Dockerfile exists"""
        dockerfile_path = project_root / "Dockerfile"
        
        if dockerfile_path.exists():
            self.assertTrue(dockerfile_path.exists())
            
            # Check if it's a valid Dockerfile
            with open(dockerfile_path, 'r') as f:
                content = f.read()
            
            # Should contain Docker instructions
            self.assertIn("FROM", content)
            
        else:
            self.skipTest("Main Dockerfile not found")
    
    def test_alpine_dockerfile_exists(self):
        """Test Alpine Dockerfile exists"""
        dockerfile_path = project_root / "Dockerfile.alpine"
        
        if dockerfile_path.exists():
            self.assertTrue(dockerfile_path.exists())
            
            # Check if it's a valid Dockerfile
            with open(dockerfile_path, 'r') as f:
                content = f.read()
            
            # Should contain Docker instructions and Alpine
            self.assertIn("FROM", content)
            self.assertIn("alpine", content.lower())
            
        else:
            self.skipTest("Alpine Dockerfile not found")
    
    def test_docker_compose_files_exist(self):
        """Test Docker Compose files exist"""
        compose_files = [
            "docker-compose.yml",
            "docker-compose.api.yml",
            "docker-compose.production.yml",
            "docker-compose.test.yml"
        ]
        
        for compose_file in compose_files:
            compose_path = project_root / compose_file
            
            if compose_path.exists():
                self.assertTrue(compose_path.exists())
                
                # Check if it's a valid YAML file
                try:
                    import yaml
                    with open(compose_path, 'r') as f:
                        data = yaml.safe_load(f)
                    
                    # Should have services section
                    self.assertIn("services", data)
                    
                except ImportError:
                    # YAML not available, just check file exists
                    pass
                except yaml.YAMLError:
                    self.fail(f"Invalid YAML in {compose_file}")


class TestDockerScripts(unittest.TestCase):
    """Test Docker management scripts"""
    
    def test_docker_run_script_exists(self):
        """Test docker-run.sh script exists"""
        script_path = project_root / "scripts" / "docker-run.sh"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's executable
            self.assertTrue(os.access(script_path, os.X_OK))
            
            # Check if it's a shell script
            with open(script_path, 'r') as f:
                first_line = f.readline()
            
            self.assertTrue(first_line.startswith("#!/"))
            
        else:
            self.skipTest("docker-run.sh script not found")
    
    def test_rapidtrader_docker_script_exists(self):
        """Test rapidtrader-docker.sh script exists"""
        script_path = project_root / "scripts" / "rapidtrader-docker.sh"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's executable
            self.assertTrue(os.access(script_path, os.X_OK))
            
        else:
            self.skipTest("rapidtrader-docker.sh script not found")
    
    def test_rapidtrader_api_script_exists(self):
        """Test rapidtrader-api.sh script exists"""
        script_path = project_root / "scripts" / "rapidtrader-api.sh"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's executable
            self.assertTrue(os.access(script_path, os.X_OK))
            
        else:
            self.skipTest("rapidtrader-api.sh script not found")
    
    def test_entrypoint_script_exists(self):
        """Test entrypoint.sh script exists"""
        script_path = project_root / "scripts" / "entrypoint.sh"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's executable
            self.assertTrue(os.access(script_path, os.X_OK))
            
        else:
            self.skipTest("entrypoint.sh script not found")


class TestDockerConfiguration(unittest.TestCase):
    """Test Docker configuration"""
    
    def test_api_gateway_dockerfile_exists(self):
        """Test API Gateway Dockerfile exists"""
        dockerfile_path = project_root / "api_gateway" / "Dockerfile"
        
        if dockerfile_path.exists():
            self.assertTrue(dockerfile_path.exists())
            
            # Check if it's a valid Dockerfile
            with open(dockerfile_path, 'r') as f:
                content = f.read()
            
            self.assertIn("FROM", content)
            
        else:
            self.skipTest("API Gateway Dockerfile not found")
    
    def test_frontend_dockerfile_exists(self):
        """Test Frontend Dockerfile exists"""
        dockerfile_paths = [
            project_root / "frontend" / "Dockerfile",
            project_root / "frontend_v2" / "Dockerfile"
        ]
        
        for dockerfile_path in dockerfile_paths:
            if dockerfile_path.exists():
                self.assertTrue(dockerfile_path.exists())
                
                # Check if it's a valid Dockerfile
                with open(dockerfile_path, 'r') as f:
                    content = f.read()
                
                self.assertIn("FROM", content)
                break
        else:
            self.skipTest("Frontend Dockerfile not found")
    
    def test_nginx_configuration_exists(self):
        """Test Nginx configuration exists"""
        nginx_configs = [
            project_root / "nginx" / "nginx.conf",
            project_root / "frontend" / "nginx.conf",
            project_root / "frontend_v2" / "nginx.conf"
        ]
        
        for nginx_config in nginx_configs:
            if nginx_config.exists():
                self.assertTrue(nginx_config.exists())
                
                # Check if it's a valid nginx config
                with open(nginx_config, 'r') as f:
                    content = f.read()
                
                # Should contain nginx directives
                self.assertTrue(
                    "server" in content or 
                    "location" in content or 
                    "upstream" in content
                )
                break
        else:
            self.skipTest("Nginx configuration not found")


class TestDockerIntegration(unittest.TestCase):
    """Test Docker integration functionality"""
    
    @patch('docker.from_env')
    def test_docker_client_import(self, mock_docker):
        """Test Docker client can be imported and used"""
        try:
            import docker
            
            # Mock Docker client
            mock_client = Mock()
            mock_docker.return_value = mock_client
            
            # Should be able to create client
            client = docker.from_env()
            self.assertIsNotNone(client)
            
        except ImportError as e:
            self.skipTest(f"Docker library not available: {e}")
    
    def test_docker_market_manager_exists(self):
        """Test Docker market manager script exists"""
        script_path = project_root / "scripts" / "docker_market_manager.py"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's a valid Python file
            try:
                with open(script_path, 'r') as f:
                    content = f.read()
                
                # Should contain Python code
                self.assertIn("def", content)
                
            except Exception as e:
                self.fail(f"Error reading docker_market_manager.py: {e}")
        else:
            self.skipTest("Docker market manager script not found")
    
    def test_test_docker_setup_script_exists(self):
        """Test test-docker-setup.sh script exists"""
        script_path = project_root / "scripts" / "test-docker-setup.sh"
        
        if script_path.exists():
            self.assertTrue(script_path.exists())
            
            # Check if it's executable
            self.assertTrue(os.access(script_path, os.X_OK))
            
        else:
            self.skipTest("test-docker-setup.sh script not found")


class TestLoggingConfiguration(unittest.TestCase):
    """Test logging configuration for Docker"""
    
    def test_logging_directory_exists(self):
        """Test logging directory exists"""
        logging_dir = project_root / "logging"
        
        if logging_dir.exists():
            self.assertTrue(logging_dir.exists())
            self.assertTrue(logging_dir.is_dir())
        else:
            self.skipTest("Logging directory not found")
    
    def test_logrotate_config_exists(self):
        """Test logrotate configuration exists"""
        config_path = project_root / "logging" / "logrotate.conf"
        
        if config_path.exists():
            self.assertTrue(config_path.exists())
            
            # Check if it's a valid logrotate config
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Should contain logrotate directives
            self.assertTrue(
                "daily" in content or 
                "weekly" in content or 
                "rotate" in content
            )
        else:
            self.skipTest("Logrotate configuration not found")
    
    def test_loki_config_exists(self):
        """Test Loki configuration exists"""
        config_path = project_root / "logging" / "loki-config.yml"
        
        if config_path.exists():
            self.assertTrue(config_path.exists())
            
            # Check if it's a valid YAML file
            try:
                import yaml
                with open(config_path, 'r') as f:
                    data = yaml.safe_load(f)
                
                # Should be a valid YAML
                self.assertIsInstance(data, dict)
                
            except ImportError:
                # YAML not available, just check file exists
                pass
            except yaml.YAMLError:
                self.fail("Invalid YAML in loki-config.yml")
        else:
            self.skipTest("Loki configuration not found")
    
    def test_promtail_config_exists(self):
        """Test Promtail configuration exists"""
        config_path = project_root / "logging" / "promtail-config.yml"
        
        if config_path.exists():
            self.assertTrue(config_path.exists())
            
            # Check if it's a valid YAML file
            try:
                import yaml
                with open(config_path, 'r') as f:
                    data = yaml.safe_load(f)
                
                # Should be a valid YAML
                self.assertIsInstance(data, dict)
                
            except ImportError:
                # YAML not available, just check file exists
                pass
            except yaml.YAMLError:
                self.fail("Invalid YAML in promtail-config.yml")
        else:
            self.skipTest("Promtail configuration not found")


if __name__ == '__main__':
    unittest.main(verbosity=2)
