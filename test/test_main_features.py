#!/usr/bin/env python3
"""
RapidTrader Main Features Test
Test all main features after directory cleanup
"""

import sys
import os
import traceback
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all main modules can be imported"""
    print("🧪 Testing Module Imports...")
    
    tests = [
        ("Core - Backtest Engine", "from core.backtest_engine import BacktestEngine"),
        ("Core - Trade Simulator", "from core.trade_simulator import TradeSimulator"),
        ("Core - Data Manager", "from core.data_manager import DataManager"),
        ("Data - Fetcher", "from data.fetcher import Fetcher"),
        ("Data - YFinance Fetcher", "from data.yfinance_fetcher import YFinanceFetcher"),
        ("Broker - Fyers", "from broker.fyers_wrapper import FyersBroker"),
        ("Broker - Dhan", "from broker.dhan_wrapper import DhanBroker"),
        ("Money Management", "from money_management.core import MoneyManager"),
        ("AI Module - Prediction Engine", "from ai_module.prediction_engine import PredictionEngine"),
        ("Security - Secrets Manager", "from security.secrets_manager import SecretsManager"),
        ("Telemetry - Logger", "from telemetry.logger import Logger"),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, import_statement in tests:
        try:
            exec(import_statement)
            print(f"  ✅ {test_name}")
            passed += 1
        except Exception as e:
            print(f"  ❌ {test_name}: {e}")
            failed += 1
    
    print(f"\n📊 Import Results: {passed} passed, {failed} failed")
    return failed == 0

def test_cli_commands():
    """Test CLI command availability"""
    print("\n🖥️  Testing CLI Commands...")
    
    import subprocess
    
    commands = [
        ("Main CLI", ["python", "-m", "core.rapidtrader", "--help"]),
        ("Data Commands", ["python", "-m", "core.rapidtrader", "data", "--help"]),
        ("Strategy Commands", ["python", "-m", "core.rapidtrader", "strategy", "--help"]),
        ("Backtest Commands", ["python", "-m", "core.rapidtrader", "backtest", "--help"]),
        ("Money Management", ["python", "-m", "core.rapidtrader", "money", "--help"]),
        ("Trading Commands", ["python", "-m", "core.rapidtrader", "trade", "--help"]),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, command in commands:
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"  ✅ {test_name}")
                passed += 1
            else:
                print(f"  ❌ {test_name}: Exit code {result.returncode}")
                failed += 1
        except Exception as e:
            print(f"  ❌ {test_name}: {e}")
            failed += 1
    
    print(f"\n📊 CLI Results: {passed} passed, {failed} failed")
    return failed == 0

def test_directory_structure():
    """Test if directory structure is clean and organized"""
    print("\n📁 Testing Directory Structure...")
    
    required_dirs = [
        "broker", "core", "data", "docs", "test", "logs", 
        "money_management", "ai_module", "security", "telemetry",
        "userdata", "scripts", "examples"
    ]
    
    required_files = [
        "README.md", "requirements.txt", "docker-compose.yml", "Dockerfile"
    ]
    
    # Check for unwanted files in root
    unwanted_patterns = [
        "test_*.py",  # Should be in test/
        "*.log",      # Should be in logs/
        "brocker"     # Misspelled directory
    ]
    
    passed = 0
    failed = 0
    
    # Check required directories
    for dir_name in required_dirs:
        if Path(dir_name).exists() and Path(dir_name).is_dir():
            print(f"  ✅ Directory: {dir_name}/")
            passed += 1
        else:
            print(f"  ❌ Missing directory: {dir_name}/")
            failed += 1
    
    # Check required files
    for file_name in required_files:
        if Path(file_name).exists() and Path(file_name).is_file():
            print(f"  ✅ File: {file_name}")
            passed += 1
        else:
            print(f"  ❌ Missing file: {file_name}")
            failed += 1
    
    # Check for unwanted files
    root_files = list(Path(".").glob("*"))
    for pattern in unwanted_patterns:
        matches = list(Path(".").glob(pattern))
        if matches:
            for match in matches:
                print(f"  ⚠️  Unwanted in root: {match}")
                failed += 1
        else:
            print(f"  ✅ Clean: No {pattern} in root")
            passed += 1
    
    print(f"\n📊 Structure Results: {passed} passed, {failed} failed")
    return failed == 0

def test_configuration_files():
    """Test if configuration files exist and are valid"""
    print("\n⚙️  Testing Configuration Files...")
    
    config_files = [
        "userdata/config/backtest-config.json",
        "userdata/config/dryrun-config.json", 
        "userdata/config/live-config.json",
        "userdata/configs/fyers-config.json",
        "config/config.yml"
    ]
    
    passed = 0
    failed = 0
    
    for config_file in config_files:
        if Path(config_file).exists():
            try:
                # Try to read the file
                with open(config_file, 'r') as f:
                    content = f.read()
                    if content.strip():
                        print(f"  ✅ Config: {config_file}")
                        passed += 1
                    else:
                        print(f"  ⚠️  Empty: {config_file}")
                        failed += 1
            except Exception as e:
                print(f"  ❌ Invalid: {config_file} - {e}")
                failed += 1
        else:
            print(f"  ⚠️  Missing: {config_file}")
            # Don't count as failed since some configs are optional
    
    print(f"\n📊 Config Results: {passed} checked, {failed} issues")
    return failed == 0

def main():
    """Run all tests"""
    print("🚀 RapidTrader Main Features Test")
    print("=" * 50)
    
    all_passed = True
    
    # Run all test suites
    all_passed &= test_directory_structure()
    all_passed &= test_imports()
    all_passed &= test_cli_commands()
    all_passed &= test_configuration_files()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All main features are working correctly!")
        print("✨ RapidTrader is ready for use!")
    else:
        print("⚠️  Some issues found, but core functionality should work")
        print("💡 Check the details above for specific issues")
    
    print("\n🔗 Next Steps:")
    print("  • Test with: python -m core.rapidtrader --help")
    print("  • Create config: python -m core.rapidtrader create-config")
    print("  • Run backtest: python -m core.rapidtrader backtest run")
    print("  • Start dry-run: python -m core.rapidtrader trade dryrun")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
