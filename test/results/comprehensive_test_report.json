{"timestamp": "2025-05-30T22:08:47.375264", "summary": {"total_modules": 7, "total_tests": 99, "total_passed": 81, "total_failed": 7, "total_errors": 11, "total_skipped": 16, "overall_success_rate": 81.81818181818183}, "modules": {"test_core_initialization": {"module": "test_core_initialization", "tests_run": 8, "failures": 1, "errors": 0, "skipped": 0, "success_rate": 87.5, "details": {"failures": [{"test": "test_invalid_config_handling (test_core_initialization.TestConfigurationManagement.test_invalid_config_handling)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_core_initialization.py\", line 169, in test_invalid_config_handling\n    self.assertEqual(result, {})\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^\nAssertionError: {'invalid json': None} != {}\n- {'invalid json': None}\n+ {}\n"}], "errors": [], "skipped": []}, "output": "Ran 8 tests in 1.313s\n\nFAILED (failures=1)\n"}, "test_broker_integrations": {"module": "test_broker_integrations", "tests_run": 15, "failures": 2, "errors": 0, "skipped": 2, "success_rate": 86.66666666666667, "details": {"failures": [{"test": "test_dhan_constants (test_broker_integrations.TestBrokerConstants.test_dhan_constants)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_broker_integrations.py\", line 174, in test_dhan_constants\n    self.assertTrue(hasattr(ProductType, 'MIS'))\n    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: False is not true\n"}, {"test": "test_fyers_constants (test_broker_integrations.TestBrokerConstants.test_fyers_constants)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_broker_integrations.py\", line 157, in test_fyers_constants\n    self.assertTrue(hasattr(FyersProductType, 'MIS'))\n    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: False is not true\n"}], "errors": [], "skipped": [{"test": "test_fyers_broker_initialization (test_broker_integrations.TestFyersBrokerIntegration.test_fyers_broker_initialization)", "reason": "Fyers broker not available: fyers-apiv3 package is required. Install with: pip install fyers-apiv3"}, {"test": "test_fyers_websocket_client (test_broker_integrations.TestFyersBrokerIntegration.test_fyers_websocket_client)", "reason": "Fyers WebSocket client not available: fyers-apiv3 package with WebSocket support is required"}]}, "output": "Ran 15 tests in 0.015s\n\nFAILED (failures=2, skipped=2)\n"}, "test_backtesting_engine": {"module": "test_backtesting_engine", "tests_run": 13, "failures": 0, "errors": 6, "skipped": 0, "success_rate": 53.84615384615385, "details": {"failures": [], "errors": [{"test": "test_backtest_engine_initialization (test_backtesting_engine.TestBacktestEngine.test_backtest_engine_initialization)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_backtesting_engine.py\", line 51, in test_backtest_engine_initialization\n    engine = BacktestEngine(\n        starting_balance=100000,\n        commission=0.001,\n        slippage=0.0001\n    )\nTypeError: BacktestEngine.__init__() got an unexpected keyword argument 'starting_balance'\n"}, {"test": "test_optimized_backtest_engine (test_backtesting_engine.TestBacktestEngine.test_optimized_backtest_engine)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_backtesting_engine.py\", line 71, in test_optimized_backtest_engine\n    engine = OptimizedBacktestEngine()\nTypeError: OptimizedBacktestEngine.__init__() missing 1 required positional argument: 'config'\n"}, {"test": "test_trade_recording (test_backtesting_engine.TestBacktestResults.test_trade_recording)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_backtesting_engine.py\", line 224, in test_trade_recording\n    results.add_trade(trade)\n    ^^^^^^^^^^^^^^^^^\nAttributeError: 'BacktestResults' object has no attribute 'add_trade'\n"}, {"test": "test_data_manager_initialization (test_backtesting_engine.TestDataManager.test_data_manager_initialization)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_backtesting_engine.py\", line 259, in test_data_manager_initialization\n    data_manager = DataManager()\nTypeError: DataManager.__init__() missing 1 required positional argument: 'config'\n"}, {"test": "test_trade_execution (test_backtesting_engine.TestTradeSimulator.test_trade_execution)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_backtesting_engine.py\", line 138, in test_trade_execution\n    simulator = TradeSimulator(initial_balance=100000)\nTypeError: TradeSimulator.__init__() got an unexpected keyword argument 'initial_balance'\n"}, {"test": "test_trade_simulator_initialization (test_backtesting_engine.TestTradeSimulator.test_trade_simulator_initialization)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_backtesting_engine.py\", line 97, in test_trade_simulator_initialization\n    simulator = TradeSimulator(\n        initial_balance=100000,\n        commission=0.001,\n        slippage=0.0001\n    )\nTypeError: TradeSimulator.__init__() got an unexpected keyword argument 'initial_balance'\n"}], "skipped": []}, "output": "Ran 13 tests in 0.022s\n\nFAILED (errors=6)\n"}, "test_api_gateway_core": {"module": "test_api_gateway_core", "tests_run": 13, "failures": 1, "errors": 1, "skipped": 9, "success_rate": 84.61538461538461, "details": {"failures": [{"test": "test_broker_registration (test_api_gateway_core.TestBrokerInterface.test_broker_registration)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_api_gateway_core.py\", line 246, in test_broker_registration\n    self.assertTrue(success)\n    ~~~~~~~~~~~~~~~^^^^^^^^^\nAssertionError: False is not true\n"}], "errors": [{"test": "test_api_gateway_initialization (test_api_gateway_core.TestAPIGatewayCore.test_api_gateway_initialization)", "error": "Traceback (most recent call last):\n  File \"/usr/lib/python3.13/unittest/mock.py\", line 1421, in patched\n    with self.decoration_helper(patched,\n         ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^\n                                args,\n                                ^^^^^\n                                keywargs) as (newargs, newkeywargs):\n                                ^^^^^^^^^\n  File \"/usr/lib/python3.13/contextlib.py\", line 141, in __enter__\n    return next(self.gen)\n  File \"/usr/lib/python3.13/unittest/mock.py\", line 1403, in decoration_helper\n    arg = exit_stack.enter_context(patching)\n  File \"/usr/lib/python3.13/contextlib.py\", line 530, in enter_context\n    result = _enter(cm)\n  File \"/usr/lib/python3.13/unittest/mock.py\", line 1479, in __enter__\n    self.target = self.getter()\n                  ~~~~~~~~~~~^^\n  File \"/usr/lib/python3.13/pkgutil.py\", line 528, in resolve_name\n    result = getattr(result, p)\nAttributeError: module 'api_gateway' has no attribute 'auth'\n"}], "skipped": [{"test": "test_api_gateway_import (test_api_gateway_core.TestAPIGatewayCore.test_api_gateway_import)", "reason": "API Gateway not available: No module named 'passlib'"}, {"test": "test_pydantic_models (test_api_gateway_core.TestAPIGatewayCore.test_pydantic_models)", "reason": "API Gateway models not available: No module named 'passlib'"}, {"test": "test_api_key_generation (test_api_gateway_core.TestAuthenticationSystem.test_api_key_generation)", "reason": "Authentication manager not available: No module named 'passlib'"}, {"test": "test_api_key_validation (test_api_gateway_core.TestAuthenticationSystem.test_api_key_validation)", "reason": "Authentication manager not available: No module named 'passlib'"}, {"test": "test_auth_manager_import (test_api_gateway_core.TestAuthenticationSystem.test_auth_manager_import)", "reason": "Authentication manager not available: No module named 'passlib'"}, {"test": "test_auth_manager_initialization (test_api_gateway_core.TestAuthenticationSystem.test_auth_manager_initialization)", "reason": "Authentication manager not available: No module named 'passlib'"}, {"test": "test_rate_limiter (test_api_gateway_core.TestAuthenticationSystem.test_rate_limiter)", "reason": "Rate limiter not available: No module named 'passlib'"}, {"test": "test_unified_api_import (test_api_gateway_core.TestUnifiedAPI.test_unified_api_import)", "reason": "Unified API not available: cannot import name 'UnifiedAPI' from 'api_gateway.unified_api' (/home/<USER>/rapidtrader/api_gateway/unified_api.py)"}, {"test": "test_unified_api_initialization (test_api_gateway_core.TestUnifiedAPI.test_unified_api_initialization)", "reason": "Unified API not available: cannot import name 'UnifiedAPI' from 'api_gateway.unified_api' (/home/<USER>/rapidtrader/api_gateway/unified_api.py)"}]}, "output": "Ran 13 tests in 0.581s\n\nFAILED (failures=1, errors=1, skipped=9)\n"}, "test_money_management": {"module": "test_money_management", "tests_run": 15, "failures": 1, "errors": 3, "skipped": 1, "success_rate": 73.33333333333333, "details": {"failures": [{"test": "test_risk_calculation (test_money_management.TestRiskManagement.test_risk_calculation)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_money_management.py\", line 133, in test_risk_calculation\n    self.assertEqual(risk, expected_risk)\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^\nAssertionError: {'position_value': 10000, 'max_loss': 500[76 chars]00.0} != 500.0\n"}], "errors": [{"test": "test_fixed_percentage_sizing (test_money_management.TestPositionSizing.test_fixed_percentage_sizing)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_money_management.py\", line 196, in test_fixed_percentage_sizing\n    position_size = position_sizer.calculate_position_size(stock_price)\nTypeError: PositionSizer.calculate_position_size() missing 3 required positional arguments: 'symbol', 'price', and 'strategy_allocation'\n"}, {"test": "test_risk_based_sizing (test_money_management.TestPositionSizing.test_risk_based_sizing)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_money_management.py\", line 225, in test_risk_based_sizing\n    position_size = position_sizer.calculate_risk_based_size(\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'PositionSizer' object has no attribute 'calculate_risk_based_size'\n"}, {"test": "test_risk_validation (test_money_management.TestRiskManagement.test_risk_validation)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_money_management.py\", line 147, in test_risk_validation\n    is_valid = risk_manager.is_position_risk_acceptable(position_risk)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'RiskManager' object has no attribute 'is_position_risk_acceptable'\n"}], "skipped": [{"test": "test_interfaces_import (test_money_management.TestMoneyManagementIntegration.test_interfaces_import)", "reason": "Money management interfaces not available: cannot import name 'RiskManagerInterface' from 'money_management.interfaces' (/home/<USER>/rapidtrader/money_management/interfaces.py)"}]}, "output": "Ran 15 tests in 0.020s\n\nFAILED (failures=1, errors=3, skipped=1)\n"}, "test_data_management": {"module": "test_data_management", "tests_run": 18, "failures": 2, "errors": 1, "skipped": 4, "success_rate": 83.33333333333334, "details": {"failures": [{"test": "test_yfinance_fetcher_initialization (test_data_management.TestDataFetchers.test_yfinance_fetcher_initialization)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_management.py\", line 102, in test_yfinance_fetcher_initialization\n    self.assertTrue(hasattr(fetcher, 'fetch_data'))\n    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: False is not true\n"}, {"test": "test_data_fetcher_functionality (test_data_management.TestDataIntegration.test_data_fetcher_functionality)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_management.py\", line 282, in test_data_fetcher_functionality\n    self.assertTrue(hasattr(fetcher, 'fetch_data'))\n    ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: False is not true\n"}], "errors": [{"test": "test_data_manager_initialization (test_data_management.TestDataManager.test_data_manager_initialization)", "error": "Traceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_management.py\", line 61, in test_data_manager_initialization\n    data_manager = DataManager()\nTypeError: DataManager.__init__() missing 1 required positional argument: 'data_fetcher'\n"}], "skipped": [{"test": "test_base_fetcher_import (test_data_management.TestDataFetchers.test_base_fetcher_import)", "reason": "Base fetcher not available: cannot import name 'BaseFetcher' from 'data.base_fetcher' (/home/<USER>/rapidtrader/data/base_fetcher.py)"}, {"test": "test_data_cleanup_import (test_data_management.TestDataOptimization.test_data_cleanup_import)", "reason": "Data cleanup not available: cannot import name 'DataCleanup' from 'data.data_cleanup' (/home/<USER>/rapidtrader/data/data_cleanup.py)"}, {"test": "test_data_optimizer_import (test_data_management.TestDataOptimization.test_data_optimizer_import)", "reason": "Data optimizer not available: cannot import name 'DataOptimizer' from 'data.data_optimizer' (/home/<USER>/rapidtrader/data/data_optimizer.py)"}, {"test": "test_group_manager_import (test_data_management.TestSymbolManagement.test_group_manager_import)", "reason": "Group manager not available: cannot import name 'GroupManager' from 'data.group_manager' (/home/<USER>/rapidtrader/data/group_manager.py)"}]}, "output": "Ran 18 tests in 0.033s\n\nFAILED (failures=2, errors=1, skipped=4)\n"}, "test_docker_integration": {"module": "test_docker_integration", "tests_run": 17, "failures": 0, "errors": 0, "skipped": 0, "success_rate": 100.0, "details": {"failures": [], "errors": [], "skipped": []}, "output": "<PERSON>n 17 tests in 0.080s\n\nOK\n"}}}