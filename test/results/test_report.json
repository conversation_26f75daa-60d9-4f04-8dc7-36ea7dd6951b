{"timestamp": "2025-05-20T12:16:45.583626", "results": [{"name": "test_broker_module", "module": "broker_module", "success": false, "message": "test_base_broker_abstract_methods (__main__.BaseBrokerTest.test_base_broker_abstract_methods)\nTest BaseBroker abstract methods. ... ok\ntest_base_broker_import (__main__.BaseBrokerTest.test_base_broker_import)\nTest BaseBroker import. ... ok\ntest_dhan_broker_get_order_status (__main__.DhanBrokerTest.test_dhan_broker_get_order_status)\nTest DhanBroker get_order_status method. ... 2025-05-20 12:16:42,895 - DhanBroker - INFO - DhanBroker initialized\nok\ntest_dhan_broker_import (__main__.DhanBrokerTest.test_dhan_broker_import)\nTest DhanBroker import. ... ok\ntest_dhan_broker_initialization (__main__.DhanBrokerTest.test_dhan_broker_initialization)\nTest DhanBroker initialization. ... 2025-05-20 12:16:42,897 - DhanBroker - INFO - DhanBroker initialized\nok\ntest_dhan_broker_place_order (__main__.DhanBrokerTest.test_dhan_broker_place_order)\nTest DhanBroker place_order method. ... 2025-05-20 12:16:42,899 - DhanBroker - INFO - DhanBroker initialized\nFAIL\ntest_enums_import (__main__.EnumsTest.test_enums_import)\nTest enums import. ... ok\ntest_enums_values (__main__.EnumsTest.test_enums_values)\nTest enums values. ... FAIL\n\n======================================================================\nFAIL: test_dhan_broker_place_order (__main__.DhanBrokerTest.test_dhan_broker_place_order)\nTest DhanBroker place_order method.\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_broker_module.py\", line 137, in test_dhan_broker_place_order\n    mock_place_order.assert_called_once_with(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        symbol=\"SBIN-EQ\",\n        ^^^^^^^^^^^^^^^^^\n    ...<6 lines>...\n        trigger_price=None\n        ^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"/usr/lib/python3.13/unittest/mock.py\", line 989, in assert_called_once_with\n    return self.assert_called_with(*args, **kwargs)\n           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^\n  File \"/usr/lib/python3.13/unittest/mock.py\", line 977, in assert_called_with\n    raise AssertionError(_error_message()) from cause\nAssertionError: expected call not found.\nExpected: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>, price=None, trigger_price=None)\n  Actual: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/usr/lib/python3.13/unittest/mock.py\", line 1424, in patched\n    return func(*newargs, **newkeywargs)\n  File \"/home/<USER>/rapidtrader/test/test_broker_module.py\", line 148, in test_dhan_broker_place_order\n    self.fail(f\"DhanBroker place_order test failed: {e}\")\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: DhanBroker place_order test failed: expected call not found.\nExpected: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>, price=None, trigger_price=None)\n  Actual: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>)\n\n======================================================================\nFAIL: test_enums_values (__main__.EnumsTest.test_enums_values)\nTest enums values.\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_broker_module.py\", line 219, in test_enums_values\n    self.assertEqual(ProductType.INTRADAY.value, \"INTRADAY\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: 'I' != 'INTRADAY'\n- I\n+ INTRADAY\n\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_broker_module.py\", line 230, in test_enums_values\n    self.fail(f\"Enums values test failed: {e}\")\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Enums values test failed: 'I' != 'INTRADAY'\n- I\n+ INTRADAY\n\n\n----------------------------------------------------------------------\nRan 8 tests in 0.140s\n\nFAILED (failures=2)\n", "duration": 0.0, "timestamp": "2025-05-20T12:16:43.071970"}, {"name": "test_rapidtrader", "module": "rapidtrader", "success": false, "message": "test_data_fetcher_initialization (__main__.DataModuleTest.test_data_fetcher_initialization)\nTest data fetcher initialization. ... 2025-05-20 12:16:44,042 - fetcher - INFO - Using enhanced YFinance fetcher with symbol manager integration\nok\ntest_data_manager (__main__.DataModuleTest.test_data_manager)\nTest data manager. ... ok\ntest_yfinance_fetcher (__main__.DataModuleTest.test_yfinance_fetcher)\nTest YFinance fetcher. ... ok\ntest_broker_initialization (__main__.BrokerModuleTest.test_broker_initialization)\nTest broker initialization. ... ok\ntest_core_initialization (__main__.CoreModuleTest.test_core_initialization)\nTest core module initialization. ... FAIL\ntest_cli_initialization (__main__.CLITest.test_cli_initialization)\nTest CLI initialization. ... ok\n\n======================================================================\nFAIL: test_core_initialization (__main__.CoreModuleTest.test_core_initialization)\nTest core module initialization.\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_rapidtrader.py\", line 237, in test_core_initialization\n    import core\n  File \"/home/<USER>/rapidtrader/core/__init__.py\", line 7, in <module>\n    from core.symbol_manager import SymbolManager\nModuleNotFoundError: No module named 'core.symbol_manager'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_rapidtrader.py\", line 243, in test_core_initialization\n    self.fail(f\"Core module initialization failed: {e}\")\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: Core module initialization failed: No module named 'core.symbol_manager'\n\n----------------------------------------------------------------------\nRan 6 tests in 0.530s\n\nFAILED (failures=1)\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_rapidtrader.py\", line 391, in <module>\n    sys.exit(main())\n             ~~~~^^\n  File \"/home/<USER>/rapidtrader/test/test_rapidtrader.py\", line 349, in main\n    results = run_tests(modules)\n  File \"/home/<USER>/rapidtrader/test/test_rapidtrader.py\", line 309, in run_tests\n    for test in test_result.successes:\n                ^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'TextTestResult' object has no attribute 'successes'\n", "duration": 0.0, "timestamp": "2025-05-20T12:16:44.358722"}, {"name": "test_data_module", "module": "data_module", "success": false, "message": "test_base_fetcher_abstract_methods (__main__.BaseDataFetcherTest.test_base_fetcher_abstract_methods)\nTest BaseDataFetcher abstract methods. ... FAIL\ntest_base_fetcher_initialization (__main__.BaseDataFetcherTest.test_base_fetcher_initialization)\nTest BaseDataFetcher initialization. ... FAIL\ntest_yfinance_fetcher_initialization (__main__.YFinanceFetcherTest.test_yfinance_fetcher_initialization)\nTest YFinanceFetcher initialization. ... ok\ntest_yfinance_fetcher_timeframes (__main__.YFinanceFetcherTest.test_yfinance_fetcher_timeframes)\nTest YFinanceFetcher timeframes. ... ok\ntest_data_manager_initialization (__main__.DataManagerTest.test_data_manager_initialization)\nTest DataManager initialization. ... ok\ntest_download_data_functions (__main__.DownloadDataTest.test_download_data_functions)\nTest download_data module functions. ... FAIL\ntest_download_data_import (__main__.DownloadDataTest.test_download_data_import)\nTest download_data module import. ... ok\ntest_fetcher_initialization (__main__.FetcherTest.test_fetcher_initialization)\nTest Fetcher initialization. ... 2025-05-20 12:16:45,384 - fetcher - INFO - Using enhanced YFinance fetcher with symbol manager integration\nok\ntest_fetcher_sources (__main__.FetcherTest.test_fetcher_sources)\nTest Fetcher sources. ... 2025-05-20 12:16:45,385 - fetcher - INFO - Using enhanced YFinance fetcher with symbol manager integration\nok\ntest_data_cleanup_import (__main__.DataCleanupTest.test_data_cleanup_import)\nTest data_cleanup module import. ... ok\ntest_data_optimizer_import (__main__.DataOptimizerTest.test_data_optimizer_import)\nTest data_optimizer module import. ... ok\n\n======================================================================\nFAIL: test_base_fetcher_abstract_methods (__main__.BaseDataFetcherTest.test_base_fetcher_abstract_methods)\nTest BaseDataFetcher abstract methods.\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_module.py\", line 59, in test_base_fetcher_abstract_methods\n    fetcher = BaseDataFetcher()\nTypeError: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_module.py\", line 74, in test_base_fetcher_abstract_methods\n    self.fail(f\"BaseDataFetcher abstract methods test failed: {e}\")\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: BaseDataFetcher abstract methods test failed: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'\n\n======================================================================\nFAIL: test_base_fetcher_initialization (__main__.BaseDataFetcherTest.test_base_fetcher_initialization)\nTest BaseDataFetcher initialization.\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_module.py\", line 49, in test_base_fetcher_initialization\n    fetcher = BaseDataFetcher()\nTypeError: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_module.py\", line 52, in test_base_fetcher_initialization\n    self.fail(f\"BaseDataFetcher initialization failed: {e}\")\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: BaseDataFetcher initialization failed: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'\n\n======================================================================\nFAIL: test_download_data_functions (__main__.DownloadDataTest.test_download_data_functions)\nTest download_data module functions.\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_module.py\", line 181, in test_download_data_functions\n    self.assertEqual(validate_timeframe(\"1m\"), \"1m\")\n    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: '1mo' != '1m'\n- 1mo\n?   -\n+ 1m\n\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/home/<USER>/rapidtrader/test/test_data_module.py\", line 189, in test_download_data_functions\n    self.fail(f\"download_data functions test failed: {e}\")\n    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nAssertionError: download_data functions test failed: '1mo' != '1m'\n- 1mo\n?   -\n+ 1m\n\n\n----------------------------------------------------------------------\nRan 11 tests in 0.372s\n\nFAILED (failures=3)\n", "duration": 0.0, "timestamp": "2025-05-20T12:16:45.580732"}]}