
    <!DOCTYPE html>
    <html>
    <head>
        <title>RapidTrader Test Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; }
            .summary { margin-bottom: 20px; }
            .module { margin-bottom: 30px; }
            .module-header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
            .test-table { width: 100%; border-collapse: collapse; }
            .test-table th, .test-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .test-table th { background-color: #f0f0f0; }
            .test-row-pass { background-color: #dff0d8; }
            .test-row-fail { background-color: #f2dede; }
            .progress-bar { width: 100%; background-color: #f0f0f0; border-radius: 5px; }
            .progress { height: 20px; background-color: #4CAF50; border-radius: 5px; text-align: center; color: white; }
        </style>
    </head>
    <body>
        <h1>RapidTrader Test Report</h1>
        <div class="summary">
            <h2>Summary</h2>
            <p>Generated on: 2025-05-20 12:16:45</p>
            <p>Total Tests: 3</p>
            <p>Passed: 0</p>
            <p>Failed: 3</p>
            <div class="progress-bar">
                <div class="progress" style="width: 0.0%;">0.0%</div>
            </div>
        </div>
    
        <div class="module">
            <h2 class="module-header">broker_module (0/1)</h2>
            <div class="progress-bar">
                <div class="progress" style="width: 0.0%;">0.0%</div>
            </div>
            <table class="test-table">
                <tr>
                    <th>Test</th>
                    <th>Status</th>
                    <th>Duration (s)</th>
                    <th>Message</th>
                </tr>
        
                <tr class="test-row-fail">
                    <td>test_broker_module</td>
                    <td>Fail</td>
                    <td>0.000</td>
                    <td>test_base_broker_abstract_methods (__main__.BaseBrokerTest.test_base_broker_abstract_methods)
Test BaseBroker abstract methods. ... ok
test_base_broker_import (__main__.BaseBrokerTest.test_base_broker_import)
Test BaseBroker import. ... ok
test_dhan_broker_get_order_status (__main__.DhanBrokerTest.test_dhan_broker_get_order_status)
Test DhanBroker get_order_status method. ... 2025-05-20 12:16:42,895 - DhanBroker - INFO - DhanBroker initialized
ok
test_dhan_broker_import (__main__.DhanBrokerTest.test_dhan_broker_import)
Test DhanBroker import. ... ok
test_dhan_broker_initialization (__main__.DhanBrokerTest.test_dhan_broker_initialization)
Test DhanBroker initialization. ... 2025-05-20 12:16:42,897 - DhanBroker - INFO - DhanBroker initialized
ok
test_dhan_broker_place_order (__main__.DhanBrokerTest.test_dhan_broker_place_order)
Test DhanBroker place_order method. ... 2025-05-20 12:16:42,899 - DhanBroker - INFO - DhanBroker initialized
FAIL
test_enums_import (__main__.EnumsTest.test_enums_import)
Test enums import. ... ok
test_enums_values (__main__.EnumsTest.test_enums_values)
Test enums values. ... FAIL

======================================================================
FAIL: test_dhan_broker_place_order (__main__.DhanBrokerTest.test_dhan_broker_place_order)
Test DhanBroker place_order method.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_broker_module.py", line 137, in test_dhan_broker_place_order
    mock_place_order.assert_called_once_with(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        symbol="SBIN-EQ",
        ^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        trigger_price=None
        ^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/usr/lib/python3.13/unittest/mock.py", line 989, in assert_called_once_with
    return self.assert_called_with(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.13/unittest/mock.py", line 977, in assert_called_with
    raise AssertionError(_error_message()) from cause
AssertionError: expected call not found.
Expected: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>, price=None, trigger_price=None)
  Actual: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/lib/python3.13/unittest/mock.py", line 1424, in patched
    return func(*newargs, **newkeywargs)
  File "/home/<USER>/rapidtrader/test/test_broker_module.py", line 148, in test_dhan_broker_place_order
    self.fail(f"DhanBroker place_order test failed: {e}")
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: DhanBroker place_order test failed: expected call not found.
Expected: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>, price=None, trigger_price=None)
  Actual: place_order(symbol='SBIN-EQ', exchange=<Exchange.NSE: 'NSE'>, quantity=1, side=<OrderSide.BUY: 'BUY'>, order_type=<OrderType.MARKET: 'MARKET'>, product_type=<ProductType.INTRADAY: 'I'>)

======================================================================
FAIL: test_enums_values (__main__.EnumsTest.test_enums_values)
Test enums values.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_broker_module.py", line 219, in test_enums_values
    self.assertEqual(ProductType.INTRADAY.value, "INTRADAY")
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: 'I' != 'INTRADAY'
- I
+ INTRADAY


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_broker_module.py", line 230, in test_enums_values
    self.fail(f"Enums values test failed: {e}")
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: Enums values test failed: 'I' != 'INTRADAY'
- I
+ INTRADAY


----------------------------------------------------------------------
Ran 8 tests in 0.140s

FAILED (failures=2)
</td>
                </tr>
            
            </table>
        </div>
        
        <div class="module">
            <h2 class="module-header">rapidtrader (0/1)</h2>
            <div class="progress-bar">
                <div class="progress" style="width: 0.0%;">0.0%</div>
            </div>
            <table class="test-table">
                <tr>
                    <th>Test</th>
                    <th>Status</th>
                    <th>Duration (s)</th>
                    <th>Message</th>
                </tr>
        
                <tr class="test-row-fail">
                    <td>test_rapidtrader</td>
                    <td>Fail</td>
                    <td>0.000</td>
                    <td>test_data_fetcher_initialization (__main__.DataModuleTest.test_data_fetcher_initialization)
Test data fetcher initialization. ... 2025-05-20 12:16:44,042 - fetcher - INFO - Using enhanced YFinance fetcher with symbol manager integration
ok
test_data_manager (__main__.DataModuleTest.test_data_manager)
Test data manager. ... ok
test_yfinance_fetcher (__main__.DataModuleTest.test_yfinance_fetcher)
Test YFinance fetcher. ... ok
test_broker_initialization (__main__.BrokerModuleTest.test_broker_initialization)
Test broker initialization. ... ok
test_core_initialization (__main__.CoreModuleTest.test_core_initialization)
Test core module initialization. ... FAIL
test_cli_initialization (__main__.CLITest.test_cli_initialization)
Test CLI initialization. ... ok

======================================================================
FAIL: test_core_initialization (__main__.CoreModuleTest.test_core_initialization)
Test core module initialization.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_rapidtrader.py", line 237, in test_core_initialization
    import core
  File "/home/<USER>/rapidtrader/core/__init__.py", line 7, in <module>
    from core.symbol_manager import SymbolManager
ModuleNotFoundError: No module named 'core.symbol_manager'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_rapidtrader.py", line 243, in test_core_initialization
    self.fail(f"Core module initialization failed: {e}")
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: Core module initialization failed: No module named 'core.symbol_manager'

----------------------------------------------------------------------
Ran 6 tests in 0.530s

FAILED (failures=1)
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_rapidtrader.py", line 391, in <module>
    sys.exit(main())
             ~~~~^^
  File "/home/<USER>/rapidtrader/test/test_rapidtrader.py", line 349, in main
    results = run_tests(modules)
  File "/home/<USER>/rapidtrader/test/test_rapidtrader.py", line 309, in run_tests
    for test in test_result.successes:
                ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TextTestResult' object has no attribute 'successes'
</td>
                </tr>
            
            </table>
        </div>
        
        <div class="module">
            <h2 class="module-header">data_module (0/1)</h2>
            <div class="progress-bar">
                <div class="progress" style="width: 0.0%;">0.0%</div>
            </div>
            <table class="test-table">
                <tr>
                    <th>Test</th>
                    <th>Status</th>
                    <th>Duration (s)</th>
                    <th>Message</th>
                </tr>
        
                <tr class="test-row-fail">
                    <td>test_data_module</td>
                    <td>Fail</td>
                    <td>0.000</td>
                    <td>test_base_fetcher_abstract_methods (__main__.BaseDataFetcherTest.test_base_fetcher_abstract_methods)
Test BaseDataFetcher abstract methods. ... FAIL
test_base_fetcher_initialization (__main__.BaseDataFetcherTest.test_base_fetcher_initialization)
Test BaseDataFetcher initialization. ... FAIL
test_yfinance_fetcher_initialization (__main__.YFinanceFetcherTest.test_yfinance_fetcher_initialization)
Test YFinanceFetcher initialization. ... ok
test_yfinance_fetcher_timeframes (__main__.YFinanceFetcherTest.test_yfinance_fetcher_timeframes)
Test YFinanceFetcher timeframes. ... ok
test_data_manager_initialization (__main__.DataManagerTest.test_data_manager_initialization)
Test DataManager initialization. ... ok
test_download_data_functions (__main__.DownloadDataTest.test_download_data_functions)
Test download_data module functions. ... FAIL
test_download_data_import (__main__.DownloadDataTest.test_download_data_import)
Test download_data module import. ... ok
test_fetcher_initialization (__main__.FetcherTest.test_fetcher_initialization)
Test Fetcher initialization. ... 2025-05-20 12:16:45,384 - fetcher - INFO - Using enhanced YFinance fetcher with symbol manager integration
ok
test_fetcher_sources (__main__.FetcherTest.test_fetcher_sources)
Test Fetcher sources. ... 2025-05-20 12:16:45,385 - fetcher - INFO - Using enhanced YFinance fetcher with symbol manager integration
ok
test_data_cleanup_import (__main__.DataCleanupTest.test_data_cleanup_import)
Test data_cleanup module import. ... ok
test_data_optimizer_import (__main__.DataOptimizerTest.test_data_optimizer_import)
Test data_optimizer module import. ... ok

======================================================================
FAIL: test_base_fetcher_abstract_methods (__main__.BaseDataFetcherTest.test_base_fetcher_abstract_methods)
Test BaseDataFetcher abstract methods.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_data_module.py", line 59, in test_base_fetcher_abstract_methods
    fetcher = BaseDataFetcher()
TypeError: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_data_module.py", line 74, in test_base_fetcher_abstract_methods
    self.fail(f"BaseDataFetcher abstract methods test failed: {e}")
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: BaseDataFetcher abstract methods test failed: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'

======================================================================
FAIL: test_base_fetcher_initialization (__main__.BaseDataFetcherTest.test_base_fetcher_initialization)
Test BaseDataFetcher initialization.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_data_module.py", line 49, in test_base_fetcher_initialization
    fetcher = BaseDataFetcher()
TypeError: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_data_module.py", line 52, in test_base_fetcher_initialization
    self.fail(f"BaseDataFetcher initialization failed: {e}")
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: BaseDataFetcher initialization failed: Can't instantiate abstract class BaseDataFetcher without an implementation for abstract methods 'fetch_multiple_ohlcv', 'fetch_ohlcv', 'get_available_timeframes', 'get_exchange_info'

======================================================================
FAIL: test_download_data_functions (__main__.DownloadDataTest.test_download_data_functions)
Test download_data module functions.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_data_module.py", line 181, in test_download_data_functions
    self.assertEqual(validate_timeframe("1m"), "1m")
    ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: '1mo' != '1m'
- 1mo
?   -
+ 1m


During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/rapidtrader/test/test_data_module.py", line 189, in test_download_data_functions
    self.fail(f"download_data functions test failed: {e}")
    ~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AssertionError: download_data functions test failed: '1mo' != '1m'
- 1mo
?   -
+ 1m


----------------------------------------------------------------------
Ran 11 tests in 0.372s

FAILED (failures=3)
</td>
                </tr>
            
            </table>
        </div>
        
    </body>
    </html>
    