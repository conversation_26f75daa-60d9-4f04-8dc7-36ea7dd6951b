
<!DOCTYPE html>
<html>
<head>
    <title>RapidTrader Comprehensive Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .module { margin: 20px 0; border: 1px solid #ddd; border-radius: 5px; }
        .module-header { background-color: #e9e9e9; padding: 10px; font-weight: bold; }
        .module-content { padding: 10px; }
        .pass { color: green; }
        .fail { color: red; }
        .skip { color: orange; }
        .error { color: red; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 RapidTrader Comprehensive Test Report</h1>
        <p>Generated on: 2025-05-30T22:08:47.375264</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Total Modules</td><td>7</td></tr>
            <tr><td>Total Tests</td><td>99</td></tr>
            <tr><td>Passed</td><td class="pass">81</td></tr>
            <tr><td>Failed</td><td class="fail">7</td></tr>
            <tr><td>Errors</td><td class="error">11</td></tr>
            <tr><td>Skipped</td><td class="skip">16</td></tr>
            <tr><td>Success Rate</td><td>81.8%</td></tr>
        </table>
    </div>
    
    <h2>Module Details</h2>

    <div class="module">
        <div class="module-header">test_core_initialization</div>
        <div class="module-content">
            <p>Tests: 8, 
               Passed: <span class="pass">7</span>, 
               Failed: <span class="fail">1</span>, 
               Errors: <span class="error">0</span>, 
               Skipped: <span class="skip">0</span>
               (Success Rate: 87.5%)</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header">test_broker_integrations</div>
        <div class="module-content">
            <p>Tests: 15, 
               Passed: <span class="pass">13</span>, 
               Failed: <span class="fail">2</span>, 
               Errors: <span class="error">0</span>, 
               Skipped: <span class="skip">2</span>
               (Success Rate: 86.7%)</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header">test_backtesting_engine</div>
        <div class="module-content">
            <p>Tests: 13, 
               Passed: <span class="pass">7</span>, 
               Failed: <span class="fail">0</span>, 
               Errors: <span class="error">6</span>, 
               Skipped: <span class="skip">0</span>
               (Success Rate: 53.8%)</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header">test_api_gateway_core</div>
        <div class="module-content">
            <p>Tests: 13, 
               Passed: <span class="pass">11</span>, 
               Failed: <span class="fail">1</span>, 
               Errors: <span class="error">1</span>, 
               Skipped: <span class="skip">9</span>
               (Success Rate: 84.6%)</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header">test_money_management</div>
        <div class="module-content">
            <p>Tests: 15, 
               Passed: <span class="pass">11</span>, 
               Failed: <span class="fail">1</span>, 
               Errors: <span class="error">3</span>, 
               Skipped: <span class="skip">1</span>
               (Success Rate: 73.3%)</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header">test_data_management</div>
        <div class="module-content">
            <p>Tests: 18, 
               Passed: <span class="pass">15</span>, 
               Failed: <span class="fail">2</span>, 
               Errors: <span class="error">1</span>, 
               Skipped: <span class="skip">4</span>
               (Success Rate: 83.3%)</p>
        </div>
    </div>

    <div class="module">
        <div class="module-header">test_docker_integration</div>
        <div class="module-content">
            <p>Tests: 17, 
               Passed: <span class="pass">17</span>, 
               Failed: <span class="fail">0</span>, 
               Errors: <span class="error">0</span>, 
               Skipped: <span class="skip">0</span>
               (Success Rate: 100.0%)</p>
        </div>
    </div>

</body>
</html>
